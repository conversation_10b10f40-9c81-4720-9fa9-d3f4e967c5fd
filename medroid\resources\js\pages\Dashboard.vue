<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { ref, computed, onMounted } from 'vue';
import axios from 'axios';

const props = defineProps({
  stats: {
    type: Object,
    default: () => ({
      upcomingAppointments: 0,
      completedAppointments: 0,
      healthRecords: 0,
      totalPatients: 0,
      monthlyEarnings: 0,
      completionRate: 0
    })
  },
  recentAppointments: {
    type: Array,
    default: () => []
  },
  quickActions: {
    type: Array,
    default: () => []
  },
  kpiData: {
    type: Object,
    default: () => ({})
  },
  user: {
    type: Object,
    default: () => null
  },
  providerData: {
    type: Object,
    default: () => ({
      weeklyAvailability: [],
      nextAppointments: [],
      earnings: {},
      patientCount: 0
    })
  }
});

const breadcrumbs = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

// Reactive data
const loading = ref(false);
const lastUpdated = ref('Never');
const selectedRole = ref('admin');
const error = ref(null);

const kpiData = ref({
  // User metrics
  total_users: 0,
  monthly_active_users: 0,
  daily_active_users: 0,
  user_growth_rate: 0,

  // Appointment metrics
  total_appointments: 0,
  monthly_appointments: 0,
  daily_appointments: 0,
  appointment_completion_rate: 0,

  // Consult metrics
  total_consults: 0,
  monthly_consults: 0,
  daily_consults: 0,

  // Conversion metrics
  consult_to_appointment_ratio: 0,
  repeat_consult_users: 0,

  // Diagnostic metrics
  diagnostic_accuracy: 0,
  diagnostic_feedback_count: 0,
  top_accurate_diagnoses: [],
  top_inaccurate_diagnoses: [],

  // Location data
  user_locations: [],

  // Revenue metrics
  total_revenue: 0,
  monthly_revenue: 0,
  average_appointment_value: 0,

  // Clinic metrics
  total_clinics: 0,
  active_clinics: 0,
  clinics_accepting_patients: 0,
  telemedicine_clinics: 0,
  clinic_activation_rate: 0,
  telemedicine_adoption_rate: 0,
  clinics_by_state: [],
  top_clinics_by_providers: [],
  top_clinics_by_patients: [],
  ...props.kpiData
});

// Computed properties
const isAdmin = computed(() => {
  return props.user?.roles?.some(role => role.name === 'admin') || false;
});

const isProvider = computed(() => {
  return props.user?.roles?.some(role => role.name === 'provider') || false;
});

const isPatient = computed(() => {
  return props.user?.roles?.some(role => role.name === 'patient') || false;
});

// Methods
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getStatusClass = (status) => {
  const classes = {
    'scheduled': 'bg-blue-100 text-blue-800',
    'confirmed': 'bg-green-100 text-green-800',
    'in_progress': 'bg-yellow-100 text-yellow-800',
    'completed': 'bg-gray-100 text-gray-800',
    'cancelled': 'bg-red-100 text-red-800'
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
};

const getDashboardTitle = () => {
  if (isProvider.value) return 'Provider Dashboard';
  if (isPatient.value) return 'Patient Dashboard';
  if (isAdmin.value) {
    const titles = {
      admin: 'Admin Dashboard',
      provider: 'Provider Dashboard',
      patient: 'Patient Dashboard',
      care_manager: 'Care Manager Dashboard'
    };
    return titles[selectedRole.value] || 'Admin Dashboard';
  }
  return 'Dashboard';
};

const getDashboardSubtitle = () => {
  if (isProvider.value) return 'Manage your patients, availability, and appointments';
  if (isPatient.value) return 'Track your health and appointments';
  if (isAdmin.value) {
    const subtitles = {
      admin: 'Manage your healthcare platform from one place',
      provider: 'Manage your patients and appointments',
      patient: 'Track your health and appointments',
      care_manager: 'Monitor patient care and compliance'
    };
    return subtitles[selectedRole.value] || 'Manage your healthcare platform';
  }
  return 'Manage your healthcare services';
};

const calculatePercentage = (value, total) => {
  if (!total) return 0;
  return Math.round((value / total) * 100);
};

const calculateLocationPercentage = (count) => {
  if (!kpiData.value.user_locations || kpiData.value.user_locations.length === 0) return 0;

  const maxCount = Math.max(...kpiData.value.user_locations.map(loc => loc.count));
  return Math.round((count / maxCount) * 100);
};

const formatCurrency = (value) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(value);
};

const formatNumber = (value) => {
  return new Intl.NumberFormat('en-US').format(value);
};

const onRoleChange = () => {
  fetchDashboardData(true);
};

const fetchDashboardData = async (refresh = false) => {
  loading.value = true;
  error.value = null;

  try {
    const response = await axios.get('/management/dashboard/kpi', {
      params: { refresh },
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    kpiData.value = {
      ...kpiData.value,
      ...response.data
    };

    lastUpdated.value = new Date().toLocaleString();
  } catch (err) {
    console.error('Error fetching KPI data:', err);
    error.value = 'Failed to load dashboard data. Please try again.';
  } finally {
    loading.value = false;
  }
};

// Provider-specific methods
const fetchProviderData = async () => {
  if (!isProvider.value) return;

  loading.value = true;
  try {
    // Get CSRF token from meta tag
    const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    const response = await axios.get('/provider/get-dashboard-data', {
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-TOKEN': token || '',
      },
      withCredentials: true
    });

    if (response.data) {
      // Update stats with provider data
      Object.assign(props.stats, response.data.stats);

      // Update recent appointments if available
      if (response.data.todayAppointments) {
        props.recentAppointments.splice(0, props.recentAppointments.length, ...response.data.todayAppointments);
      }
    }
  } catch (err) {
    console.error('Error fetching provider data:', err);
    // Don't show error to user if they're not actually a provider
    if (err.response?.status === 401 || err.response?.status === 403) {
      console.warn('User does not have provider access - this is expected for non-provider users');
    }
  } finally {
    loading.value = false;
  }
};

const getProviderQuickActions = () => {
  return [
    {
      title: 'Manage Availability',
      href: '/provider/availability',
      icon: 'calendar-alt',
      description: 'Set your working hours and time slots',
      color: 'blue'
    },
    {
      title: 'View Appointments',
      href: '/appointments',
      icon: 'calendar-check',
      description: 'See your upcoming appointments',
      color: 'green'
    },
    {
      title: 'Patient List',
      href: '/provider/patients',
      icon: 'users',
      description: 'Manage your patient records',
      color: 'purple'
    },
    {
      title: 'Earnings',
      href: '/provider/earnings',
      icon: 'dollar-sign',
      description: 'Track your earnings and payments',
      color: 'yellow'
    }
  ];
};

// Initialize on mount
onMounted(() => {
  if (props.user) {
    selectedRole.value = props.user.role || 'admin';
  }

  if (isProvider.value) {
    fetchProviderData();
  } else if (isAdmin.value) {
    fetchDashboardData();
  }
});
</script>

<template>
    <Head title="Dashboard" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="p-6">
            <div class="dashboard-container">
                <!-- Header Section -->
                <div class="dashboard-header flex items-center justify-between">
                    <div class="header-content">
                        <h1 class="dashboard-title">{{ getDashboardTitle() }}</h1>
                        <p class="dashboard-subtitle">{{ getDashboardSubtitle() }}</p>
                    </div>
                    <div class="header-actions">
                        <!-- Role Selector for Admin -->
                        <select
                            v-if="isAdmin"
                            v-model="selectedRole"
                            @change="onRoleChange"
                            class="role-selector"
                        >
                            <option value="admin">Admin View</option>
                            <option value="provider">Provider View</option>
                            <option value="patient">Patient View</option>
                            <option value="care_manager">Care Manager View</option>
                        </select>

                        <!-- Refresh Button -->
                        <button
                            v-if="isAdmin"
                            @click="fetchDashboardData(true)"
                            class="refresh-btn"
                            :disabled="loading"
                        >
                            <i v-if="loading" class="fas fa-spinner fa-spin"></i>
                            <i v-else class="fas fa-sync-alt"></i>
                            <span class="ml-2">{{ loading ? 'Refreshing...' : 'Refresh Data' }}</span>
                        </button>
                    </div>
                </div>

                <!-- Provider Stats Section -->
                <div v-if="isProvider" class="">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">Provider Overview</h3>

                    <!-- Provider KPIs -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div class="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500">
                            <div class="flex justify-between">
                                <div>
                                    <p class="text-sm text-gray-500">Total Patients</p>
                                    <p class="text-2xl font-bold text-blue-600">{{ stats.totalPatients || 0 }}</p>
                                    <p class="text-xs text-gray-400">Active patients</p>
                                </div>
                                <div class="text-blue-500">
                                    <i class="fas fa-users text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-green-50 rounded-lg p-4 border-l-4 border-green-500">
                            <div class="flex justify-between">
                                <div>
                                    <p class="text-sm text-gray-500">Upcoming Appointments</p>
                                    <p class="text-2xl font-bold text-green-600">{{ stats.upcomingAppointments || 0 }}</p>
                                    <p class="text-xs text-gray-400">Next 7 days</p>
                                </div>
                                <div class="text-green-500">
                                    <i class="fas fa-calendar-check text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-purple-50 rounded-lg p-4 border-l-4 border-purple-500">
                            <div class="flex justify-between">
                                <div>
                                    <p class="text-sm text-gray-500">Completion Rate</p>
                                    <p class="text-2xl font-bold text-purple-600">{{ stats.completionRate || 0 }}%</p>
                                    <p class="text-xs text-gray-400">This month</p>
                                </div>
                                <div class="text-purple-500">
                                    <i class="fas fa-chart-line text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-yellow-50 rounded-lg p-4 border-l-4 border-yellow-500">
                            <div class="flex justify-between">
                                <div>
                                    <p class="text-sm text-gray-500">Monthly Earnings</p>
                                    <p class="text-2xl font-bold text-yellow-600">${{ stats.monthlyEarnings || 0 }}</p>
                                    <p class="text-xs text-gray-400">Current month</p>
                                </div>
                                <div class="text-yellow-500">
                                    <i class="fas fa-dollar-sign text-2xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Comprehensive KPI Dashboard for Admin -->
                <div v-if="isAdmin" class="">
                    <!-- Loading State -->
                    <div v-if="loading" class="bg-white rounded-lg shadow p-6 mb-6">
                        <div class="flex items-center justify-center py-8">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                            <span class="ml-3 text-gray-600">Loading KPI data...</span>
                        </div>
                    </div>

                    <!-- Error State -->
                    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Error Loading Dashboard</h3>
                                <p class="mt-1 text-sm text-red-700">{{ error }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Main KPI Cards -->
                    <div v-else class="kpi-grid grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
                        <!-- User Metrics -->
                        <div class="bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl shadow-lg p-6 border border-blue-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <div class="p-2 bg-blue-500 rounded-lg mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                            </svg>
                                        </div>
                                        <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Total Users</h3>
                                    </div>
                                    <p class="text-3xl font-bold text-gray-800 mb-2">{{ formatNumber(kpiData.total_users || 0) }}</p>
                                    <div class="flex items-center">
                                        <span class="text-sm text-green-600 flex items-center font-medium" v-if="kpiData.user_growth_rate > 0">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                            </svg>
                                            +{{ kpiData.user_growth_rate || 0 }}%
                                        </span>
                                        <span class="text-sm text-gray-500 ml-2">this month</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Appointment Metrics -->
                        <div class="bg-gradient-to-br from-green-100 to-green-200 rounded-xl shadow-lg p-6 border border-green-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <div class="p-2 bg-green-500 rounded-lg mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                        <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Appointments</h3>
                                    </div>
                                    <p class="text-3xl font-bold text-gray-800 mb-2">{{ formatNumber(kpiData.total_appointments || 0) }}</p>
                                    <div class="flex items-center">
                                        <span class="text-sm text-gray-600 font-medium">{{ formatNumber(kpiData.monthly_appointments || 0) }}</span>
                                        <span class="text-sm text-gray-500 ml-2">this month</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Revenue Metrics -->
                        <div class="bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-xl shadow-lg p-6 border border-yellow-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <div class="p-2 bg-yellow-500 rounded-lg mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Total Revenue</h3>
                                    </div>
                                    <p class="text-3xl font-bold text-gray-800 mb-2">{{ formatCurrency(kpiData.total_revenue || 0) }}</p>
                                    <div class="flex items-center">
                                        <span class="text-sm text-gray-600 font-medium">{{ formatCurrency(kpiData.monthly_revenue || 0) }}</span>
                                        <span class="text-sm text-gray-500 ml-2">this month</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Clinic Metrics -->
                        <div class="bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-xl shadow-lg p-6 border border-indigo-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <div class="p-2 bg-indigo-500 rounded-lg mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                            </svg>
                                        </div>
                                        <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Total Clinics</h3>
                                    </div>
                                    <p class="text-3xl font-bold text-gray-800 mb-2">{{ formatNumber(kpiData.total_clinics || 0) }}</p>
                                    <div class="flex items-center">
                                        <span class="text-sm text-gray-600 font-medium">{{ formatNumber(kpiData.active_clinics || 0) }}</span>
                                        <span class="text-sm text-gray-500 ml-2">active</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- AI Accuracy -->
                        <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl shadow-lg p-6 border border-purple-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <div class="p-2 bg-purple-500 rounded-lg mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                            </svg>
                                        </div>
                                        <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wide">AI Accuracy</h3>
                                    </div>
                                    <p class="text-3xl font-bold text-gray-800 mb-2">{{ kpiData.diagnostic_accuracy || 0 }}%</p>
                                    <div class="flex items-center">
                                        <span class="text-sm text-gray-600 font-medium">{{ formatNumber(kpiData.diagnostic_feedback_count || 0) }}</span>
                                        <span class="text-sm text-gray-500 ml-2">feedbacks</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Secondary Metrics Row -->
                    <div v-if="!loading && !error" class="secondary-metrics grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                        <!-- Active Users -->
                        <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-indigo-100 rounded-lg mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-700">User Activity</h4>
                            </div>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                                    <span class="text-sm text-gray-600 font-medium">Daily Active</span>
                                    <span class="text-lg font-bold text-gray-800">{{ formatNumber(kpiData.daily_active_users || 0) }}</span>
                                </div>
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-sm text-gray-600 font-medium">Monthly Active</span>
                                    <span class="text-lg font-bold text-gray-800">{{ formatNumber(kpiData.monthly_active_users || 0) }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Appointment Stats -->
                        <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-emerald-100 rounded-lg mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                    </svg>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-700">Appointment Stats</h4>
                            </div>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                                    <span class="text-sm text-gray-600 font-medium">Completion Rate</span>
                                    <span class="text-lg font-bold text-emerald-600">{{ kpiData.appointment_completion_rate || 0 }}%</span>
                                </div>
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-sm text-gray-600 font-medium">Daily Appointments</span>
                                    <span class="text-lg font-bold text-gray-800">{{ formatNumber(kpiData.daily_appointments || 0) }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Consult Metrics -->
                        <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-violet-100 rounded-lg mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-violet-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                    </svg>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-700">AI Consults</h4>
                            </div>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                                    <span class="text-sm text-gray-600 font-medium">Total Consults</span>
                                    <span class="text-lg font-bold text-gray-800">{{ formatNumber(kpiData.total_consults || 0) }}</span>
                                </div>
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-sm text-gray-600 font-medium">Monthly Consults</span>
                                    <span class="text-lg font-bold text-gray-800">{{ formatNumber(kpiData.monthly_consults || 0) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Location Analytics -->
                    <div v-if="!loading && !error && kpiData.user_locations && kpiData.user_locations.length > 0" class="location-analytics bg-white rounded-xl shadow-lg p-6 mb-6 border border-gray-100">
                        <div class="flex items-center mb-6">
                            <div class="p-2 bg-blue-100 rounded-lg mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-gray-800">Provider Locations</h3>
                        </div>
                        <div class="space-y-4">
                            <div v-for="location in kpiData.user_locations.slice(0, 5)" :key="location.location" class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors duration-200">
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-base font-semibold text-gray-700">{{ location.location }}</span>
                                    <span class="text-sm font-medium text-gray-600 bg-white px-3 py-1 rounded-full">{{ formatNumber(location.count) }} providers</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 ease-out" :style="{ width: calculateLocationPercentage(location.count) + '%' }"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Last Updated -->
                    <div v-if="!loading && !error && lastUpdated" class="text-center py-4 border-t border-gray-100">
                        <div class="flex items-center justify-center text-sm text-gray-500">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Last updated: {{ lastUpdated }}
                        </div>
                    </div>
                </div>

                <!-- Recent Appointments Section -->
                <div v-if="recentAppointments.length > 0" class="bg-white overflow-hidden shadow-sm rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Appointments</h3>
                        <div class="space-y-4">
                            <div
                                v-for="appointment in recentAppointments"
                                :key="appointment.id"
                                class="border border-gray-200 rounded-lg p-4"
                            >
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h4 class="font-medium text-gray-900">
                                            Dr. {{ appointment.provider?.user?.name || 'Unknown Provider' }}
                                        </h4>
                                        <p class="text-sm text-gray-600">{{ appointment.provider?.specialization || 'General Practice' }}</p>
                                        <p class="text-sm text-gray-500 mt-1">
                                            {{ formatDate(appointment.scheduled_at) }}
                                        </p>
                                    </div>
                                    <span
                                        :class="getStatusClass(appointment.status)"
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                    >
                                        {{ appointment.status }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Provider Quick Actions -->
                <div v-if="isProvider" class="bg-white overflow-hidden shadow-sm rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <Link
                                v-for="action in getProviderQuickActions()"
                                :key="action.title"
                                :href="action.href"
                                class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                            >
                                <div :class="`w-12 h-12 bg-${action.color}-100 rounded-full flex items-center justify-center mb-3`">
                                    <i :class="`fas fa-${action.icon} text-${action.color}-600 text-xl`"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-900 text-center">{{ action.title }}</span>
                                <span class="text-xs text-gray-500 text-center mt-1">{{ action.description }}</span>
                            </Link>
                        </div>
                    </div>
                </div>

                <!-- Patient/Admin Quick Actions -->
                <div v-else class="bg-white overflow-hidden shadow-sm rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <Link
                                href="/chat"
                                class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                            >
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium text-gray-900">AI Chat</span>
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
