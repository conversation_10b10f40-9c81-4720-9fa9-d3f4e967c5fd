<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\PersonalAccessToken;
use Illuminate\Auth\AuthenticationException;

class AuthenticateWithToken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  ...$abilities
     * @return mixed
     *
     * @throws \Illuminate\Auth\AuthenticationException
     */
    public function handle(Request $request, Closure $next, ...$abilities)
    {
        try {
            if (! $request->bearerToken()) {
                \Log::warning('No bearer token provided');
                throw new AuthenticationException('Unauthenticated.');
            }

            $token = PersonalAccessToken::findToken($request->bearerToken());

            if (! $token) {
                \Log::warning('Invalid token provided: ' . substr($request->bearerToken(), 0, 10) . '...');
                throw new AuthenticationException('Unauthenticated.');
            }

            // Check if token is expired (only if expires_at is set)
            if ($token->expires_at && $token->expires_at->isPast()) {
                \Log::warning('Token expired for user: ' . $token->user_id);
                throw new AuthenticationException('Token expired.');
            }

            if (! $this->hasValidToken($token)) {
                \Log::warning('Token expired or invalid');
                throw new AuthenticationException('Unauthenticated.');
            }

            // Load the user relationship
            $user = $token->user;

            if (!$user) {
                \Log::warning('Token has no associated user');
                throw new AuthenticationException('Unauthenticated.');
            }

            if (! $this->hasAbilities($token, $abilities)) {
                \Log::warning('User lacks required abilities', [
                    'user_id' => $user->id,
                    'required_abilities' => $abilities
                ]);
                throw new AuthenticationException('Unauthorized.');
            }

            // Update the last used timestamp
            $token->last_used_at = now();
            $token->save();

            // Set the authenticated user directly on the request
            $request->setUserResolver(function () use ($user) {
                return $user;
            });

            // Also set the user on the Auth facade for compatibility
            auth()->setUser($user);

            return $next($request);
        } catch (AuthenticationException $e) {
            return response()->json(['message' => $e->getMessage()], 401);
        } catch (\Exception $e) {
            \Log::error('Error in AuthenticateWithToken middleware: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['message' => 'Authentication error'], 500);
        }
    }

    /**
     * Determine if the token is valid.
     *
     * @param  \App\Models\PersonalAccessToken  $token
     * @return bool
     */
    protected function hasValidToken($token)
    {
        return ! $token->expires_at || $token->expires_at->isFuture();
    }

    /**
     * Determine if the token has the given abilities.
     *
     * @param  \App\Models\PersonalAccessToken  $token
     * @param  array  $abilities
     * @return bool
     */
    protected function hasAbilities($token, array $abilities)
    {
        if (empty($abilities)) {
            return true;
        }

        foreach ($abilities as $ability) {
            if ($token->can($ability)) {
                return true;
            }
        }

        return false;
    }
}
