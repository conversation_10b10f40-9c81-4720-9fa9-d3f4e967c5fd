import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:medroid_app/utils/app_colors.dart';

/// Unified payment form widget for handling Stripe payments
class UnifiedPaymentForm extends StatefulWidget {
  final String clientSecret;
  final String publishableKey;
  final Function(Map<String, dynamic>) onPaymentResult;
  final Map<String, dynamic>? billingDetails;

  const UnifiedPaymentForm({
    Key? key,
    required this.clientSecret,
    required this.publishableKey,
    required this.onPaymentResult,
    this.billingDetails,
  }) : super(key: key);

  @override
  State<UnifiedPaymentForm> createState() => _UnifiedPaymentFormState();
}

class _UnifiedPaymentFormState extends State<UnifiedPaymentForm> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeStripe();
  }

  Future<void> _initializeStripe() async {
    try {
      Stripe.publishableKey = widget.publishableKey;
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: widget.clientSecret,
          merchantDisplayName: 'Medroid',
          style: ThemeMode.light,
          billingDetails: widget.billingDetails != null
              ? BillingDetails(
                  name: widget.billingDetails!['name'],
                  email: widget.billingDetails!['email'],
                  phone: widget.billingDetails!['phone'],
                  address: widget.billingDetails!['address'] != null
                      ? Address(
                          city: widget.billingDetails!['address']['city'],
                          country: widget.billingDetails!['address']['country'],
                          line1: widget.billingDetails!['address']['line1'],
                          line2: widget.billingDetails!['address']['line2'],
                          postalCode: widget.billingDetails!['address']
                              ['postal_code'],
                          state: widget.billingDetails!['address']['state'],
                        )
                      : null,
                )
              : null,
        ),
      );
    } catch (e) {
      debugPrint('Error initializing Stripe: $e');
    }
  }

  Future<void> _processPayment() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await Stripe.instance.presentPaymentSheet();
      widget.onPaymentResult({'success': true}); // Success
    } catch (e) {
      debugPrint('Payment error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
      widget
          .onPaymentResult({'success': false, 'error': e.toString()}); // Failed
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Complete Payment',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.midnightNavy,
            ),
          ),
          const SizedBox(height: 24),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.tealSurge.withAlpha(26), // 10% opacity
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                  color: AppColors.tealSurge.withAlpha(51)), // 20% opacity
            ),
            child: const Column(
              children: [
                Icon(
                  Icons.security,
                  color: AppColors.tealSurge,
                  size: 32,
                ),
                SizedBox(height: 8),
                Text(
                  'Secure Payment',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.tealSurge,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'Your payment information is encrypted and secure',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.slateGrey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _processPayment,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.tealSurge,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                elevation: 2,
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Text(
                      'Pay Now',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
