<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Role;

class FixUserRoles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:user-roles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix user roles by assigning Spatie roles based on the role column';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Fixing user roles and permissions...');

        // First, ensure all required roles exist
        $this->ensureRolesExist();

        // Get all users who have a role in the role column but no Spatie roles
        $users = User::whereDoesntHave('roles')->whereNotNull('role')->get();

        if ($users->isEmpty()) {
            $this->info('✅ No users found that need role fixes.');

            // Check if all users have proper permissions
            $this->verifyPermissions();
            return 0;
        }

        $this->info('Found ' . $users->count() . ' users that need role fixes.');

        $fixed = 0;
        $skipped = 0;

        foreach ($users as $user) {
            // Check if the role exists in Spatie roles
            if (!Role::where('name', $user->role)->exists()) {
                $this->warn("Role '{$user->role}' does not exist for user {$user->email}. Skipping.");
                $skipped++;
                continue;
            }

            // Assign the Spatie role
            $user->assignRole($user->role);
            $this->info("✅ Assigned '{$user->role}' role to {$user->email}");
            $fixed++;
        }

        $this->info("\n📊 Completed fixing user roles:");
        $this->info("- Fixed: {$fixed} users");
        $this->info("- Skipped: {$skipped} users");

        // Verify permissions after fixing
        $this->verifyPermissions();

        return 0;
    }

    /**
     * Ensure all required roles exist
     */
    private function ensureRolesExist(): void
    {
        $roles = ['admin', 'manager', 'provider', 'patient'];

        foreach ($roles as $roleName) {
            if (!Role::where('name', $roleName)->exists()) {
                $this->warn("Creating missing role: {$roleName}");
                Role::create(['name' => $roleName, 'guard_name' => 'web']);
            }
        }
    }

    /**
     * Verify that permissions are working correctly
     */
    private function verifyPermissions(): void
    {
        $this->info("\n🔍 Verifying permissions...");

        $adminUser = User::where('role', 'admin')->first();
        if ($adminUser) {
            $hasAdminRole = $adminUser->hasRole('admin');
            $permissionCount = $adminUser->getAllPermissions()->count();

            $this->info("Admin user verification:");
            $this->info("- Has admin role: " . ($hasAdminRole ? '✅ Yes' : '❌ No'));
            $this->info("- Permission count: {$permissionCount}");

            if (!$hasAdminRole || $permissionCount === 0) {
                $this->error("❌ Admin permissions are not working correctly!");
                $this->info("💡 Try running: php artisan db:seed --class=RolesAndPermissionsSeeder");
            } else {
                $this->info("✅ Admin permissions are working correctly!");
            }
        }
    }
}