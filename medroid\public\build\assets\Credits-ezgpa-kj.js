import{r as n,o as G,c as F,d as a,e as l,f as $,u as J,m as K,g as X,i as e,j as C,x as Y,t as o,l as c,p as T,F as x,q as y,v as U,s as S,z as Z}from"./vendor-DkZiYBIF.js";import{_ as ee}from"./AppLayout.vue_vue_type_script_setup_true_lang-C2T_8RUB.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-fZxfkI-v.js";import"./createLucideIcon-CSZE2I1R.js";const te={class:"p-6"},se={class:"mb-6 flex justify-between items-center"},re={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},oe={class:"bg-white rounded-lg shadow p-6"},ae={class:"flex items-center"},le={class:"ml-4"},de={class:"text-2xl font-bold text-gray-900"},ne={class:"bg-white rounded-lg shadow p-6"},ie={class:"flex items-center"},ue={class:"ml-4"},ce={class:"text-2xl font-bold text-gray-900"},pe={class:"bg-white rounded-lg shadow p-6"},me={class:"flex items-center"},xe={class:"ml-4"},ge={class:"text-2xl font-bold text-gray-900"},ve={class:"bg-white rounded-lg shadow p-6"},be={class:"flex items-center"},ye={class:"ml-4"},fe={class:"text-2xl font-bold text-gray-900"},we={class:"bg-white rounded-lg shadow p-6 mb-6"},he={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},_e=["value"],ke={class:"bg-white rounded-lg shadow mb-6"},Ce={key:0,class:"p-6"},Ue={key:1,class:"p-6 text-center text-gray-500"},Me={key:2,class:"overflow-x-auto"},Ae={class:"min-w-full divide-y divide-gray-200"},Fe={class:"bg-white divide-y divide-gray-200"},Te={class:"px-6 py-4 whitespace-nowrap"},Se={class:"text-sm font-medium text-gray-900"},Be={class:"text-sm text-gray-500"},Ve={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},Ne={class:"px-6 py-4 whitespace-nowrap text-sm text-green-600"},De={class:"px-6 py-4 whitespace-nowrap text-sm text-red-600"},Ee={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},$e={class:"bg-white rounded-lg shadow"},ze={key:0,class:"p-6 text-center text-gray-500"},je={key:1,class:"overflow-x-auto"},Le={class:"min-w-full divide-y divide-gray-200"},He={class:"bg-white divide-y divide-gray-200"},Ie={class:"px-6 py-4 whitespace-nowrap"},Pe={class:"text-sm font-medium text-gray-900"},Re={class:"text-sm text-gray-500"},qe={class:"px-6 py-4 whitespace-nowrap"},Oe={class:"px-6 py-4 whitespace-nowrap"},Qe={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},We={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Ge={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Je={key:0,class:"fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm overflow-y-auto h-full w-full z-50"},Ke={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 lg:w-1/3 shadow-lg rounded-md bg-white"},Xe={class:"mt-3"},Ye={class:"relative"},Ze={key:0,class:"absolute right-3 top-3"},et={key:0,class:"mt-2 max-h-40 overflow-y-auto border border-gray-300 rounded-md bg-white shadow-lg"},tt=["onClick"],st={class:"text-sm font-medium text-gray-900"},rt={class:"text-xs text-gray-500"},ot={key:1,class:"mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md"},at={class:"text-sm font-medium text-blue-900"},lt={class:"text-xs text-blue-700"},dt=["value"],nt={class:"flex justify-end space-x-3 pt-4"},gt={__name:"Credits",setup(it){const z=[{title:"Dashboard",href:"/dashboard"},{title:"Credits",href:"/credits"}],M=n(!1),f=n([]),w=n([]);n(0);const p=n({total_earned:0,total_used:0,current_balance:0,total_transactions:0,users_with_credits:0}),h=n(!1),A=n(!1),u=n([]),g=n(""),i=n({user_id:"",amount:"",source:"admin",description:""}),B=[{value:"admin",label:"Admin Credit"},{value:"referral",label:"Referral Bonus"},{value:"promotion",label:"Promotional Credit"},{value:"bonus",label:"Bonus Credit"},{value:"compensation",label:"Compensation"},{value:"welcome",label:"Welcome Bonus"}],d=n({user_id:"",type:"",source:"",search:""}),v=n(null),V=async()=>{M.value=!0;try{const r=await window.axios.get("/credits-list");f.value=r.data.data||[]}catch(r){console.error("Error fetching credits:",r),f.value=[]}finally{M.value=!1}},_=async()=>{try{const r=new URLSearchParams;d.value.user_id&&r.append("user_id",d.value.user_id),d.value.type&&r.append("type",d.value.type),d.value.source&&r.append("source",d.value.source);const t=await window.axios.get(`/credits-all-transactions?${r}`);w.value=t.data.data||[]}catch(r){console.error("Error fetching transactions:",r),w.value=[]}},N=async()=>{try{const r=await window.axios.get("/credits-stats");p.value=r.data}catch(r){console.error("Error fetching stats:",r),p.value={total_earned:0,total_used:0,current_balance:0,total_transactions:0,users_with_credits:0}}},j=async r=>{if(!r||r.length<2){u.value=[];return}A.value=!0;try{const t=await window.axios.get("/users-search",{params:{search:r,limit:10}});u.value=t.data.data||[]}catch(t){console.error("Error searching users:",t),u.value=[]}finally{A.value=!1}},L=async()=>{if(!i.value.user_id||!i.value.amount){alert("Please fill in all required fields");return}try{const r=await window.axios.post("/credits-add",i.value);r.data.success?(alert("Credit added successfully!"),h.value=!1,D(),await V(),await _(),await N()):alert(r.data.message||"Failed to add credit")}catch(r){console.error("Error adding credit:",r),alert("Failed to add credit. Please try again.")}},D=()=>{i.value={user_id:"",amount:"",source:"admin",description:""},v.value=null,g.value="",u.value=[]},H=r=>{v.value=r,i.value.user_id=r.id,g.value=r.name,u.value=[]},I=()=>{_()},P=()=>{d.value={user_id:"",type:"",source:"",search:""},_()};G(()=>{V(),_(),N()});const R=F(()=>p.value.total_earned||0),q=F(()=>p.value.total_used||0),O=F(()=>p.value.current_balance||0),E=r=>new Date(r).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),k=r=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(r),Q=r=>({earned:"bg-green-100 text-green-800",used:"bg-red-100 text-red-800",expired:"bg-gray-100 text-gray-800",refunded:"bg-blue-100 text-blue-800"})[r]||"bg-gray-100 text-gray-800",W=r=>({admin:"bg-purple-100 text-purple-800",referral:"bg-pink-100 text-pink-800",promotion:"bg-yellow-100 text-yellow-800",bonus:"bg-indigo-100 text-indigo-800",compensation:"bg-orange-100 text-orange-800",welcome:"bg-teal-100 text-teal-800"})[r]||"bg-gray-100 text-gray-800";return(r,t)=>(l(),a(x,null,[$(J(K),{title:"Credits Management"}),$(ee,{breadcrumbs:z},{default:X(()=>[e("div",te,[e("div",se,[t[11]||(t[11]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Credits Management"),e("p",{class:"text-gray-600"},"Manage user credits and transaction history")],-1)),e("button",{onClick:t[0]||(t[0]=s=>h.value=!0),class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"},t[10]||(t[10]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),Y(" Add Credit ")]))]),e("div",re,[e("div",oe,[e("div",ae,[t[13]||(t[13]=e("div",{class:"p-2 bg-green-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",le,[t[12]||(t[12]=e("p",{class:"text-sm font-medium text-gray-600"},"Total Earned",-1)),e("p",de,o(k(R.value)),1)])])]),e("div",ne,[e("div",ie,[t[15]||(t[15]=e("div",{class:"p-2 bg-red-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1)),e("div",ue,[t[14]||(t[14]=e("p",{class:"text-sm font-medium text-gray-600"},"Total Used",-1)),e("p",ce,o(k(q.value)),1)])])]),e("div",pe,[e("div",me,[t[17]||(t[17]=e("div",{class:"p-2 bg-blue-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",xe,[t[16]||(t[16]=e("p",{class:"text-sm font-medium text-gray-600"},"Current Balance",-1)),e("p",ge,o(k(O.value)),1)])])]),e("div",ve,[e("div",be,[t[19]||(t[19]=e("div",{class:"p-2 bg-purple-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",ye,[t[18]||(t[18]=e("p",{class:"text-sm font-medium text-gray-600"},"Transactions",-1)),e("p",fe,o(p.value.total_transactions),1)])])])]),e("div",we,[t[25]||(t[25]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Filter Transactions",-1)),e("div",he,[e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Transaction Type",-1)),c(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>d.value.type=s),class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},t[20]||(t[20]=[e("option",{value:""},"All Types",-1),e("option",{value:"earned"},"Earned",-1),e("option",{value:"used"},"Used",-1),e("option",{value:"expired"},"Expired",-1),e("option",{value:"refunded"},"Refunded",-1)]),512),[[T,d.value.type]])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Source",-1)),c(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>d.value.source=s),class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},[t[22]||(t[22]=e("option",{value:""},"All Sources",-1)),(l(),a(x,null,y(B,s=>e("option",{key:s.value,value:s.value},o(s.label),9,_e)),64))],512),[[T,d.value.source]])]),e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"User ID",-1)),c(e("input",{"onUpdate:modelValue":t[3]||(t[3]=s=>d.value.user_id=s),type:"number",placeholder:"Enter user ID",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},null,512),[[U,d.value.user_id]])]),e("div",{class:"flex items-end space-x-2"},[e("button",{onClick:I,class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"}," Apply Filters "),e("button",{onClick:P,class:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md"}," Clear ")])])]),e("div",ke,[t[28]||(t[28]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h2",{class:"text-lg font-medium text-gray-900"},"User Credit Balances")],-1)),M.value?(l(),a("div",Ce,t[26]||(t[26]=[e("div",{class:"animate-pulse"},[e("div",{class:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),e("div",{class:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),e("div",{class:"h-4 bg-gray-200 rounded w-5/6"})],-1)]))):f.value.length===0?(l(),a("div",Ue," No credit records found. ")):(l(),a("div",Me,[e("table",Ae,[t[27]||(t[27]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"User"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Current Balance"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Total Earned"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Total Spent"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Last Updated")])],-1)),e("tbody",Fe,[(l(!0),a(x,null,y(f.value,s=>{var m,b;return l(),a("tr",{key:s.id},[e("td",Te,[e("div",Se,o(((m=s.user)==null?void 0:m.name)||"N/A"),1),e("div",Be,o(((b=s.user)==null?void 0:b.email)||"N/A"),1)]),e("td",Ve," $"+o(parseFloat(s.balance||0).toFixed(2)),1),e("td",Ne," $"+o(parseFloat(s.total_earned||0).toFixed(2)),1),e("td",De," $"+o(parseFloat(s.total_spent||0).toFixed(2)),1),e("td",Ee,o(E(s.updated_at)),1)])}),128))])])]))]),e("div",$e,[t[30]||(t[30]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h2",{class:"text-lg font-medium text-gray-900"},"Credit Transactions")],-1)),w.value.length===0?(l(),a("div",ze," No transactions found. ")):(l(),a("div",je,[e("table",Le,[t[29]||(t[29]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"User"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Type"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Source"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Amount"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Description"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Date")])],-1)),e("tbody",He,[(l(!0),a(x,null,y(w.value,s=>{var m,b;return l(),a("tr",{key:s.id},[e("td",Ie,[e("div",Pe,o(((m=s.user)==null?void 0:m.name)||"N/A"),1),e("div",Re,o(((b=s.user)==null?void 0:b.email)||"N/A"),1)]),e("td",qe,[e("span",{class:S([Q(s.type),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(s.type),3)]),e("td",Oe,[e("span",{class:S([W(s.source),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(s.source||"N/A"),3)]),e("td",Qe,[e("span",{class:S(s.type==="earned"?"text-green-600":"text-red-600")},o(s.type==="earned"?"+":"-")+o(k(Math.abs(parseFloat(s.amount||0)))),3)]),e("td",We,o(s.description||"N/A"),1),e("td",Ge,o(E(s.created_at)),1)])}),128))])])]))])]),h.value?(l(),a("div",Je,[e("div",Ke,[e("div",Xe,[t[37]||(t[37]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Add Credit to User",-1)),e("form",{onSubmit:Z(L,["prevent"]),class:"space-y-4"},[e("div",null,[t[32]||(t[32]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Search User",-1)),e("div",Ye,[c(e("input",{"onUpdate:modelValue":t[4]||(t[4]=s=>g.value=s),onInput:t[5]||(t[5]=s=>j(g.value)),type:"text",placeholder:"Type user name or email...",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",required:""},null,544),[[U,g.value]]),A.value?(l(),a("div",Ze,t[31]||(t[31]=[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"},null,-1)]))):C("",!0)]),u.value.length>0?(l(),a("div",et,[(l(!0),a(x,null,y(u.value,s=>(l(),a("div",{key:s.id,onClick:m=>H(s),class:"p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"},[e("div",st,o(s.name),1),e("div",rt,o(s.email),1)],8,tt))),128))])):C("",!0),v.value?(l(),a("div",ot,[e("div",at,"Selected: "+o(v.value.name),1),e("div",lt,o(v.value.email),1)])):C("",!0)]),e("div",null,[t[33]||(t[33]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Amount ($)",-1)),c(e("input",{"onUpdate:modelValue":t[6]||(t[6]=s=>i.value.amount=s),type:"number",step:"0.01",min:"0.01",placeholder:"0.00",required:"",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},null,512),[[U,i.value.amount]])]),e("div",null,[t[34]||(t[34]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Credit Type",-1)),c(e("select",{"onUpdate:modelValue":t[7]||(t[7]=s=>i.value.source=s),class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},[(l(),a(x,null,y(B,s=>e("option",{key:s.value,value:s.value},o(s.label),9,dt)),64))],512),[[T,i.value.source]])]),e("div",null,[t[35]||(t[35]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Description (Optional)",-1)),c(e("textarea",{"onUpdate:modelValue":t[8]||(t[8]=s=>i.value.description=s),rows:"3",placeholder:"Enter description for this credit...",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},null,512),[[U,i.value.description]])]),e("div",nt,[e("button",{type:"button",onClick:t[9]||(t[9]=s=>{h.value=!1,D()}),class:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"}," Cancel "),t[36]||(t[36]=e("button",{type:"submit",class:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"}," Add Credit ",-1))])],32)])])])):C("",!0)]),_:1})],64))}};export{gt as default};
