<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('waitlist_requests')) {
            Schema::create('waitlist_requests', function (Blueprint $table) {
                $table->id();
                $table->string('email')->unique();
                $table->string('name')->nullable();
                $table->enum('status', ['pending', 'invited', 'registered'])->default('pending');
                $table->timestamp('invited_at')->nullable();
                $table->timestamp('registered_at')->nullable();
                $table->unsignedBigInteger('invitation_id')->nullable();
                $table->json('metadata')->nullable();
                $table->timestamps();
                
                $table->foreign('invitation_id')->references('id')->on('waitlist_invitations')->onDelete('set null');
                $table->index(['status', 'created_at']);
                $table->index('email');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('waitlist_requests');
    }
};
