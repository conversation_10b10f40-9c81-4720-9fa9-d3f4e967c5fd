<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\PersonalAccessToken;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class ApiAuthenticationPHPUnitTest extends TestCase
{
    use RefreshDatabase;

    public function test_api_user_can_login_with_valid_credentials()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'patient',
        ]);

        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'device_name' => 'test-device',
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'user',
                    'access_token',
                    'token_type',
                ]);

        // Verify token was created in database
        $this->assertDatabaseHas('personal_access_tokens', [
            'user_id' => $user->id,
            'name' => 'test-device',
        ]);
    }

    public function test_api_user_cannot_login_with_invalid_credentials()
    {
        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'wrong-password',
        ]);

        $response->assertStatus(401);
    }

    public function test_api_login_generates_valid_token_format()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'patient',
        ]);

        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'device_name' => 'test-device',
        ]);

        $response->assertStatus(200);
        $token = $response->json('access_token');
        
        // Token should be in format: id|plainTextToken
        $this->assertStringContainsString('|', $token);
        $this->assertGreaterThan(40, strlen($token)); // Should be reasonably long
    }

    public function test_api_token_can_authenticate_subsequent_requests()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'patient',
        ]);

        // Login to get token
        $loginResponse = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'device_name' => 'test-device',
        ]);

        $token = $loginResponse->json('access_token');

        // Use token to make authenticated request
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json',
        ])->getJson('/api/user');

        $response->assertStatus(200)
                ->assertJson([
                    'id' => $user->id,
                    'email' => $user->email,
                ]);
    }

    public function test_api_invalid_token_returns_401()
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer invalid-token',
            'Accept' => 'application/json',
        ])->getJson('/api/user');

        $response->assertStatus(401);
    }

    public function test_api_token_validation_with_custom_personal_access_token_model()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'patient',
        ]);

        // Create token using our custom model
        $tokenResult = $user->createToken('test-device');
        $token = $tokenResult->plainTextToken;

        // Verify the token exists in database with correct user_id
        $this->assertDatabaseHas('personal_access_tokens', [
            'user_id' => $user->id,
            'name' => 'test-device',
        ]);

        // Test that the token can be found and validated
        $accessToken = PersonalAccessToken::findToken($token);
        $this->assertNotNull($accessToken);
        $this->assertEquals($user->id, $accessToken->user_id);
        $this->assertEquals($user->id, $accessToken->user->id);
    }

    public function test_api_multiple_device_tokens_for_same_user()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'patient',
        ]);

        // Login from multiple devices
        $device1Response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'device_name' => 'device-1',
        ]);

        $device2Response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'device_name' => 'device-2',
        ]);

        $device1Response->assertStatus(200);
        $device2Response->assertStatus(200);

        $token1 = $device1Response->json('access_token');
        $token2 = $device2Response->json('access_token');

        // Both tokens should work
        $this->withHeaders(['Authorization' => 'Bearer ' . $token1])
             ->getJson('/api/user')
             ->assertStatus(200);

        $this->withHeaders(['Authorization' => 'Bearer ' . $token2])
             ->getJson('/api/user')
             ->assertStatus(200);

        // Should have 2 tokens in database
        $this->assertEquals(2, PersonalAccessToken::where('user_id', $user->id)->count());
    }

    public function test_api_token_works_with_flutter_like_request_headers()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'patient',
        ]);

        // Create patient profile for the user
        \App\Models\Patient::create([
            'user_id' => $user->id,
            'emergency_contact_name' => 'Test Contact',
            'emergency_contact_phone' => '**********',
        ]);

        // Simulate Flutter login request with all headers
        $loginResponse = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'device_type' => 'android',
            'user_agent' => 'medroid_app/1.0.0 (Android Android 15; RMX3771)',
            'device_model' => 'RMX3771',
            'platform' => 'Android',
            'app_version' => '1.0.0',
        ], [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ]);

        $loginResponse->assertStatus(200);
        $token = $loginResponse->json('access_token');

        // Test subsequent requests with Flutter-like headers
        $response = $this->withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/appointments-list');

        $response->assertStatus(200);
        $this->assertIsArray($response->json());
    }

    public function test_api_token_persists_across_multiple_requests()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'patient',
        ]);

        // Login to get token
        $loginResponse = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'device_name' => 'test-device',
        ]);

        $token = $loginResponse->json('access_token');

        // Make multiple requests with the same token
        for ($i = 0; $i < 5; $i++) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Accept' => 'application/json',
            ])->getJson('/api/user');

            $response->assertStatus(200);
        }

        // Token should still exist and be valid
        $accessToken = PersonalAccessToken::findToken($token);
        $this->assertNotNull($accessToken);
        $this->assertEquals($user->id, $accessToken->user_id);
    }

    public function test_api_token_does_not_expire_for_better_flutter_compatibility()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'patient',
        ]);

        $loginResponse = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'device_name' => 'test-device',
        ]);

        $token = $loginResponse->json('access_token');
        $accessToken = PersonalAccessToken::findToken($token);

        // Token should not have expiration set for better mobile app experience
        $this->assertNull($accessToken->expires_at);
        $this->assertEquals($user->id, $accessToken->user_id);
        $this->assertEquals('test-device', $accessToken->name);
    }
}
