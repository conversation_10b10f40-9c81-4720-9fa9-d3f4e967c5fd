/**
 * Simple logout composable
 * Provides a clean, consistent logout function across the app
 */

import { router } from '@inertiajs/vue3';

export function useLogout() {
    const logout = () => {
        // Clear any local storage data
        try {
            localStorage.removeItem('user');
            localStorage.removeItem('auth_token');
            localStorage.removeItem('chat_history');
            sessionStorage.clear();
        } catch (error) {
            console.log('Error clearing storage:', error);
        }

        // Perform logout with Inertia
        router.post('/logout', {}, {
            onFinish: () => {
                // Force redirect to home page
                window.location.href = '/';
            }
        });
    };

    return {
        logout
    };
}
