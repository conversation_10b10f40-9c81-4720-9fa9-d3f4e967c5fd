<?php

/**
 * <PERSON><PERSON>t to auto-verify existing providers
 * Run this script on production to fix appointment slots issues
 * 
 * Usage: php verify_providers.php [--dry-run]
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel application
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Provider;
use Carbon\Carbon;

// Check for dry-run flag
$dryRun = in_array('--dry-run', $argv);

echo "=== MEDROID PROVIDER VERIFICATION SCRIPT ===\n\n";

if ($dryRun) {
    echo "🔍 DRY RUN MODE - No changes will be made\n\n";
} else {
    echo "⚠️  LIVE MODE - Changes will be applied to database\n\n";
}

// Get all providers that are not verified
$unverifiedProviders = Provider::where('verification_status', '!=', 'verified')
    ->with('user')
    ->get();

echo "Found {$unverifiedProviders->count()} unverified providers\n\n";

if ($unverifiedProviders->count() == 0) {
    echo "✅ All providers are already verified!\n";
    exit(0);
}

echo "Providers to be verified:\n";
foreach ($unverifiedProviders as $provider) {
    $userName = $provider->user ? $provider->user->name : 'No User';
    $userEmail = $provider->user ? $provider->user->email : 'No Email';
    echo "- ID: {$provider->id} | Name: {$userName} | Email: {$userEmail} | Current Status: {$provider->verification_status}\n";
}

echo "\n";

if (!$dryRun) {
    echo "Do you want to verify all these providers? (y/N): ";
    $handle = fopen("php://stdin", "r");
    $line = fgets($handle);
    fclose($handle);
    
    if (trim(strtolower($line)) !== 'y') {
        echo "Operation cancelled.\n";
        exit(0);
    }
    
    echo "\nVerifying providers...\n\n";
    
    $verifiedCount = 0;
    $errorCount = 0;
    
    foreach ($unverifiedProviders as $provider) {
        try {
            $provider->update([
                'verification_status' => 'verified',
                'verified_at' => Carbon::now(),
                'rejection_reason' => null // Clear any rejection reason
            ]);
            
            $userName = $provider->user ? $provider->user->name : 'Provider ' . $provider->id;
            echo "✅ Verified: {$userName} (ID: {$provider->id})\n";
            $verifiedCount++;
            
        } catch (Exception $e) {
            $userName = $provider->user ? $provider->user->name : 'Provider ' . $provider->id;
            echo "❌ Error verifying {$userName} (ID: {$provider->id}): " . $e->getMessage() . "\n";
            $errorCount++;
        }
    }
    
    echo "\n=== VERIFICATION COMPLETE ===\n";
    echo "✅ Successfully verified: {$verifiedCount} providers\n";
    
    if ($errorCount > 0) {
        echo "❌ Errors: {$errorCount} providers\n";
    }
    
    // Verify the changes
    echo "\n=== VERIFICATION STATUS AFTER UPDATE ===\n";
    $totalProviders = Provider::count();
    $verifiedProviders = Provider::where('verification_status', 'verified')->count();
    $pendingProviders = Provider::where('verification_status', 'pending')->count();
    $rejectedProviders = Provider::where('verification_status', 'rejected')->count();
    
    echo "Total Providers: {$totalProviders}\n";
    echo "Verified: {$verifiedProviders}\n";
    echo "Pending: {$pendingProviders}\n";
    echo "Rejected: {$rejectedProviders}\n";
    
    if ($verifiedProviders > 0) {
        echo "\n✅ SUCCESS: Appointment slots should now be available in chat!\n";
        echo "💡 TIP: Test the chat appointment booking feature to confirm it's working.\n";
    } else {
        echo "\n❌ WARNING: No verified providers found. There may be other issues.\n";
    }
    
} else {
    echo "🔍 DRY RUN: Would verify {$unverifiedProviders->count()} providers\n";
    echo "💡 Run without --dry-run flag to apply changes: php verify_providers.php\n";
}

echo "\n=== SCRIPT COMPLETE ===\n";
