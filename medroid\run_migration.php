<?php

/**
 * Run migration script - Adds verified_at column to providers table
 * Usage: php run_migration.php
 */

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\Schema;

echo "🔧 MIGRATION RUNNER: Adding verified_at column to providers table...\n\n";

// Check if column already exists
if (Schema::hasColumn('providers', 'verified_at')) {
    echo "✅ verified_at column already exists. No migration needed.\n";
    exit(0);
}

echo "⚠️  verified_at column missing. Running migration...\n";

try {
    // Run the migration
    \Artisan::call('migrate', ['--force' => true]);
    
    $output = \Artisan::output();
    echo $output;
    
    // Verify the column was added
    if (Schema::hasColumn('providers', 'verified_at')) {
        echo "\n✅ SUCCESS: verified_at column added successfully!\n";
        echo "💡 You can now run the provider verification scripts.\n";
    } else {
        echo "\n❌ ERROR: Column was not added. Check migration files.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    echo "\n💡 Try running manually:\n";
    echo "   php artisan migrate --force\n";
}

echo "\n=== MIGRATION COMPLETE ===\n";
