import{r as b,o as T,d as r,e as a,f as S,u as q,m as G,g as O,i as t,j as v,t as c,z as Y,l as n,v as u,F as y,q as w,p as H,x as C,M as V,A,a as M}from"./vendor-DkZiYBIF.js";import{_ as J}from"./AppLayout.vue_vue_type_script_setup_true_lang-C2T_8RUB.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-fZxfkI-v.js";import"./createLucideIcon-CSZE2I1R.js";const Q={class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},R={key:0,class:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4"},W={class:"flex"},X={class:"text-red-700"},Z={key:1,class:"mb-6 bg-green-50 border border-green-200 rounded-lg p-4"},ee={class:"flex"},te={class:"text-green-700"},le={key:2,class:"text-center py-12"},se={key:3,class:"bg-white rounded-lg shadow-sm border"},oe={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},re=["value"],ae={class:"mt-4"},ne={class:"mt-4"},ie={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ue={class:"flex flex-wrap gap-2 mb-4"},de=["onClick"],pe={class:"flex space-x-2"},ce={class:"border border-gray-200 rounded-lg p-4 mb-4"},be={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},me={class:"md:col-span-2"},fe={class:"flex items-center"},ge={key:0,class:"space-y-3 mb-4"},ve={class:"flex-1"},ye={class:"flex items-center"},xe={class:"text-sm font-medium text-gray-900"},ke={key:0,class:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},_e={class:"text-sm text-gray-600"},we={class:"flex items-center space-x-2"},Ce=["onClick"],Pe=["onClick"],Ve={key:1,class:"text-center py-4 text-gray-500 mb-4"},Ue={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6"},ze={class:"mb-4"},Le={class:"flex items-center mb-3"},Se={key:0},Ae={class:"flex gap-2 mb-3"},Me={key:0,class:"flex flex-wrap gap-2"},Fe=["onClick"],Ie={class:"space-y-2 mb-4"},$e={class:"text-sm"},Ne=["onClick"],De={class:"flex space-x-2"},Ee={class:"flex justify-end pt-6 border-t"},he=["disabled"],je={key:0,class:"fas fa-spinner fa-spin mr-2"},Be={key:1,class:"fas fa-save mr-2"},He={__name:"Profile",setup(Ke){const F=[{title:"Dashboard",href:"/dashboard"},{title:"Provider Profile",href:"/provider/profile"}],P=b(!1),x=b(!1),k=b(null),_=b(""),s=b({specialization:"",bio:"",education:"",license_number:"",gender:"",languages:[],practice_locations:[],accepts_insurance:!1,insurance_providers:[],pricing:{consultation:0,follow_up:0},phone:"",address:"",city:"",state:"",zip_code:"",consultation_fee:0,certifications:[]}),m=b(""),f=b(""),g=b(""),i=b({address:"",city:"",state:"",zip_code:"",coordinates:[0,0],is_primary:!1}),I=[{value:"male",label:"Male"},{value:"female",label:"Female"},{value:"other",label:"Other"}],$=async()=>{var p,e,l;P.value=!0;try{const d=await M.get("/provider/get-profile");if(d.data){const o=d.data;s.value={specialization:o.specialization||"",bio:o.bio||"",education:o.education||"",license_number:o.license_number||"",gender:o.gender||"",languages:o.languages||[],practice_locations:o.practice_locations||[],accepts_insurance:o.accepts_insurance||!1,insurance_providers:o.insurance_providers||[],pricing:{consultation:((p=o.pricing)==null?void 0:p.consultation)||o.consultation_fee||0,follow_up:((e=o.pricing)==null?void 0:e.follow_up)||0},phone:o.phone||"",address:o.address||"",city:o.city||"",state:o.state||"",zip_code:o.zip_code||"",consultation_fee:o.consultation_fee||((l=o.pricing)==null?void 0:l.consultation)||0,certifications:o.certifications||[]}}}catch(d){console.error("Error fetching profile:",d),k.value="Failed to load profile data"}finally{P.value=!1}},N=async()=>{x.value=!0,k.value=null,_.value="";try{await M.post("/provider/profile",s.value),_.value="Profile updated successfully!",setTimeout(()=>{_.value=""},3e3)}catch(p){console.error("Error saving profile:",p),k.value="Failed to save profile. Please try again."}finally{x.value=!1}},U=()=>{m.value.trim()&&!s.value.languages.includes(m.value.trim())&&(s.value.languages.push(m.value.trim()),m.value="")},D=p=>{s.value.languages.splice(p,1)},z=()=>{f.value.trim()&&!s.value.certifications.includes(f.value.trim())&&(s.value.certifications.push(f.value.trim()),f.value="")},E=p=>{s.value.certifications.splice(p,1)},h=()=>{i.value.address.trim()&&(s.value.practice_locations.push({address:i.value.address.trim(),city:i.value.city.trim(),state:i.value.state.trim(),zip_code:i.value.zip_code.trim(),coordinates:i.value.coordinates,is_primary:i.value.is_primary||s.value.practice_locations.length===0}),i.value={address:"",city:"",state:"",zip_code:"",coordinates:[0,0],is_primary:!1})},j=p=>{s.value.practice_locations.splice(p,1)},B=p=>{s.value.practice_locations.forEach((e,l)=>{e.is_primary=l===p})},L=()=>{g.value.trim()&&!s.value.insurance_providers.includes(g.value.trim())&&(s.value.insurance_providers.push(g.value.trim()),g.value="")},K=p=>{s.value.insurance_providers.splice(p,1)};return T(()=>{$()}),(p,e)=>(a(),r(y,null,[S(q(G),{title:"Provider Profile"}),S(J,{breadcrumbs:F},{default:O(()=>[t("div",Q,[e[56]||(e[56]=t("div",{class:"mb-8"},[t("h1",{class:"text-3xl font-bold text-gray-900"},"Provider Profile"),t("p",{class:"mt-2 text-gray-600"},"Manage your professional information and credentials")],-1)),k.value?(a(),r("div",R,[t("div",W,[e[21]||(e[21]=t("i",{class:"fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"},null,-1)),t("p",X,c(k.value),1)])])):v("",!0),_.value?(a(),r("div",Z,[t("div",ee,[e[22]||(e[22]=t("i",{class:"fas fa-check-circle text-green-400 mr-3 mt-0.5"},null,-1)),t("p",te,c(_.value),1)])])):v("",!0),P.value?(a(),r("div",le,e[23]||(e[23]=[t("i",{class:"fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"},null,-1),t("p",{class:"text-gray-600"},"Loading profile...",-1)]))):(a(),r("div",se,[t("form",{onSubmit:Y(N,["prevent"]),class:"p-6 space-y-6"},[t("div",null,[e[30]||(e[30]=t("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Professional Information",-1)),t("div",oe,[t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Specialization",-1)),n(t("input",{"onUpdate:modelValue":e[0]||(e[0]=l=>s.value.specialization=l),type:"text",placeholder:"e.g., Cardiology, Dermatology",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[u,s.value.specialization]])]),t("div",null,[e[25]||(e[25]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"License Number",-1)),n(t("input",{"onUpdate:modelValue":e[1]||(e[1]=l=>s.value.license_number=l),type:"text",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[u,s.value.license_number]])]),t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Gender",-1)),n(t("select",{"onUpdate:modelValue":e[2]||(e[2]=l=>s.value.gender=l),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[e[26]||(e[26]=t("option",{value:""},"Select Gender",-1)),(a(),r(y,null,w(I,l=>t("option",{key:l.value,value:l.value},c(l.label),9,re)),64))],512),[[H,s.value.gender]])])]),t("div",ae,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Education",-1)),n(t("textarea",{"onUpdate:modelValue":e[3]||(e[3]=l=>s.value.education=l),rows:"3",placeholder:"Your educational background and qualifications",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[u,s.value.education]])]),t("div",ne,[e[29]||(e[29]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Bio",-1)),n(t("textarea",{"onUpdate:modelValue":e[4]||(e[4]=l=>s.value.bio=l),rows:"4",placeholder:"Tell patients about yourself and your practice",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[u,s.value.bio]])])]),t("div",null,[e[36]||(e[36]=t("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Contact Information",-1)),t("div",ie,[t("div",null,[e[31]||(e[31]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Phone",-1)),n(t("input",{"onUpdate:modelValue":e[5]||(e[5]=l=>s.value.phone=l),type:"tel",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[u,s.value.phone]])]),t("div",null,[e[32]||(e[32]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Address",-1)),n(t("input",{"onUpdate:modelValue":e[6]||(e[6]=l=>s.value.address=l),type:"text",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[u,s.value.address]])]),t("div",null,[e[33]||(e[33]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"City",-1)),n(t("input",{"onUpdate:modelValue":e[7]||(e[7]=l=>s.value.city=l),type:"text",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[u,s.value.city]])]),t("div",null,[e[34]||(e[34]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"State",-1)),n(t("input",{"onUpdate:modelValue":e[8]||(e[8]=l=>s.value.state=l),type:"text",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[u,s.value.state]])]),t("div",null,[e[35]||(e[35]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Postcode",-1)),n(t("input",{"onUpdate:modelValue":e[9]||(e[9]=l=>s.value.zip_code=l),type:"text",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[u,s.value.zip_code]])])])]),t("div",null,[e[38]||(e[38]=t("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Languages",-1)),t("div",ue,[(a(!0),r(y,null,w(s.value.languages,(l,d)=>(a(),r("span",{key:d,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"},[C(c(l)+" ",1),t("button",{onClick:o=>D(d),type:"button",class:"ml-2 text-blue-600 hover:text-blue-800"},e[37]||(e[37]=[t("i",{class:"fas fa-times text-xs"},null,-1)]),8,de)]))),128))]),t("div",pe,[n(t("input",{"onUpdate:modelValue":e[10]||(e[10]=l=>m.value=l),onKeyup:V(U,["enter"]),type:"text",placeholder:"Add a language",class:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,544),[[u,m.value]]),t("button",{onClick:U,type:"button",class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"}," Add ")])]),t("div",null,[e[47]||(e[47]=t("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Practice Locations",-1)),t("div",ce,[e[45]||(e[45]=t("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"Add New Location",-1)),t("div",be,[t("div",me,[e[39]||(e[39]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Address",-1)),n(t("input",{"onUpdate:modelValue":e[11]||(e[11]=l=>i.value.address=l),type:"text",placeholder:"Street address",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[u,i.value.address]])]),t("div",null,[e[40]||(e[40]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"City",-1)),n(t("input",{"onUpdate:modelValue":e[12]||(e[12]=l=>i.value.city=l),type:"text",placeholder:"City",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[u,i.value.city]])]),t("div",null,[e[41]||(e[41]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"State",-1)),n(t("input",{"onUpdate:modelValue":e[13]||(e[13]=l=>i.value.state=l),type:"text",placeholder:"State",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[u,i.value.state]])]),t("div",null,[e[42]||(e[42]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Postcode",-1)),n(t("input",{"onUpdate:modelValue":e[14]||(e[14]=l=>i.value.zip_code=l),type:"text",placeholder:"Postcode",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[u,i.value.zip_code]])]),t("div",fe,[n(t("input",{"onUpdate:modelValue":e[15]||(e[15]=l=>i.value.is_primary=l),type:"checkbox",id:"is_primary",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[A,i.value.is_primary]]),e[43]||(e[43]=t("label",{for:"is_primary",class:"ml-2 block text-sm text-gray-700"}," Set as primary location ",-1))])]),t("button",{onClick:h,type:"button",class:"mt-3 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},e[44]||(e[44]=[t("i",{class:"fas fa-plus mr-1"},null,-1),C(" Add Location ")]))]),s.value.practice_locations.length>0?(a(),r("div",ge,[(a(!0),r(y,null,w(s.value.practice_locations,(l,d)=>(a(),r("div",{key:d,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[t("div",ve,[t("div",ye,[t("span",xe,c(l.address),1),l.is_primary?(a(),r("span",ke," Primary ")):v("",!0)]),t("p",_e,c(l.city)+", "+c(l.state)+" "+c(l.zip_code),1)]),t("div",we,[l.is_primary?v("",!0):(a(),r("button",{key:0,onClick:o=>B(d),type:"button",class:"text-sm text-blue-600 hover:text-blue-800"}," Set Primary ",8,Ce)),t("button",{onClick:o=>j(d),type:"button",class:"text-red-600 hover:text-red-800"},e[46]||(e[46]=[t("i",{class:"fas fa-trash text-sm"},null,-1)]),8,Pe)])]))),128))])):(a(),r("div",Ve," No practice locations added yet "))]),t("div",null,[e[53]||(e[53]=t("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Pricing & Insurance",-1)),t("div",Ue,[t("div",null,[e[48]||(e[48]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Consultation Fee ($)",-1)),n(t("input",{"onUpdate:modelValue":e[16]||(e[16]=l=>s.value.pricing.consultation=l),type:"number",min:"0",step:"0.01",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[u,s.value.pricing.consultation,void 0,{number:!0}]])]),t("div",null,[e[49]||(e[49]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Follow-up Fee ($)",-1)),n(t("input",{"onUpdate:modelValue":e[17]||(e[17]=l=>s.value.pricing.follow_up=l),type:"number",min:"0",step:"0.01",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[u,s.value.pricing.follow_up,void 0,{number:!0}]])])]),t("div",ze,[t("div",Le,[n(t("input",{"onUpdate:modelValue":e[18]||(e[18]=l=>s.value.accepts_insurance=l),type:"checkbox",id:"accepts_insurance",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[A,s.value.accepts_insurance]]),e[50]||(e[50]=t("label",{for:"accepts_insurance",class:"ml-2 block text-sm font-medium text-gray-700"}," Accept Insurance ",-1))])]),s.value.accepts_insurance?(a(),r("div",Se,[e[52]||(e[52]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Insurance Providers",-1)),t("div",Ae,[n(t("input",{"onUpdate:modelValue":e[19]||(e[19]=l=>g.value=l),type:"text",placeholder:"Add insurance provider",class:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",onKeyup:V(L,["enter"])},null,544),[[u,g.value]]),t("button",{onClick:L,type:"button",class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Add ")]),s.value.insurance_providers.length>0?(a(),r("div",Me,[(a(!0),r(y,null,w(s.value.insurance_providers,(l,d)=>(a(),r("span",{key:d,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"},[C(c(l)+" ",1),t("button",{onClick:o=>K(d),type:"button",class:"ml-2 text-blue-600 hover:text-blue-800"},e[51]||(e[51]=[t("i",{class:"fas fa-times text-xs"},null,-1)]),8,Fe)]))),128))])):v("",!0)])):v("",!0)]),t("div",null,[e[55]||(e[55]=t("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Certifications",-1)),t("div",Ie,[(a(!0),r(y,null,w(s.value.certifications,(l,d)=>(a(),r("div",{key:d,class:"flex justify-between items-center p-3 bg-gray-50 rounded-lg"},[t("span",$e,c(l),1),t("button",{onClick:o=>E(d),type:"button",class:"text-red-600 hover:text-red-800"},e[54]||(e[54]=[t("i",{class:"fas fa-trash text-sm"},null,-1)]),8,Ne)]))),128))]),t("div",De,[n(t("input",{"onUpdate:modelValue":e[20]||(e[20]=l=>f.value=l),onKeyup:V(z,["enter"]),type:"text",placeholder:"Add a certification",class:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,544),[[u,f.value]]),t("button",{onClick:z,type:"button",class:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"}," Add ")])]),t("div",Ee,[t("button",{type:"submit",disabled:x.value,class:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"},[x.value?(a(),r("i",je)):(a(),r("i",Be)),C(" "+c(x.value?"Saving...":"Save Profile"),1)],8,he)])],32)]))])]),_:1})],64))}};export{He as default};
