<template>
  <AppLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        My Orders
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6 bg-white border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-2xl font-bold text-gray-900">My Orders</h1>
                <p class="text-gray-600 mt-1">Track and manage your orders</p>
              </div>
              <Link
                href="/shop"
                class="inline-flex items-center px-4 py-2 bg-medroid-orange border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-medroid-orange-dark focus:bg-medroid-orange-dark active:bg-medroid-orange-dark focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:ring-offset-2 transition ease-in-out duration-150"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
                Continue Shopping
              </Link>
            </div>
          </div>
        </div>

        <!-- Orders List -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 bg-white">
            <div v-if="orders.data.length === 0" class="text-center py-12">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No orders yet</h3>
              <p class="mt-1 text-sm text-gray-500">You haven't placed any orders yet.</p>
              <div class="mt-6">
                <Link
                  href="/shop"
                  class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-medroid-orange hover:bg-medroid-orange-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-medroid-orange"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                  Start Shopping
                </Link>
              </div>
            </div>

            <div v-else class="space-y-6">
              <div
                v-for="order in orders.data"
                :key="order.id"
                class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow duration-200"
              >
                <!-- Order Header -->
                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center space-x-4">
                    <div>
                      <h3 class="text-lg font-semibold text-gray-900">
                        Order #{{ order.order_number }}
                      </h3>
                      <p class="text-sm text-gray-500">
                        Placed on {{ formatDate(order.created_at) }}
                      </p>
                    </div>
                  </div>
                  <div class="flex items-center space-x-3">
                    <span :class="getStatusColor(order.status)" class="px-3 py-1 rounded-full text-sm font-medium">
                      {{ order.status_label }}
                    </span>
                    <span :class="getPaymentStatusColor(order.payment_status)" class="px-3 py-1 rounded-full text-sm font-medium">
                      {{ order.payment_status_label }}
                    </span>
                  </div>
                </div>

                <!-- Order Items Preview -->
                <div class="mb-4">
                  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div
                      v-for="item in order.items.slice(0, 3)"
                      :key="item.id"
                      class="flex items-center space-x-3"
                    >
                      <div class="flex-shrink-0 w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center">
                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M9 1v6m6-6v6" />
                        </svg>
                      </div>
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">
                          {{ item.product_name }}
                        </p>
                        <p class="text-sm text-gray-500">
                          Qty: {{ item.quantity }} × {{ item.formatted_unit_price }}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div v-if="order.items.length > 3" class="mt-2">
                    <p class="text-sm text-gray-500">
                      +{{ order.items.length - 3 }} more item{{ order.items.length - 3 > 1 ? 's' : '' }}
                    </p>
                  </div>
                </div>

                <!-- Order Footer -->
                <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-500">
                      Total: <span class="font-semibold text-gray-900">{{ order.formatted_total }}</span>
                    </div>
                    <div v-if="order.tracking_number" class="text-sm text-gray-500">
                      Tracking: <span class="font-medium">{{ order.tracking_number }}</span>
                    </div>
                  </div>
                  <div class="flex items-center space-x-3">
                    <Link
                      :href="`/shop/orders/${order.order_number}`"
                      class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-medroid-orange"
                    >
                      View Details
                    </Link>
                    <button
                      v-if="order.can_be_cancelled"
                      @click="cancelOrder(order)"
                      class="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      Cancel Order
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Pagination -->
            <div v-if="orders.data.length > 0" class="mt-6">
              <Pagination :links="orders.links" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Link, router } from '@inertiajs/vue3';
import AppLayout from '@/layouts/AppLayout.vue';
import Pagination from '@/components/Pagination.vue';

const props = defineProps({
  orders: {
    type: Object,
    required: true,
  },
});

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

const getStatusColor = (status) => {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    shipped: 'bg-purple-100 text-purple-800',
    delivered: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800',
    refunded: 'bg-gray-100 text-gray-800',
  };
  return colors[status] || 'bg-gray-100 text-gray-800';
};

const getPaymentStatusColor = (paymentStatus) => {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    paid: 'bg-green-100 text-green-800',
    failed: 'bg-red-100 text-red-800',
    refunded: 'bg-gray-100 text-gray-800',
  };
  return colors[paymentStatus] || 'bg-gray-100 text-gray-800';
};

const cancelOrder = (order) => {
  if (confirm(`Are you sure you want to cancel order #${order.order_number}?`)) {
    router.post(`/shop/orders/${order.order_number}/cancel`, {}, {
      onSuccess: () => {
        // Order list will be refreshed automatically
      },
      onError: (errors) => {
        alert('Failed to cancel order. Please try again.');
      },
    });
  }
};
</script>
