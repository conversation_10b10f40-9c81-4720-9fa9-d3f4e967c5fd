<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DigitalProductDownload extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'order_id',
        'order_item_id',
        'product_id',
        'download_token',
        'file_path',
        'original_filename',
        'download_count',
        'download_limit',
        'expires_at',
        'last_downloaded_at',
        'is_active',
    ];

    protected $casts = [
        'download_count' => 'integer',
        'download_limit' => 'integer',
        'expires_at' => 'datetime',
        'last_downloaded_at' => 'datetime',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function orderItem()
    {
        return $this->belongsTo(OrderItem::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    public function scopeWithinDownloadLimit($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('download_limit')
              ->orWhereRaw('download_count < download_limit');
        });
    }

    public function canDownload()
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->expires_at && $this->expires_at->isPast()) {
            return false;
        }

        if ($this->download_limit && $this->download_count >= $this->download_limit) {
            return false;
        }

        return true;
    }

    public function recordDownload()
    {
        $this->increment('download_count');
        $this->update(['last_downloaded_at' => now()]);
    }

    public function getIsExpiredAttribute()
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function getIsDownloadLimitReachedAttribute()
    {
        return $this->download_limit && $this->download_count >= $this->download_limit;
    }

    public function getRemainingDownloadsAttribute()
    {
        if (!$this->download_limit) {
            return null;
        }

        return max(0, $this->download_limit - $this->download_count);
    }

    public function getDownloadUrlAttribute()
    {
        return route('digital-download', ['token' => $this->download_token]);
    }

    public function getFileSizeAttribute()
    {
        $filePath = storage_path('app/' . $this->file_path);
        
        if (file_exists($filePath)) {
            return filesize($filePath);
        }

        return 0;
    }

    public function getFormattedFileSizeAttribute()
    {
        $bytes = $this->file_size;
        
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }
}
