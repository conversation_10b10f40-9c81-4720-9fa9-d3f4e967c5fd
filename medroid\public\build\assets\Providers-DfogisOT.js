import{r as p,w as ne,o as le,d as c,e as g,i as e,z as X,t as l,j as w,l as x,v as y,p as Z,F as z,q as U,A as de,a as S,c as ue,f as ee,u as te,m as ge,g as V,y as se,x as r,s as M,P as ce}from"./vendor-DkZiYBIF.js";import{_ as me}from"./AppLayout.vue_vue_type_script_setup_true_lang-C2T_8RUB.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-fZxfkI-v.js";import"./createLucideIcon-CSZE2I1R.js";const xe={class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},ve={class:"flex min-h-full items-center justify-center p-4"},pe={class:"bg-white px-6 py-4 border-b border-gray-200"},fe={class:"flex items-center justify-between"},be={class:"text-lg font-semibold text-gray-900"},ye={class:"space-y-6"},ke={class:"bg-gray-50 p-4 rounded-lg"},we={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},he={key:0},_e=["required"],Ce={class:"bg-gray-50 p-4 rounded-lg"},Ne={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ae=["value"],Ee={class:"bg-gray-50 p-4 rounded-lg"},$e={class:"space-y-4"},Pe={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ze={class:"flex items-center"},Se={class:"flex justify-end space-x-3 pt-6 border-t"},Ve=["disabled"],Me={__name:"ProviderModal",props:{provider:Object,isEdit:Boolean},emits:["close","saved"],setup(h,{emit:C}){const k=h,v=C,m=p(!1),o=p([]),i=p({name:"",email:"",phone_number:"",password:"",clinic_id:"",specialization:"",license_number:"",gender:"",bio:"",education:"",accepts_insurance:!1}),N=async()=>{m.value=!0;try{k.isEdit?await S.put(`/update-provider/${k.provider.id}`,i.value):await S.post("/save-provider-with-user",i.value),v("saved")}catch(u){console.error("Error saving provider:",u),alert("Error saving provider. Please try again.")}finally{m.value=!1}},_=async()=>{try{const u=await S.get("/clinics-list");o.value=u.data.data||u.data.clinics||[]}catch(u){console.error("Error fetching clinics:",u)}};return ne(()=>k.provider,u=>{var s,a,A;u&&k.isEdit&&Object.assign(i.value,{name:((s=u.user)==null?void 0:s.name)||"",email:((a=u.user)==null?void 0:a.email)||"",phone_number:((A=u.user)==null?void 0:A.phone_number)||"",clinic_id:u.clinic_id||"",specialization:u.specialization||"",license_number:u.license_number||"",gender:u.gender||"",bio:u.bio||"",education:u.education||"",accepts_insurance:u.accepts_insurance||!1})},{immediate:!0}),le(()=>{_()}),(u,s)=>(g(),c("div",xe,[e("div",{class:"fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm transition-opacity",onClick:s[0]||(s[0]=a=>u.$emit("close"))}),e("div",ve,[e("div",{class:"relative w-full max-w-4xl transform overflow-hidden rounded-lg bg-white shadow-xl transition-all",onClick:s[14]||(s[14]=X(()=>{},["stop"]))},[e("div",pe,[e("div",fe,[e("h3",be,l(h.isEdit?"Edit Provider":"Create New Provider"),1),e("button",{onClick:s[1]||(s[1]=a=>u.$emit("close")),class:"text-gray-400 hover:text-gray-600 transition-colors"},s[15]||(s[15]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),e("form",{onSubmit:X(N,["prevent"]),class:"px-6 py-4"},[e("div",ye,[e("div",ke,[s[20]||(s[20]=e("h4",{class:"text-md font-semibold text-gray-900 mb-4"},"User Information",-1)),e("div",we,[e("div",null,[s[16]||(s[16]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Full Name *",-1)),x(e("input",{"onUpdate:modelValue":s[2]||(s[2]=a=>i.value.name=a),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter full name"},null,512),[[y,i.value.name]])]),e("div",null,[s[17]||(s[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Email *",-1)),x(e("input",{"onUpdate:modelValue":s[3]||(s[3]=a=>i.value.email=a),type:"email",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter email address"},null,512),[[y,i.value.email]])]),e("div",null,[s[18]||(s[18]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Phone Number",-1)),x(e("input",{"onUpdate:modelValue":s[4]||(s[4]=a=>i.value.phone_number=a),type:"tel",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter phone number"},null,512),[[y,i.value.phone_number]])]),h.isEdit?w("",!0):(g(),c("div",he,[s[19]||(s[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Password *",-1)),x(e("input",{"onUpdate:modelValue":s[5]||(s[5]=a=>i.value.password=a),type:"password",required:!h.isEdit,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter password"},null,8,_e),[[y,i.value.password]])]))])]),e("div",Ce,[s[25]||(s[25]=e("h4",{class:"text-md font-semibold text-gray-900 mb-4"},"Provider Information",-1)),e("div",Ne,[e("div",null,[s[21]||(s[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Specialization *",-1)),x(e("input",{"onUpdate:modelValue":s[6]||(s[6]=a=>i.value.specialization=a),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., Cardiology, Dermatology"},null,512),[[y,i.value.specialization]])]),e("div",null,[s[22]||(s[22]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"License Number *",-1)),x(e("input",{"onUpdate:modelValue":s[7]||(s[7]=a=>i.value.license_number=a),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Medical license number"},null,512),[[y,i.value.license_number]])]),e("div",null,[s[24]||(s[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Clinic",-1)),x(e("select",{"onUpdate:modelValue":s[8]||(s[8]=a=>i.value.clinic_id=a),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},[s[23]||(s[23]=e("option",{value:""},"Select Clinic (Default will be assigned if none selected)",-1)),(g(!0),c(z,null,U(o.value,a=>(g(),c("option",{key:a.id,value:a.id},l(a.name),9,Ae))),128))],512),[[Z,i.value.clinic_id]])])])]),e("div",Ee,[s[31]||(s[31]=e("h4",{class:"text-md font-semibold text-gray-900 mb-4"},"Additional Information",-1)),e("div",$e,[e("div",Pe,[e("div",null,[s[27]||(s[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Gender",-1)),x(e("select",{"onUpdate:modelValue":s[9]||(s[9]=a=>i.value.gender=a),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},s[26]||(s[26]=[e("option",{value:""},"Select Gender",-1),e("option",{value:"male"},"Male",-1),e("option",{value:"female"},"Female",-1),e("option",{value:"other"},"Other",-1)]),512),[[Z,i.value.gender]])]),e("div",null,[s[28]||(s[28]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Education",-1)),x(e("input",{"onUpdate:modelValue":s[10]||(s[10]=a=>i.value.education=a),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Educational background"},null,512),[[y,i.value.education]])])]),e("div",null,[s[29]||(s[29]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Bio",-1)),x(e("textarea",{"onUpdate:modelValue":s[11]||(s[11]=a=>i.value.bio=a),rows:"3",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Brief professional biography"},null,512),[[y,i.value.bio]])]),e("div",ze,[x(e("input",{"onUpdate:modelValue":s[12]||(s[12]=a=>i.value.accepts_insurance=a),type:"checkbox",id:"accepts_insurance",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[de,i.value.accepts_insurance]]),s[30]||(s[30]=e("label",{for:"accepts_insurance",class:"ml-2 block text-sm text-gray-900"},"Accepts Insurance",-1))])])])]),e("div",Se,[e("button",{type:"button",onClick:s[13]||(s[13]=a=>u.$emit("close")),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"}," Cancel "),e("button",{type:"submit",disabled:m.value,class:"px-4 py-2 text-sm font-medium text-white bg-medroid-orange border border-transparent rounded-md hover:bg-medroid-orange-dark disabled:opacity-50"},l(m.value?"Saving...":h.isEdit?"Update Provider":"Create Provider"),9,Ve)])],32)])])]))}},Ue={class:"flex items-center justify-between"},je={class:"flex mt-2","aria-label":"Breadcrumb"},Be={class:"inline-flex items-center space-x-1 md:space-x-3"},Le={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},De={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},Ie={class:"py-12"},qe={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},Fe={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6"},Oe={class:"p-6"},Ge={class:"flex flex-col sm:flex-row gap-4"},Je={class:"flex-1"},Te={class:"relative"},Re={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},He={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},Qe={class:"p-6"},Ye={class:"flex items-center"},Ke={class:"ml-4"},We={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},Xe={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},Ze={class:"p-6"},et={class:"flex items-center"},tt={class:"ml-4"},st={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},lt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},ot={class:"p-6"},at={class:"flex items-center"},rt={class:"ml-4"},it={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},nt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},dt={class:"p-6"},ut={class:"flex items-center"},gt={class:"ml-4"},ct={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},mt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},xt={class:"p-6 text-gray-900 dark:text-gray-100"},vt={key:0,class:"text-center py-8"},pt={key:1,class:"text-center py-8"},ft={key:2,class:"overflow-x-auto"},bt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},yt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},kt={class:"px-6 py-4 whitespace-nowrap"},wt={class:"flex items-center"},ht={class:"ml-4"},_t={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},Ct={class:"text-sm text-gray-500 dark:text-gray-400"},Nt={class:"text-xs text-gray-400"},At={class:"px-6 py-4 whitespace-nowrap"},Et={class:"text-sm text-gray-900 dark:text-gray-100"},$t={class:"text-sm text-gray-500 dark:text-gray-400"},Pt={class:"px-6 py-4 whitespace-nowrap"},zt={class:"text-sm text-gray-900 dark:text-gray-100"},St={class:"text-sm text-gray-500 dark:text-gray-400"},Vt={class:"text-xs text-gray-400"},Mt={class:"px-6 py-4 whitespace-nowrap"},Ut={class:"px-6 py-4 whitespace-nowrap"},jt={class:"text-sm text-gray-900 dark:text-gray-100"},Bt={class:"text-xs text-gray-500 dark:text-gray-400"},Lt={class:"px-6 py-4 whitespace-nowrap"},Dt={class:"flex items-center"},It={class:"text-sm text-gray-900 dark:text-gray-100"},qt={class:"text-xs text-gray-500 dark:text-gray-400"},Ft={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Ot=["onClick"],Gt=["onClick"],Jt={key:0,class:"text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300"},Tt={key:0,class:"fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm overflow-y-auto h-full w-full z-50"},Rt={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white dark:bg-gray-800"},Ht={class:"mt-3"},Qt={class:"flex justify-between items-center mb-4"},Yt={class:"text-lg font-medium text-gray-900 dark:text-gray-100"},Kt={key:0,class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Wt={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg"},Xt={class:"space-y-2 text-sm"},Zt={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg"},es={class:"space-y-2 text-sm"},ts={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg"},ss={class:"space-y-2 text-sm"},ls={class:"flex items-center"},os={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg"},as={class:"space-y-2 text-sm"},rs={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg md:col-span-2"},is={class:"space-y-2 text-sm"},ns={class:"text-gray-600 dark:text-gray-400 bg-white dark:bg-gray-800 p-3 rounded"},ds={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4"},us={class:"flex justify-end mt-6 space-x-3"},gs={key:0,class:"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"},fs={__name:"Providers",setup(h){const C=[{title:"Dashboard",href:"/dashboard"},{title:"Providers",href:"/providers"}],k=p(!1),v=p([]),m=p(""),o=p(null),i=p(!1),N=p(!1),_=p(!1),u=async()=>{k.value=!0;try{const n=await window.axios.get("/providers-list");console.log("Providers API response:",n.data),v.value=n.data.data||n.data||[],console.log("Processed providers:",v.value)}catch(n){console.error("Error fetching providers:",n),v.value=[]}finally{k.value=!1}},s=n=>n?new Date(n).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):"N/A",a=n=>{if(!n)return[];if(typeof n=="string")try{return JSON.parse(n)}catch{return[]}return Array.isArray(n)?n:[]},A=n=>({verified:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",rejected:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",suspended:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"})[n]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",oe=n=>{o.value=n,i.value=!0},j=()=>{i.value=!1,o.value=null},B=()=>{o.value=null,N.value=!0},ae=n=>{o.value=n,_.value=!0},L=()=>{N.value=!1,_.value=!1,o.value=null},re=()=>{L(),u()},D=ue(()=>m.value?v.value.filter(n=>{var t,f,b,E,$,P;return((f=(t=n.user)==null?void 0:t.name)==null?void 0:f.toLowerCase().includes(m.value.toLowerCase()))||((E=(b=n.user)==null?void 0:b.email)==null?void 0:E.toLowerCase().includes(m.value.toLowerCase()))||(($=n.specialization)==null?void 0:$.toLowerCase().includes(m.value.toLowerCase()))||((P=n.license_number)==null?void 0:P.includes(m.value))}):v.value);return le(()=>{u()}),(n,t)=>(g(),c(z,null,[ee(te(ge),{title:"Provider Management"}),ee(me,null,{header:V(()=>[e("div",Ue,[e("div",null,[t[2]||(t[2]=e("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Provider Management ",-1)),e("nav",je,[e("ol",Be,[(g(),c(z,null,U(C,(f,b)=>e("li",{key:b,class:"inline-flex items-center"},[b<C.length-1?(g(),se(te(ce),{key:0,href:f.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:V(()=>[r(l(f.title),1)]),_:2},1032,["href"])):(g(),c("span",Le,l(f.title),1)),b<C.length-1?(g(),c("svg",De,t[1]||(t[1]=[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):w("",!0)])),64))])])]),e("button",{onClick:B,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"}," Add Provider ")])]),default:V(()=>{var f,b,E,$,P,I,q,F,O,G,J,T,R;return[e("div",Ie,[e("div",qe,[e("div",Fe,[e("div",Oe,[e("div",Ge,[e("div",Je,[e("div",Te,[t[3]||(t[3]=e("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[e("i",{class:"fas fa-search text-gray-400"})],-1)),x(e("input",{"onUpdate:modelValue":t[0]||(t[0]=d=>m.value=d),type:"text",placeholder:"Search providers by name, email, specialization, or license...",class:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[y,m.value]])])]),e("button",{onClick:B,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"},t[4]||(t[4]=[e("i",{class:"fas fa-plus mr-2"},null,-1),r("Add Provider ")]))])])]),e("div",Re,[e("div",He,[e("div",Qe,[e("div",Ye,[t[6]||(t[6]=e("div",{class:"flex-shrink-0"},[e("i",{class:"fas fa-user-md text-2xl text-blue-500"})],-1)),e("div",Ke,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Total Providers",-1)),e("p",We,l(v.value.length),1)])])])]),e("div",Xe,[e("div",Ze,[e("div",et,[t[8]||(t[8]=e("div",{class:"flex-shrink-0"},[e("i",{class:"fas fa-check-circle text-2xl text-green-500"})],-1)),e("div",tt,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Verified",-1)),e("p",st,l(v.value.filter(d=>d.verification_status==="verified").length),1)])])])]),e("div",lt,[e("div",ot,[e("div",at,[t[10]||(t[10]=e("div",{class:"flex-shrink-0"},[e("i",{class:"fas fa-clock text-2xl text-yellow-500"})],-1)),e("div",rt,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Pending",-1)),e("p",it,l(v.value.filter(d=>d.verification_status==="pending").length),1)])])])]),e("div",nt,[e("div",dt,[e("div",ut,[t[12]||(t[12]=e("div",{class:"flex-shrink-0"},[e("i",{class:"fas fa-stethoscope text-2xl text-purple-500"})],-1)),e("div",gt,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Specializations",-1)),e("p",ct,l([...new Set(v.value.map(d=>d.specialization).filter(Boolean))].length),1)])])])])]),e("div",mt,[e("div",xt,[k.value?(g(),c("div",vt,t[13]||(t[13]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):D.value.length===0?(g(),c("div",pt,t[14]||(t[14]=[e("i",{class:"fas fa-user-md text-4xl text-gray-400 mb-4"},null,-1),e("p",{class:"text-gray-500"},"No providers found",-1)]))):(g(),c("div",ft,[e("table",bt,[t[20]||(t[20]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Provider "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Clinic "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Specialization "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Experience "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Rating "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),e("tbody",yt,[(g(!0),c(z,null,U(D.value,d=>{var H,Q,Y,K,W;return g(),c("tr",{key:d.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",kt,[e("div",wt,[t[15]||(t[15]=e("div",{class:"flex-shrink-0 h-10 w-10"},[e("div",{class:"h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center"},[e("i",{class:"fas fa-user-md text-blue-600 dark:text-blue-400"})])],-1)),e("div",ht,[e("div",_t,l(((H=d.user)==null?void 0:H.name)||"Unknown Provider"),1),e("div",Ct,l(((Q=d.user)==null?void 0:Q.email)||"No Email"),1),e("div",Nt," ID: "+l(d.id),1)])])]),e("td",At,[e("div",Et,l(((Y=d.clinic)==null?void 0:Y.name)||"No Clinic"),1),e("div",$t,l(((K=d.clinic)==null?void 0:K.city)||"N/A"),1)]),e("td",Pt,[e("div",zt,l(d.specialization||"N/A"),1),e("div",St,l(d.license_number||"No License"),1),e("div",Vt,l(d.gender||"N/A"),1)]),e("td",Mt,[e("span",{class:M([A(d.verification_status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full capitalize"])},l(d.verification_status||"Unknown"),3)]),e("td",Ut,[e("div",jt,l(d.specialization||"N/A"),1),e("div",Bt,l(s((W=d.user)==null?void 0:W.created_at)),1)]),e("td",Lt,[e("div",Dt,[t[16]||(t[16]=e("i",{class:"fas fa-star text-yellow-400 mr-1"},null,-1)),e("span",It,l(d.rating||"0.0"),1)]),e("div",qt,l(d.accepts_insurance?"Accepts Insurance":"Cash Only"),1)]),e("td",Ft,[e("button",{onClick:ie=>oe(d),class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"},t[17]||(t[17]=[e("i",{class:"fas fa-eye mr-1"},null,-1),r("View ")]),8,Ot),e("button",{onClick:ie=>ae(d),class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"},t[18]||(t[18]=[e("i",{class:"fas fa-edit mr-1"},null,-1),r("Edit ")]),8,Gt),d.verification_status==="pending"?(g(),c("button",Jt,t[19]||(t[19]=[e("i",{class:"fas fa-check mr-1"},null,-1),r("Verify ")]))):w("",!0)])])}),128))])])]))])])])]),i.value?(g(),c("div",Tt,[e("div",Rt,[e("div",Ht,[e("div",Qt,[e("h3",Yt," Provider Details - "+l((b=(f=o.value)==null?void 0:f.user)==null?void 0:b.name),1),e("button",{onClick:j,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[21]||(t[21]=[e("i",{class:"fas fa-times text-xl"},null,-1)]))]),o.value?(g(),c("div",Kt,[e("div",Wt,[t[28]||(t[28]=e("h4",{class:"text-md font-semibold text-gray-900 dark:text-gray-100 mb-3"},[e("i",{class:"fas fa-user mr-2"}),r("Personal Information ")],-1)),e("div",Xt,[e("div",null,[t[22]||(t[22]=e("strong",null,"Name:",-1)),r(" "+l((E=o.value.user)==null?void 0:E.name),1)]),e("div",null,[t[23]||(t[23]=e("strong",null,"Email:",-1)),r(" "+l(($=o.value.user)==null?void 0:$.email),1)]),e("div",null,[t[24]||(t[24]=e("strong",null,"Phone:",-1)),r(" "+l(((P=o.value.user)==null?void 0:P.phone_number)||"N/A"),1)]),e("div",null,[t[25]||(t[25]=e("strong",null,"Gender:",-1)),r(" "+l(o.value.gender||"N/A"),1)]),e("div",null,[t[26]||(t[26]=e("strong",null,"Date of Birth:",-1)),r(" "+l(s(o.value.date_of_birth)),1)]),e("div",null,[t[27]||(t[27]=e("strong",null,"Status:",-1)),e("span",{class:M((I=o.value.user)!=null&&I.is_active?"text-green-600":"text-red-600")},l((q=o.value.user)!=null&&q.is_active?"Active":"Inactive"),3)])])]),e("div",Zt,[t[33]||(t[33]=e("h4",{class:"text-md font-semibold text-gray-900 dark:text-gray-100 mb-3"},[e("i",{class:"fas fa-stethoscope mr-2"}),r("Professional Information ")],-1)),e("div",es,[e("div",null,[t[29]||(t[29]=e("strong",null,"Specialization:",-1)),r(" "+l(o.value.specialization||"N/A"),1)]),e("div",null,[t[30]||(t[30]=e("strong",null,"License Number:",-1)),r(" "+l(o.value.license_number||"N/A"),1)]),e("div",null,[t[31]||(t[31]=e("strong",null,"Education:",-1)),r(" "+l(o.value.education||"N/A"),1)]),e("div",null,[t[32]||(t[32]=e("strong",null,"Verification Status:",-1)),e("span",{class:M([A(o.value.verification_status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full capitalize ml-2"])},l(o.value.verification_status||"Unknown"),3)])])]),e("div",ts,[t[41]||(t[41]=e("h4",{class:"text-md font-semibold text-gray-900 dark:text-gray-100 mb-3"},[e("i",{class:"fas fa-clinic-medical mr-2"}),r("Clinic Information ")],-1)),e("div",ss,[e("div",null,[t[34]||(t[34]=e("strong",null,"Clinic Name:",-1)),r(" "+l(((F=o.value.clinic)==null?void 0:F.name)||"No Clinic Assigned"),1)]),e("div",null,[t[35]||(t[35]=e("strong",null,"Clinic Address:",-1)),r(" "+l(((O=o.value.clinic)==null?void 0:O.full_address)||"N/A"),1)]),e("div",null,[t[36]||(t[36]=e("strong",null,"Clinic Phone:",-1)),r(" "+l(((G=o.value.clinic)==null?void 0:G.phone)||"N/A"),1)]),e("div",null,[t[37]||(t[37]=e("strong",null,"Clinic Email:",-1)),r(" "+l(((J=o.value.clinic)==null?void 0:J.email)||"N/A"),1)]),e("div",null,[t[38]||(t[38]=e("strong",null,"Accepts Insurance:",-1)),r(" "+l(o.value.accepts_insurance?"Yes":"No"),1)]),e("div",null,[t[40]||(t[40]=e("strong",null,"Rating:",-1)),e("span",ls,[t[39]||(t[39]=e("i",{class:"fas fa-star text-yellow-400 mr-1"},null,-1)),r(" "+l(o.value.rating||"0.0"),1)])])])]),e("div",os,[t[46]||(t[46]=e("h4",{class:"text-md font-semibold text-gray-900 dark:text-gray-100 mb-3"},[e("i",{class:"fas fa-calendar mr-2"}),r("Availability & Services ")],-1)),e("div",as,[e("div",null,[t[42]||(t[42]=e("strong",null,"Available Days:",-1)),r(" "+l(a(o.value.available_days).join(", ")||"N/A"),1)]),e("div",null,[t[43]||(t[43]=e("strong",null,"Available Hours:",-1)),r(" "+l(o.value.available_hours||"N/A"),1)]),e("div",null,[t[44]||(t[44]=e("strong",null,"Services:",-1)),r(" "+l(a(o.value.services).join(", ")||"N/A"),1)]),e("div",null,[t[45]||(t[45]=e("strong",null,"Languages:",-1)),r(" "+l(a(o.value.languages).join(", ")||"N/A"),1)])])]),e("div",rs,[t[50]||(t[50]=e("h4",{class:"text-md font-semibold text-gray-900 dark:text-gray-100 mb-3"},[e("i",{class:"fas fa-info-circle mr-2"}),r("Biography & Additional Information ")],-1)),e("div",is,[t[49]||(t[49]=e("div",null,[e("strong",null,"Bio:")],-1)),e("p",ns,l(o.value.bio||"No biography provided."),1),e("div",ds,[e("div",null,[t[47]||(t[47]=e("strong",null,"Joined:",-1)),r(" "+l(s((T=o.value.user)==null?void 0:T.created_at)),1)]),e("div",null,[t[48]||(t[48]=e("strong",null,"Last Updated:",-1)),r(" "+l(s(o.value.updated_at)),1)])])])])])):w("",!0),e("div",us,[e("button",{onClick:j,class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"}," Close "),t[52]||(t[52]=e("button",{class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"},[e("i",{class:"fas fa-edit mr-2"}),r("Edit Provider ")],-1)),((R=o.value)==null?void 0:R.verification_status)==="pending"?(g(),c("button",gs,t[51]||(t[51]=[e("i",{class:"fas fa-check mr-2"},null,-1),r("Verify Provider ")]))):w("",!0)])])])])):w("",!0),N.value||_.value?(g(),se(Me,{key:1,provider:o.value,isEdit:_.value,onClose:L,onSaved:re},null,8,["provider","isEdit"])):w("",!0)]}),_:1})],64))}};export{fs as default};
