import{n as K,c as g,r as x,o as Q,d as l,e as r,f as S,u as m,m as G,g as p,i as t,l as D,v as H,F as f,q as A,t as d,p as M,s as j,x as y,j as v,y as B,P as h}from"./vendor-DkZiYBIF.js";import{_ as J}from"./AppLayout.vue_vue_type_script_setup_true_lang-C2T_8RUB.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-fZxfkI-v.js";import"./createLucideIcon-CSZE2I1R.js";const O={class:"flex items-center justify-between"},W={class:"flex mt-2","aria-label":"Breadcrumb"},X={class:"inline-flex items-center space-x-1 md:space-x-3"},Y={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},Z={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},ee={class:"text-xs text-gray-500 mb-2"},te={class:"py-12"},se={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},ae={class:"mb-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},re={class:"p-6"},le={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},de=["value"],oe={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},ne={class:"p-6 text-gray-900 dark:text-gray-100"},ie={key:0,class:"text-center py-8"},ue={key:1,class:"text-center py-8"},ce={key:2,class:"overflow-x-auto"},ge={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},xe={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},pe={class:"px-6 py-4 whitespace-nowrap"},ye={class:"flex items-center"},me={class:"flex-shrink-0 h-10 w-10"},ve=["src","alt"],fe={key:1,class:"h-10 w-10 rounded bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},he={class:"ml-4"},be={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},ke={class:"text-sm text-gray-500 dark:text-gray-400"},_e={class:"px-6 py-4 whitespace-nowrap"},we={class:"text-sm text-gray-900 dark:text-gray-100"},Pe={class:"px-6 py-4 whitespace-nowrap"},Ce={class:"px-6 py-4 whitespace-nowrap"},Se={class:"text-sm text-gray-900 dark:text-gray-100"},De={key:0,class:"text-xs text-gray-500 line-through ml-1"},Ae={class:"px-6 py-4 whitespace-nowrap"},Be={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Ve={key:1,class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"},Ee={__name:"Index",setup(Le){const E=K(),o=g(()=>{var a;return(a=E.props.auth)==null?void 0:a.user}),b=[{title:"Dashboard",href:"/dashboard"},{title:"Products",href:"/admin/products"}],k=x(!1),_=x([]),V=x([]),n=x(""),u=x("all"),c=x("all"),L=async()=>{var a;k.value=!0;try{const e=new URLSearchParams;n.value&&e.append("search",n.value),u.value!=="all"&&e.append("category",u.value),c.value!=="all"&&e.append("type",c.value);const s=await window.axios.get(`/admin/products-list?${e.toString()}`);_.value=((a=s.data.products)==null?void 0:a.data)||s.data.products||[],s.data.categories&&(V.value=s.data.categories)}catch(e){console.error("Error fetching products:",e),_.value=[]}finally{k.value=!1}},$=a=>a?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",F=a=>a==="digital"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300":"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",N=a=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a),T=g(()=>_.value.filter(a=>{const e=!n.value||a.name.toLowerCase().includes(n.value.toLowerCase())||a.sku.toLowerCase().includes(n.value.toLowerCase()),s=u.value==="all"||a.category_id==u.value,i=c.value==="all"||a.type===c.value;return e&&s&&i})),w=g(()=>{var a,e;return((e=(a=o.value)==null?void 0:a.roles)==null?void 0:e.some(s=>s.name==="admin"))||!1}),U=g(()=>{var a,e;return((e=(a=o.value)==null?void 0:a.user_permissions)==null?void 0:e.includes("create products"))||!1}),I=g(()=>{var a,e;return((e=(a=o.value)==null?void 0:a.user_permissions)==null?void 0:e.includes("edit products"))||!1}),z=g(()=>{var a,e;return((e=(a=o.value)==null?void 0:a.user_permissions)==null?void 0:e.includes("delete products"))||!1}),R=a=>{var e;return w.value?!0:I.value&&a.user_id===((e=o.value)==null?void 0:e.id)},q=a=>{var e;return w.value?!0:z.value&&a.user_id===((e=o.value)==null?void 0:e.id)};return Q(()=>{L()}),(a,e)=>(r(),l(f,null,[S(m(G),{title:"Product Management"}),S(J,null,{header:p(()=>{var s,i;return[t("div",O,[t("div",null,[e[4]||(e[4]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Product Management ",-1)),t("nav",W,[t("ol",X,[(r(),l(f,null,A(b,(P,C)=>t("li",{key:C,class:"inline-flex items-center"},[C<b.length-1?(r(),B(m(h),{key:0,href:P.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:p(()=>[y(d(P.title),1)]),_:2},1032,["href"])):(r(),l("span",Y,d(P.title),1)),C<b.length-1?(r(),l("svg",Z,e[3]||(e[3]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):v("",!0)])),64))])])]),t("div",ee," Debug: isAdmin="+d(w.value)+", canCreateProducts="+d(U.value)+", userPermissions="+d(((i=(s=o.value)==null?void 0:s.user_permissions)==null?void 0:i.join(", "))||"none"),1),U.value?(r(),B(m(h),{key:0,href:"/admin/products/create",class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"},{default:p(()=>e[5]||(e[5]=[y(" Add Product ")])),_:1})):v("",!0),e[6]||(e[6]=y(" asdasd "))])]}),default:p(()=>[t("div",te,[t("div",se,[t("div",ae,[t("div",re,[t("div",le,[t("div",null,[D(t("input",{"onUpdate:modelValue":e[0]||(e[0]=s=>n.value=s),type:"text",placeholder:"Search products...",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[H,n.value]])]),t("div",null,[D(t("select",{"onUpdate:modelValue":e[1]||(e[1]=s=>u.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},[e[7]||(e[7]=t("option",{value:"all"},"All Categories",-1)),(r(!0),l(f,null,A(V.value,s=>(r(),l("option",{key:s.id,value:s.id},d(s.name),9,de))),128))],512),[[M,u.value]])]),t("div",null,[D(t("select",{"onUpdate:modelValue":e[2]||(e[2]=s=>c.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},e[8]||(e[8]=[t("option",{value:"all"},"All Types",-1),t("option",{value:"physical"},"Physical",-1),t("option",{value:"digital"},"Digital",-1)]),512),[[M,c.value]])]),t("div",null,[t("button",{onClick:L,class:"w-full bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"}," Refresh ")])])])]),t("div",oe,[t("div",ne,[k.value?(r(),l("div",ie,e[9]||(e[9]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):T.value.length===0?(r(),l("div",ue,e[10]||(e[10]=[t("p",{class:"text-gray-500 dark:text-gray-400"},"No products found.",-1)]))):(r(),l("div",ce,[t("table",ge,[e[14]||(e[14]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Product "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Category "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Type "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Price "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),t("tbody",xe,[(r(!0),l(f,null,A(T.value,s=>{var i;return r(),l("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",pe,[t("div",ye,[t("div",me,[s.primary_image?(r(),l("img",{key:0,src:`/storage/${s.primary_image}`,alt:s.name,class:"h-10 w-10 rounded object-cover"},null,8,ve)):(r(),l("div",fe,e[11]||(e[11]=[t("i",{class:"fas fa-box text-gray-500 dark:text-gray-400"},null,-1)])))]),t("div",he,[t("div",be,d(s.name),1),t("div",ke," SKU: "+d(s.sku),1)])])]),t("td",_e,[t("span",we,d(((i=s.category)==null?void 0:i.name)||"N/A"),1)]),t("td",Pe,[t("span",{class:j([F(s.type),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},d(s.type),3)]),t("td",Ce,[t("div",Se,[y(d(N(s.price))+" ",1),s.sale_price?(r(),l("span",De,d(N(s.sale_price)),1)):v("",!0)])]),t("td",Ae,[t("span",{class:j([$(s.is_active),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},d(s.is_active?"Active":"Inactive"),3)]),t("td",Be,[S(m(h),{href:`/admin/products/${s.id}`,class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"},{default:p(()=>e[12]||(e[12]=[y(" View ")])),_:2},1032,["href"]),R(s)?(r(),B(m(h),{key:0,href:`/admin/products/${s.id}/edit`,class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"},{default:p(()=>e[13]||(e[13]=[y(" Edit ")])),_:2},1032,["href"])):v("",!0),q(s)?(r(),l("button",Ve," Delete ")):v("",!0)])])}),128))])])]))])])])])]),_:1})],64))}};export{Ee as default};
