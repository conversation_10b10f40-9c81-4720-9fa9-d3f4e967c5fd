<?php

namespace App\Models;

use <PERSON><PERSON>\Sanctum\PersonalAccessToken as SanctumPersonalAccessToken;

class PersonalAccessToken extends SanctumPersonalAccessToken
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'name',
        'token',
        'abilities',
        'last_used_at',
        'expires_at',
    ];

    /**
     * Get the tokenable model that the access token belongs to.
     * Override to use direct user relationship instead of polymorphic
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function tokenable()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Alias for tokenable to maintain compatibility
     */
    public function user()
    {
        return $this->tokenable();
    }

    /**
     * Override the morphTo relationship to use our direct user relationship
     * This ensures Sanctum's middleware can properly resolve the user
     */
    public function getTokenableTypeAttribute()
    {
        return User::class;
    }

    /**
     * Override the morphTo relationship to use our direct user relationship
     * This ensures Sanctum's middleware can properly resolve the user
     */
    public function getTokenableIdAttribute()
    {
        return $this->user_id;
    }

    /**
     * Set the tokenable_type attribute (for Sanctum compatibility)
     */
    public function setTokenableTypeAttribute($value)
    {
        // Don't actually set anything, we use user_id directly
    }

    /**
     * Set the tokenable_id attribute (for Sanctum compatibility)
     */
    public function setTokenableIdAttribute($value)
    {
        $this->attributes['user_id'] = $value;
    }

    /**
     * Find the token instance matching the given token.
     * Override to ensure compatibility with our custom structure
     *
     * @param  string  $token
     * @return static|null
     */
    public static function findToken($token)
    {
        if (strpos($token, '|') === false) {
            return null;
        }

        [$id, $token] = explode('|', $token, 2);

        if ($instance = static::find($id)) {
            return hash_equals($instance->token, hash('sha256', $token)) ? $instance : null;
        }

        return null;
    }

    /**
     * Override the can method to work with our structure
     */
    public function can($ability)
    {
        return in_array('*', $this->abilities) ||
               array_key_exists($ability, array_flip($this->abilities));
    }

    /**
     * Override the cant method to work with our structure
     */
    public function cant($ability)
    {
        return ! $this->can($ability);
    }
}
