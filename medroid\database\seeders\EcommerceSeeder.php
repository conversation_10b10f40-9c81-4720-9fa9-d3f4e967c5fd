<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ProductCategory;
use App\Models\Product;

class EcommerceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create product categories
        $categories = [
            [
                'name' => 'Supplements',
                'slug' => 'supplements',
                'description' => 'Health supplements and vitamins',
                'icon' => '💊',
                'sort_order' => 1,
            ],
            [
                'name' => 'Medical Devices',
                'slug' => 'medical-devices',
                'description' => 'Medical equipment and devices',
                'icon' => '🩺',
                'sort_order' => 2,
            ],
            [
                'name' => 'Digital Health',
                'slug' => 'digital-health',
                'description' => 'Digital health products and apps',
                'icon' => '📱',
                'sort_order' => 3,
            ],
            [
                'name' => 'Wellness',
                'slug' => 'wellness',
                'description' => 'Wellness and fitness products',
                'icon' => '🧘',
                'sort_order' => 4,
            ],
            [
                'name' => 'Personal Care',
                'slug' => 'personal-care',
                'description' => 'Personal care and hygiene products',
                'icon' => '🧴',
                'sort_order' => 5,
            ],
        ];

        foreach ($categories as $categoryData) {
            ProductCategory::create($categoryData);
        }

        // Get created categories
        $supplementsCategory = ProductCategory::where('slug', 'supplements')->first();
        $medicalDevicesCategory = ProductCategory::where('slug', 'medical-devices')->first();
        $digitalHealthCategory = ProductCategory::where('slug', 'digital-health')->first();
        $wellnessCategory = ProductCategory::where('slug', 'wellness')->first();
        $personalCareCategory = ProductCategory::where('slug', 'personal-care')->first();

        // Create sample products
        $products = [
            // Supplements
            [
                'name' => 'Vitamin D3 5000 IU',
                'slug' => 'vitamin-d3-5000-iu',
                'description' => 'High-potency Vitamin D3 supplement for bone health and immune support. Each capsule contains 5000 IU of cholecalciferol.',
                'short_description' => 'High-potency Vitamin D3 for bone health and immune support',
                'type' => 'physical',
                'category_id' => $supplementsCategory->id,
                'price' => 24.99,
                'sku' => 'VIT-D3-5000',
                'stock_quantity' => 100,
                'is_featured' => true,
            ],
            [
                'name' => 'Omega-3 Fish Oil',
                'slug' => 'omega-3-fish-oil',
                'description' => 'Premium omega-3 fish oil supplement with EPA and DHA for heart and brain health.',
                'short_description' => 'Premium omega-3 for heart and brain health',
                'type' => 'physical',
                'category_id' => $supplementsCategory->id,
                'price' => 32.99,
                'sale_price' => 27.99,
                'sku' => 'OMEGA-3-1000',
                'stock_quantity' => 75,
            ],
            [
                'name' => 'Multivitamin Complex',
                'slug' => 'multivitamin-complex',
                'description' => 'Complete daily multivitamin with essential vitamins and minerals for overall health.',
                'short_description' => 'Complete daily multivitamin for overall health',
                'type' => 'physical',
                'category_id' => $supplementsCategory->id,
                'price' => 19.99,
                'sku' => 'MULTI-VIT-001',
                'stock_quantity' => 150,
            ],

            // Medical Devices
            [
                'name' => 'Digital Blood Pressure Monitor',
                'slug' => 'digital-blood-pressure-monitor',
                'description' => 'Accurate digital blood pressure monitor with large display and memory function.',
                'short_description' => 'Accurate digital blood pressure monitor',
                'type' => 'physical',
                'category_id' => $medicalDevicesCategory->id,
                'price' => 89.99,
                'sku' => 'BP-MON-001',
                'stock_quantity' => 25,
                'is_featured' => true,
            ],
            [
                'name' => 'Pulse Oximeter',
                'slug' => 'pulse-oximeter',
                'description' => 'Fingertip pulse oximeter for measuring blood oxygen saturation and pulse rate.',
                'short_description' => 'Fingertip pulse oximeter for oxygen monitoring',
                'type' => 'physical',
                'category_id' => $medicalDevicesCategory->id,
                'price' => 29.99,
                'sku' => 'PULSE-OX-001',
                'stock_quantity' => 50,
            ],

            // Digital Health
            [
                'name' => 'Health Tracking App Premium',
                'slug' => 'health-tracking-app-premium',
                'description' => 'Premium subscription to our comprehensive health tracking application with advanced analytics.',
                'short_description' => 'Premium health tracking app subscription',
                'type' => 'digital',
                'category_id' => $digitalHealthCategory->id,
                'price' => 9.99,
                'sku' => 'APP-HEALTH-PREM',
                'stock_quantity' => 0,
                'manage_stock' => false,
                'digital_files' => [
                    ['name' => 'app_access_key.txt', 'path' => 'digital/health_app_key.txt']
                ],
                'download_limit' => 3,
                'download_expiry_days' => 365,
            ],
            [
                'name' => 'Meditation Guide eBook',
                'slug' => 'meditation-guide-ebook',
                'description' => 'Comprehensive digital guide to meditation and mindfulness practices.',
                'short_description' => 'Digital meditation and mindfulness guide',
                'type' => 'digital',
                'category_id' => $digitalHealthCategory->id,
                'price' => 14.99,
                'sku' => 'EBOOK-MED-001',
                'stock_quantity' => 0,
                'manage_stock' => false,
                'digital_files' => [
                    ['name' => 'meditation_guide.pdf', 'path' => 'digital/meditation_guide.pdf']
                ],
                'download_limit' => 5,
                'download_expiry_days' => 30,
            ],

            // Wellness
            [
                'name' => 'Yoga Mat Premium',
                'slug' => 'yoga-mat-premium',
                'description' => 'High-quality non-slip yoga mat made from eco-friendly materials.',
                'short_description' => 'Premium eco-friendly yoga mat',
                'type' => 'physical',
                'category_id' => $wellnessCategory->id,
                'price' => 49.99,
                'sku' => 'YOGA-MAT-001',
                'stock_quantity' => 30,
            ],

            // Personal Care
            [
                'name' => 'Hand Sanitizer 500ml',
                'slug' => 'hand-sanitizer-500ml',
                'description' => 'Antibacterial hand sanitizer with 70% alcohol content.',
                'short_description' => 'Antibacterial hand sanitizer 500ml',
                'type' => 'physical',
                'category_id' => $personalCareCategory->id,
                'price' => 12.99,
                'sku' => 'SANITIZER-500',
                'stock_quantity' => 200,
            ],
        ];

        foreach ($products as $productData) {
            Product::create($productData);
        }
    }
}
