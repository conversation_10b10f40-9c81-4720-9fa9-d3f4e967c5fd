import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';

class DeviceInfoService {
  static final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();
  static Map<String, dynamic>? _cachedDeviceInfo;

  /// Get comprehensive device information
  static Future<Map<String, dynamic>> getDeviceInfo() async {
    if (_cachedDeviceInfo != null) {
      return _cachedDeviceInfo!;
    }

    try {
      final packageInfo = await PackageInfo.fromPlatform();
      Map<String, dynamic> deviceInfo = {
        'app_name': packageInfo.appName,
        'app_version': packageInfo.version,
        'build_number': packageInfo.buildNumber,
        'package_name': packageInfo.packageName,
      };

      if (kIsWeb) {
        final webInfo = await _deviceInfoPlugin.webBrowserInfo;
        deviceInfo.addAll({
          'platform': 'Web',
          'browser': webInfo.browserName.name,
          'user_agent': webInfo.userAgent ?? '',
          'device_type': 'web',
          'os_version': '${webInfo.platform}',
          'device_model': 'Web Browser',
          'device_brand': webInfo.vendor ?? 'Unknown',
        });
      } else if (Platform.isAndroid) {
        final androidInfo = await _deviceInfoPlugin.androidInfo;
        deviceInfo.addAll({
          'platform': 'Android',
          'device_type': 'android',
          'os_version': 'Android ${androidInfo.version.release}',
          'device_model': androidInfo.model,
          'device_brand': androidInfo.brand,
          'device_manufacturer': androidInfo.manufacturer,
          'device_id': androidInfo.id,
          'sdk_version': androidInfo.version.sdkInt.toString(),
          'is_physical_device': androidInfo.isPhysicalDevice,
        });
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfoPlugin.iosInfo;
        deviceInfo.addAll({
          'platform': 'iOS',
          'device_type': 'ios',
          'os_version': 'iOS ${iosInfo.systemVersion}',
          'device_model': iosInfo.model,
          'device_brand': 'Apple',
          'device_manufacturer': 'Apple',
          'device_id': iosInfo.identifierForVendor ?? '',
          'device_name': iosInfo.name,
          'is_physical_device': iosInfo.isPhysicalDevice,
        });
      }

      _cachedDeviceInfo = deviceInfo;
      return deviceInfo;
    } catch (e) {
      debugPrint('Error getting device info: $e');
      return _getBasicDeviceInfo();
    }
  }

  /// Get basic device information as fallback
  static Map<String, dynamic> _getBasicDeviceInfo() {
    if (kIsWeb) {
      return {
        'platform': 'Web',
        'device_type': 'web',
        'browser': 'Unknown',
        'user_agent': '',
        'os_version': 'Web',
        'device_model': 'Web Browser',
        'device_brand': 'Unknown',
      };
    } else if (Platform.isAndroid) {
      return {
        'platform': 'Android',
        'device_type': 'android',
        'os_version': 'Android',
        'device_model': 'Unknown',
        'device_brand': 'Unknown',
        'device_manufacturer': 'Unknown',
      };
    } else if (Platform.isIOS) {
      return {
        'platform': 'iOS',
        'device_type': 'ios',
        'os_version': 'iOS',
        'device_model': 'Unknown',
        'device_brand': 'Apple',
        'device_manufacturer': 'Apple',
      };
    }

    return {
      'platform': 'Unknown',
      'device_type': 'unknown',
      'os_version': 'Unknown',
      'device_model': 'Unknown',
      'device_brand': 'Unknown',
    };
  }

  /// Get device type for notifications (android/ios/web)
  static String getNotificationDeviceType() {
    if (kIsWeb) return 'web';
    if (Platform.isAndroid) return 'android';
    if (Platform.isIOS) return 'ios';
    return 'unknown';
  }

  /// Get platform name
  static String getPlatformName() {
    if (kIsWeb) return 'Web';
    if (Platform.isAndroid) return 'Android';
    if (Platform.isIOS) return 'iOS';
    return 'Unknown';
  }

  /// Clear cached device info (useful for testing)
  static void clearCache() {
    _cachedDeviceInfo = null;
  }

  /// Get user agent string (for web)
  static Future<String> getUserAgent() async {
    if (kIsWeb) {
      try {
        final webInfo = await _deviceInfoPlugin.webBrowserInfo;
        return webInfo.userAgent ?? '';
      } catch (e) {
        debugPrint('Error getting user agent: $e');
        return '';
      }
    }
    
    // For mobile, construct a user agent-like string
    final deviceInfo = await getDeviceInfo();
    return '${deviceInfo['app_name']}/${deviceInfo['app_version']} (${deviceInfo['platform']} ${deviceInfo['os_version']}; ${deviceInfo['device_model']})';
  }
}
