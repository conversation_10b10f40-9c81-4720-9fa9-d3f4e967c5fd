import{r as u,c as m,o as R,d,e as o,f as P,u as C,m as V,g as h,i as t,j as g,t as a,F as c,q as w,s as D,z as S,y as G,P as O,x as q}from"./vendor-DkZiYBIF.js";import{_ as J}from"./AppLayout.vue_vue_type_script_setup_true_lang-C2T_8RUB.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-fZxfkI-v.js";import"./createLucideIcon-CSZE2I1R.js";const Y={class:"flex items-center justify-between"},H={class:"flex mt-2","aria-label":"Breadcrumb"},K={class:"inline-flex items-center space-x-1 md:space-x-3"},Q={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},W={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},X={class:"py-12"},Z={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},tt={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},et={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},st={class:"p-6"},at={class:"flex items-center"},rt={class:"ml-4"},ot={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},dt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},lt={class:"p-6"},nt={class:"flex items-center"},it={class:"ml-4"},xt={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},gt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},ut={class:"p-6"},yt={class:"flex items-center"},mt={class:"ml-4"},ct={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},pt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},ft={class:"p-6"},vt={class:"flex items-center"},kt={class:"ml-4"},bt={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},_t={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},ht={class:"p-6 text-gray-900 dark:text-gray-100"},wt={key:0,class:"text-center py-8"},At={key:1,class:"overflow-x-auto"},Pt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Ct={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Dt={class:"px-6 py-4 whitespace-nowrap"},$t={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},Mt={class:"text-sm text-gray-500 dark:text-gray-400"},Nt={class:"px-6 py-4 whitespace-nowrap"},Ft={class:"text-sm text-gray-900 dark:text-gray-100"},jt={class:"px-6 py-4 whitespace-nowrap"},Bt={class:"text-sm text-gray-900 dark:text-gray-100"},Ut={class:"px-6 py-4 whitespace-nowrap"},Lt={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},Tt={class:"px-6 py-4 whitespace-nowrap"},zt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},Et={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},It=["onClick","disabled"],Rt={key:0},Vt={key:1},St={key:0,class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"},Gt={class:"p-6"},Ot={key:0,class:"space-y-6"},qt={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},Jt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Yt={class:"text-sm text-gray-900 dark:text-gray-100"},Ht={class:"text-sm font-semibold text-gray-900 dark:text-gray-100"},Kt={class:"text-sm text-gray-900 dark:text-gray-100"},Qt={class:"text-sm text-gray-900 dark:text-gray-100"},Wt={class:"text-sm text-gray-900 dark:text-gray-100"},Xt={key:0,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},Zt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},te={class:"text-sm text-gray-900 dark:text-gray-100"},ee={class:"text-sm text-gray-900 dark:text-gray-100"},se={key:1,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},ae={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},re={class:"text-sm text-gray-900 dark:text-gray-100"},oe={class:"text-sm text-gray-900 dark:text-gray-100"},de={class:"text-sm text-gray-900 dark:text-gray-100"},le={class:"text-sm text-gray-900 dark:text-gray-100"},ne={key:2,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},ie={class:"text-sm text-gray-900 dark:text-gray-100"},xe={key:3,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},ge={class:"space-y-2"},ue={class:"text-sm font-medium text-gray-500 dark:text-gray-400 capitalize"},ye={class:"text-sm text-gray-900 dark:text-gray-100"},me={key:1,class:"text-center py-8"},_e={__name:"Payments",setup(ce){const p=[{title:"Dashboard",href:"/dashboard"},{title:"Payments",href:"/payments"}],f=u(!1),x=u([]),v=u(!1),r=u(null),y=u(!1),$=async()=>{f.value=!0;try{const s=await window.axios.get("/payments-list"),e=s.data.data||s.data||[];x.value=e.map(l=>({...l,amount:parseFloat(l.amount)||0}))}catch(s){console.error("Error fetching payments:",s),x.value=[]}finally{f.value=!1}},A=s=>({paid:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",failed:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",refunded:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"})[s]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",M=m(()=>Array.isArray(x.value)?x.value.reduce((e,l)=>{const n=parseFloat(l.amount)||0;return e+n},0).toFixed(2):"0.00"),N=m(()=>Array.isArray(x.value)?x.value.filter(s=>s.status==="paid").length:0),F=m(()=>Array.isArray(x.value)?x.value.filter(s=>s.status==="pending").length:0),j=m(()=>Array.isArray(x.value)?x.value.filter(s=>s.status==="failed").length:0),B=async s=>{var e,l;y.value=!0;try{const n=await window.axios.get(`/payments-detail/${s}`);r.value=n.data,v.value=!0}catch(n){console.error("Error fetching payment details:",n),((e=n.response)==null?void 0:e.status)===403?alert("You do not have permission to view payment details."):((l=n.response)==null?void 0:l.status)===404?alert("Payment not found."):alert("Error loading payment details. Please try again.")}finally{y.value=!1}},k=()=>{v.value=!1,r.value=null},U=s=>{if(!s)return"N/A";try{return new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}catch{return s}},L=s=>{if(!s)return"N/A";try{return new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}catch{return s}},T=s=>{if(!s)return"N/A";if(typeof s=="string")return s;if(typeof s=="object"&&s.start_time&&s.end_time)return`${s.start_time} - ${s.end_time}`;try{const e=typeof s=="string"?JSON.parse(s):s;if(e.start_time&&e.end_time)return`${e.start_time} - ${e.end_time}`}catch{return s.toString()}return s.toString()},z=(s,e="USD")=>{const l=parseFloat(s)||0,n={USD:"$",GBP:"£",EUR:"€",gbp:"£",usd:"$",eur:"€"};return`${n[e]||n[e==null?void 0:e.toUpperCase()]||"$"}${l.toFixed(2)}`},E=(s,e)=>{if(!s)return"N/A";const l=s.charAt(0).toUpperCase()+s.slice(1);return!e||e==="unknown"?l:`${l} ****${e}`},I=s=>s?{succeeded:"Succeeded",pending:"Pending",failed:"Failed",refunded:"Refunded",paid:"Paid"}[s]||s.charAt(0).toUpperCase()+s.slice(1):"N/A";return R(()=>{$()}),(s,e)=>(o(),d(c,null,[P(C(V),{title:"Payment Management"}),P(J,null,{header:h(()=>[t("div",Y,[t("div",null,[e[2]||(e[2]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Payment Management ",-1)),t("nav",H,[t("ol",K,[(o(),d(c,null,w(p,(l,n)=>t("li",{key:n,class:"inline-flex items-center"},[n<p.length-1?(o(),G(C(O),{key:0,href:l.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:h(()=>[q(a(l.title),1)]),_:2},1032,["href"])):(o(),d("span",Q,a(l.title),1)),n<p.length-1?(o(),d("svg",W,e[1]||(e[1]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):g("",!0)])),64))])])])])]),default:h(()=>{var l,n,b;return[t("div",X,[t("div",Z,[t("div",tt,[t("div",et,[t("div",st,[t("div",at,[e[4]||(e[4]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-dollar-sign text-2xl text-green-500"})],-1)),t("div",rt,[e[3]||(e[3]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Total Revenue",-1)),t("p",ot," $"+a(M.value),1)])])])]),t("div",dt,[t("div",lt,[t("div",nt,[e[6]||(e[6]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-check-circle text-2xl text-green-600"})],-1)),t("div",it,[e[5]||(e[5]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Paid",-1)),t("p",xt,a(N.value),1)])])])]),t("div",gt,[t("div",ut,[t("div",yt,[e[8]||(e[8]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-clock text-2xl text-yellow-500"})],-1)),t("div",mt,[e[7]||(e[7]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Pending",-1)),t("p",ct,a(F.value),1)])])])]),t("div",pt,[t("div",ft,[t("div",vt,[e[10]||(e[10]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-times-circle text-2xl text-red-500"})],-1)),t("div",kt,[e[9]||(e[9]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Failed",-1)),t("p",bt,a(j.value),1)])])])])]),t("div",_t,[t("div",ht,[f.value?(o(),d("div",wt,e[11]||(e[11]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(o(),d("div",At,[t("table",Pt,[e[12]||(e[12]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Transaction "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Patient "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Provider "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Amount "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Date "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),t("tbody",Ct,[(o(!0),d(c,null,w(Array.isArray(x.value)?x.value:[],i=>(o(),d("tr",{key:i.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",Dt,[t("div",$t,a(i.transaction_id),1),t("div",Mt,a(i.payment_method),1)]),t("td",Nt,[t("div",Ft,a(i.patient_name),1)]),t("td",jt,[t("div",Bt,a(i.provider_name),1)]),t("td",Ut,[t("div",Lt," $"+a((parseFloat(i.amount)||0).toFixed(2)),1)]),t("td",Tt,[t("span",{class:D([A(i.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(i.status),3)]),t("td",zt,a(i.date),1),t("td",Et,[t("button",{onClick:_=>B(i.id),disabled:y.value,class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3 disabled:opacity-50 disabled:cursor-not-allowed"},[y.value?(o(),d("span",Rt,"Loading...")):(o(),d("span",Vt,"View"))],8,It),i.status==="paid"?(o(),d("button",St," Refund ")):g("",!0)])]))),128))])])]))])])])]),v.value?(o(),d("div",{key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:k},[t("div",{class:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",onClick:e[0]||(e[0]=S(()=>{},["stop"]))},[t("div",Gt,[t("div",{class:"flex items-center justify-between mb-6"},[e[14]||(e[14]=t("h3",{class:"text-xl font-semibold text-gray-900 dark:text-gray-100"},"Payment Details",-1)),t("button",{onClick:k,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[13]||(e[13]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),r.value?(o(),d("div",Ot,[t("div",qt,[e[21]||(e[21]=t("h4",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-3"},"Transaction Information",-1)),t("div",Jt,[t("div",null,[e[15]||(e[15]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Transaction ID",-1)),t("p",Yt,a(r.value.payment_id),1)]),t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Amount",-1)),t("p",Ht,a(z(r.value.amount,r.value.currency)),1)]),t("div",null,[e[17]||(e[17]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Status",-1)),t("span",{class:D([A(r.value.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(I(r.value.status)),3)]),t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Currency",-1)),t("p",Kt,a(r.value.currency||"USD"),1)]),t("div",null,[e[19]||(e[19]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Payment Method",-1)),t("p",Qt,a(E(r.value.payment_method_type,r.value.payment_method_details)),1)]),t("div",null,[e[20]||(e[20]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Date",-1)),t("p",Wt,a(U(r.value.created_at)),1)])])]),r.value.user?(o(),d("div",Xt,[e[24]||(e[24]=t("h4",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-3"},"Patient Information",-1)),t("div",Zt,[t("div",null,[e[22]||(e[22]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Name",-1)),t("p",te,a(r.value.user.name),1)]),t("div",null,[e[23]||(e[23]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Email",-1)),t("p",ee,a(r.value.user.email),1)])])])):g("",!0),r.value.appointment?(o(),d("div",se,[e[29]||(e[29]=t("h4",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-3"},"Appointment Information",-1)),t("div",ae,[t("div",null,[e[25]||(e[25]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Provider",-1)),t("p",re,a(((n=(l=r.value.appointment.provider)==null?void 0:l.user)==null?void 0:n.name)||"N/A"),1)]),t("div",null,[e[26]||(e[26]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Service",-1)),t("p",oe,a(((b=r.value.appointment.service)==null?void 0:b.name)||"General Consultation"),1)]),t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Date",-1)),t("p",de,a(L(r.value.appointment.date)),1)]),t("div",null,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Time",-1)),t("p",le,a(T(r.value.appointment.time_slot)),1)])])])):g("",!0),r.value.description?(o(),d("div",ne,[e[30]||(e[30]=t("h4",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-3"},"Description",-1)),t("p",ie,a(r.value.description),1)])):g("",!0),r.value.metadata&&Object.keys(r.value.metadata).length>0?(o(),d("div",xe,[e[31]||(e[31]=t("h4",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-3"},"Additional Information",-1)),t("div",ge,[(o(!0),d(c,null,w(r.value.metadata,(i,_)=>(o(),d("div",{key:_,class:"flex justify-between"},[t("span",ue,a(_.replace("_"," "))+":",1),t("span",ye,a(i),1)]))),128))])])):g("",!0)])):(o(),d("div",me,e[32]||(e[32]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1),t("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Loading payment details...",-1)]))),t("div",{class:"mt-6 flex justify-end"},[t("button",{onClick:k,class:"px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"}," Close ")])])])])):g("",!0)]}),_:1})],64))}};export{_e as default};
