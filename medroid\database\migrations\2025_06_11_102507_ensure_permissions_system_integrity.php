<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * This migration ensures the permissions system integrity for production deployments.
     * It's safe to run multiple times and will only fix issues without breaking existing data.
     */
    public function up(): void
    {
        // Only run if we have permission tables
        if (!Schema::hasTable('roles') || !Schema::hasTable('permissions')) {
            return;
        }

        // Ensure all required roles exist
        $this->ensureRolesExist();

        // Ensure permissions exist (only if none exist)
        $this->ensurePermissionsExist();

        // Assign roles to users who don't have them
        $this->assignMissingRoles();

        // Clear permission cache
        $this->clearPermissionCache();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is safe and doesn't need to be reversed
        // as it only ensures data integrity
    }

    /**
     * Ensure all required roles exist
     */
    private function ensureRolesExist(): void
    {
        $roles = ['admin', 'manager', 'provider', 'patient'];

        foreach ($roles as $roleName) {
            Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'web'
            ]);
        }
    }

    /**
     * Ensure permissions exist by running seeder if no permissions found
     */
    private function ensurePermissionsExist(): void
    {
        $permissionCount = Permission::count();

        if ($permissionCount === 0) {
            // Run the seeder to create permissions and assign them to roles
            \Artisan::call('db:seed', ['--class' => 'RolesAndPermissionsSeeder']);
        }
    }

    /**
     * Assign roles to users who don't have Spatie roles but have role column
     */
    private function assignMissingRoles(): void
    {
        $users = User::whereDoesntHave('roles')
                    ->whereNotNull('role')
                    ->get();

        foreach ($users as $user) {
            // Only assign if the role exists
            if (Role::where('name', $user->role)->exists()) {
                $user->assignRole($user->role);
            }
        }
    }

    /**
     * Clear permission cache
     */
    private function clearPermissionCache(): void
    {
        try {
            app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
        } catch (\Exception $e) {
            // Ignore cache clearing errors
        }
    }
};
