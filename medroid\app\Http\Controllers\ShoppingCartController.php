<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ShoppingCart;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ShoppingCartController extends Controller
{
    public function index(Request $request)
    {
        $cartItems = ShoppingCart::with(['product.category', 'product.images'])
            ->forUser(Auth::id())
            ->get();

        $cartTotal = ShoppingCart::getCartTotal(Auth::id());
        $cartCount = ShoppingCart::getCartCount(Auth::id());

        if ($request->expectsJson()) {
            return response()->json([
                'cart_items' => $cartItems,
                'cart_total' => $cartTotal,
                'cart_count' => $cartCount,
                'formatted_total' => '$' . number_format($cartTotal, 2),
            ]);
        }

        return Inertia::render('Shop/Cart', [
            'cartItems' => $cartItems,
            'cartTotal' => $cartTotal,
            'cartCount' => $cartCount,
        ]);
    }

    public function add(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'integer|min:1|max:100',
            'options' => 'array',
        ]);

        try {
            $product = Product::findOrFail($request->product_id);
            
            if (!$product->canPurchase()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product is not available for purchase',
                ], 400);
            }

            $quantity = $request->get('quantity', 1);
            $options = $request->get('options', []);

            // Check stock for physical products
            if ($product->isPhysical() && $product->manage_stock) {
                $existingCartItem = ShoppingCart::where('user_id', Auth::id())
                    ->where('product_id', $product->id)
                    ->first();
                
                $currentCartQuantity = $existingCartItem ? $existingCartItem->quantity : 0;
                $totalQuantity = $currentCartQuantity + $quantity;
                
                if ($totalQuantity > $product->stock_quantity) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Not enough stock available',
                        'available_stock' => $product->stock_quantity - $currentCartQuantity,
                    ], 400);
                }
            }

            $cartItem = ShoppingCart::addItem(Auth::id(), $product->id, $quantity, $options);

            $cartTotal = ShoppingCart::getCartTotal(Auth::id());
            $cartCount = ShoppingCart::getCartCount(Auth::id());

            return response()->json([
                'success' => true,
                'message' => 'Product added to cart',
                'cart_item' => $cartItem->load(['product.category', 'product.images']),
                'cart_total' => $cartTotal,
                'cart_count' => $cartCount,
                'formatted_total' => '$' . number_format($cartTotal, 2),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function update(Request $request, $productId)
    {
        $request->validate([
            'quantity' => 'required|integer|min:0|max:100',
        ]);

        try {
            $product = Product::findOrFail($productId);
            $quantity = $request->quantity;

            // Check stock for physical products
            if ($quantity > 0 && $product->isPhysical() && $product->manage_stock) {
                if ($quantity > $product->stock_quantity) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Not enough stock available',
                        'available_stock' => $product->stock_quantity,
                    ], 400);
                }
            }

            $cartItem = ShoppingCart::updateQuantity(Auth::id(), $productId, $quantity);

            $cartTotal = ShoppingCart::getCartTotal(Auth::id());
            $cartCount = ShoppingCart::getCartCount(Auth::id());

            return response()->json([
                'success' => true,
                'message' => $quantity > 0 ? 'Cart updated' : 'Item removed from cart',
                'cart_item' => $cartItem ? $cartItem->load(['product.category', 'product.images']) : null,
                'cart_total' => $cartTotal,
                'cart_count' => $cartCount,
                'formatted_total' => '$' . number_format($cartTotal, 2),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function remove(Request $request, $productId)
    {
        try {
            ShoppingCart::removeItem(Auth::id(), $productId);

            $cartTotal = ShoppingCart::getCartTotal(Auth::id());
            $cartCount = ShoppingCart::getCartCount(Auth::id());

            return response()->json([
                'success' => true,
                'message' => 'Item removed from cart',
                'cart_total' => $cartTotal,
                'cart_count' => $cartCount,
                'formatted_total' => '$' . number_format($cartTotal, 2),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function clear(Request $request)
    {
        try {
            ShoppingCart::clearCart(Auth::id());

            return response()->json([
                'success' => true,
                'message' => 'Cart cleared',
                'cart_total' => 0,
                'cart_count' => 0,
                'formatted_total' => '$0.00',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function count(Request $request)
    {
        $cartCount = ShoppingCart::getCartCount(Auth::id());
        
        return response()->json([
            'cart_count' => $cartCount,
        ]);
    }
}
