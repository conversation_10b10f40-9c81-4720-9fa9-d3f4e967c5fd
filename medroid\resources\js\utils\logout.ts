/**
 * Logout Utilities
 * 
 * This module provides utilities for handling logout operations
 * across different authentication methods (session-based and token-based)
 */

import { router } from '@inertiajs/vue3';
import axios from 'axios';

/**
 * Simple logout handler for web sessions
 * Uses Inertia.js for clean logout
 */
export const performLogout = async (): Promise<void> => {
    try {
        console.log('Starting logout process...');

        // Clear any cached data first
        clearLocalData();

        // Use Inertia router for session logout
        router.post('/logout', {}, {
            onSuccess: () => {
                console.log('Logout successful');
                // Redirect will be handled by server
            },
            onError: (errors) => {
                console.log('Logout completed with minor errors (expected)');
                // Force redirect to ensure logout
                window.location.href = '/';
            },
            onFinish: () => {
                console.log('Logout process finished');
            }
        });

    } catch (error) {
        console.log('Logout error, forcing redirect:', error);
        // Force redirect on any error
        window.location.href = '/';
    }
};

/**
 * Clear local data (localStorage, sessionStorage, etc.)
 */
export const clearLocalData = (): void => {
    try {
        // Clear any localStorage items
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && (key.includes('auth') || key.includes('token') || key.includes('user'))) {
                keysToRemove.push(key);
            }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key));
        
        // Clear any sessionStorage items
        const sessionKeysToRemove = [];
        for (let i = 0; i < sessionStorage.length; i++) {
            const key = sessionStorage.key(i);
            if (key && (key.includes('auth') || key.includes('token') || key.includes('user'))) {
                sessionKeysToRemove.push(key);
            }
        }
        sessionKeysToRemove.forEach(key => sessionStorage.removeItem(key));
        
        console.log('Local data cleared');
    } catch (error) {
        console.error('Error clearing local data:', error);
    }
};

/**
 * Simple logout for Inertia components
 * Just clears local data and lets the router handle the rest
 */
export const handleInertiaLogout = (): void => {
    try {
        // Clear local data
        clearLocalData();
        console.log('Local data cleared for logout');
    } catch (error) {
        console.log('Error clearing local data (continuing with logout):', error);
    }
};

/**
 * Force logout (for when normal logout fails)
 */
export const forceLogout = (): void => {
    console.log('Forcing logout...');
    clearLocalData();
    
    // Clear all cookies by setting them to expire
    document.cookie.split(";").forEach((c) => {
        const eqPos = c.indexOf("=");
        const name = eqPos > -1 ? c.substr(0, eqPos) : c;
        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=" + window.location.hostname;
    });
    
    // Force redirect
    window.location.href = '/';
};