<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShoppingCart extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'product_id',
        'quantity',
        'price',
        'product_options',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'price' => 'decimal:2',
        'product_options' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'total_price',
        'formatted_total_price',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function getTotalPriceAttribute()
    {
        return $this->quantity * $this->price;
    }

    public function getFormattedTotalPriceAttribute()
    {
        return '$' . number_format($this->total_price, 2);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public static function addItem($userId, $productId, $quantity = 1, $options = [])
    {
        $product = Product::findOrFail($productId);
        
        if (!$product->canPurchase()) {
            throw new \Exception('Product is not available for purchase');
        }

        $existingItem = static::where('user_id', $userId)
            ->where('product_id', $productId)
            ->first();

        if ($existingItem) {
            $existingItem->quantity += $quantity;
            $existingItem->price = $product->effective_price;
            $existingItem->product_options = $options;
            $existingItem->save();
            return $existingItem;
        }

        return static::create([
            'user_id' => $userId,
            'product_id' => $productId,
            'quantity' => $quantity,
            'price' => $product->effective_price,
            'product_options' => $options,
        ]);
    }

    public static function updateQuantity($userId, $productId, $quantity)
    {
        $item = static::where('user_id', $userId)
            ->where('product_id', $productId)
            ->firstOrFail();

        if ($quantity <= 0) {
            $item->delete();
            return null;
        }

        $item->quantity = $quantity;
        $item->save();
        return $item;
    }

    public static function removeItem($userId, $productId)
    {
        return static::where('user_id', $userId)
            ->where('product_id', $productId)
            ->delete();
    }

    public static function clearCart($userId)
    {
        return static::where('user_id', $userId)->delete();
    }

    public static function getCartTotal($userId)
    {
        return static::where('user_id', $userId)
            ->get()
            ->sum('total_price');
    }

    public static function getCartCount($userId)
    {
        return static::where('user_id', $userId)
            ->sum('quantity');
    }
}
