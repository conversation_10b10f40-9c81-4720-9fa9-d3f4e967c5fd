import{N as k,r as O,G as ke,Q as C,u as t,o as Y,y as h,e as p,g as r,S as D,j as V,af as Ce,f as l,a7 as he,a8 as $e,c as T,i as s,d as _,s as ae,V as ne,x as v,n as Pe,m as De,z as ee,P as Be,ak as Ve,l as S,U as Me,t as F,F as j,q as R,p as Oe,v as te,M as q,A as se,a as oe}from"./vendor-DkZiYBIF.js";import{_ as H}from"./HeadingSmall.vue_vue_type_script_setup_true_lang-DIBhJu7Q.js";import{_ as G}from"./InputError.vue_vue_type_script_setup_true_lang-CSFrrJiY.js";import{_ as L}from"./index-DNsgB9iJ.js";import{c as Fe,u as X,g as le,e as Ee,k as Ie,_ as Ae,l as Q,d as Le,P as re,b as ze,h as ie,a as de}from"./useBodyScrollLock-CEKoJB3U.js";import{c as Se,b as I,g as Ue,_ as B,a as M}from"./Label.vue_vue_type_script_setup_true_lang-CbHOsuTJ.js";import{P as K,c as U}from"./Primitive-fZxfkI-v.js";import{c as je}from"./createLucideIcon-CSZE2I1R.js";import{_ as Re}from"./AppLayout.vue_vue_type_script_setup_true_lang-C2T_8RUB.js";import{_ as Te}from"./Layout.vue_vue_type_script_setup_true_lang-BM08YvWd.js";import"./index-CM-BHwaf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ke=je("XIcon",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),[A,Ne]=Fe("DialogRoot"),qe=k({inheritAttrs:!1,__name:"DialogRoot",props:{open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:!1},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(c,{emit:d}){const o=c,a=Se(o,"open",d,{defaultValue:o.defaultOpen,passive:o.open===void 0}),m=O(),f=O(),{modal:g}=ke(o);return Ne({open:a,modal:g,openModal:()=>{a.value=!0},onOpenChange:b=>{a.value=b},onOpenToggle:()=>{a.value=!a.value},contentId:"",titleId:"",descriptionId:"",triggerElement:m,contentElement:f}),(b,y)=>C(b.$slots,"default",{open:t(a)})}}),He=k({__name:"DialogTrigger",props:{asChild:{type:Boolean},as:{default:"button"}},setup(c){const d=c,o=A(),{forwardRef:u,currentElement:a}=I();return o.contentId||(o.contentId=X(void 0,"reka-dialog-content")),Y(()=>{o.triggerElement.value=a.value}),(m,f)=>(p(),h(t(K),D(d,{ref:t(u),type:m.as==="button"?"button":void 0,"aria-haspopup":"dialog","aria-expanded":t(o).open.value||!1,"aria-controls":t(o).open.value?t(o).contentId:void 0,"data-state":t(o).open.value?"open":"closed",onClick:t(o).onOpenToggle}),{default:r(()=>[C(m.$slots,"default")]),_:3},16,["type","aria-expanded","aria-controls","data-state","onClick"]))}}),Ge=k({__name:"Teleport",props:{to:{default:"body"},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(c){const d=Ue();return(o,u)=>t(d)||o.forceMount?(p(),h(Ce,{key:0,to:o.to,disabled:o.disabled,defer:o.defer},[C(o.$slots,"default")],8,["to","disabled","defer"])):V("",!0)}}),ue=k({__name:"DialogContentImpl",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(c,{emit:d}){const o=c,u=d,a=A(),{forwardRef:m,currentElement:f}=I();return a.titleId||(a.titleId=X(void 0,"reka-dialog-title")),a.descriptionId||(a.descriptionId=X(void 0,"reka-dialog-description")),Y(()=>{a.contentElement=f,le()!==document.body&&(a.triggerElement.value=le())}),(g,b)=>(p(),h(t(Ae),{"as-child":"",loop:"",trapped:o.trapFocus,onMountAutoFocus:b[5]||(b[5]=y=>u("openAutoFocus",y)),onUnmountAutoFocus:b[6]||(b[6]=y=>u("closeAutoFocus",y))},{default:r(()=>[l(t(Ee),D({id:t(a).contentId,ref:t(m),as:g.as,"as-child":g.asChild,"disable-outside-pointer-events":g.disableOutsidePointerEvents,role:"dialog","aria-describedby":t(a).descriptionId,"aria-labelledby":t(a).titleId,"data-state":t(Ie)(t(a).open.value)},g.$attrs,{onDismiss:b[0]||(b[0]=y=>t(a).onOpenChange(!1)),onEscapeKeyDown:b[1]||(b[1]=y=>u("escapeKeyDown",y)),onFocusOutside:b[2]||(b[2]=y=>u("focusOutside",y)),onInteractOutside:b[3]||(b[3]=y=>u("interactOutside",y)),onPointerDownOutside:b[4]||(b[4]=y=>u("pointerDownOutside",y))}),{default:r(()=>[C(g.$slots,"default")]),_:3},16,["id","as","as-child","disable-outside-pointer-events","aria-describedby","aria-labelledby","data-state"])]),_:3},8,["trapped"]))}}),Xe=k({__name:"DialogContentModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(c,{emit:d}){const o=c,u=d,a=A(),m=Q(u),{forwardRef:f,currentElement:g}=I();return Le(g),(b,y)=>(p(),h(ue,D({...o,...t(m)},{ref:t(f),"trap-focus":t(a).open.value,"disable-outside-pointer-events":!0,onCloseAutoFocus:y[0]||(y[0]=i=>{var $;i.defaultPrevented||(i.preventDefault(),($=t(a).triggerElement.value)==null||$.focus())}),onPointerDownOutside:y[1]||(y[1]=i=>{const $=i.detail.originalEvent,z=$.button===0&&$.ctrlKey===!0;($.button===2||z)&&i.preventDefault()}),onFocusOutside:y[2]||(y[2]=i=>{i.preventDefault()})}),{default:r(()=>[C(b.$slots,"default")]),_:3},16,["trap-focus"]))}}),Ye=k({__name:"DialogContentNonModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(c,{emit:d}){const o=c,a=Q(d);I();const m=A(),f=O(!1),g=O(!1);return(b,y)=>(p(),h(ue,D({...o,...t(a)},{"trap-focus":!1,"disable-outside-pointer-events":!1,onCloseAutoFocus:y[0]||(y[0]=i=>{var $;i.defaultPrevented||(f.value||($=t(m).triggerElement.value)==null||$.focus(),i.preventDefault()),f.value=!1,g.value=!1}),onInteractOutside:y[1]||(y[1]=i=>{var E;i.defaultPrevented||(f.value=!0,i.detail.originalEvent.type==="pointerdown"&&(g.value=!0));const $=i.target;((E=t(m).triggerElement.value)==null?void 0:E.contains($))&&i.preventDefault(),i.detail.originalEvent.type==="focusin"&&g.value&&i.preventDefault()})}),{default:r(()=>[C(b.$slots,"default")]),_:3},16))}}),Qe=k({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(c,{emit:d}){const o=c,u=d,a=A(),m=Q(u),{forwardRef:f}=I();return(g,b)=>(p(),h(t(re),{present:g.forceMount||t(a).open.value},{default:r(()=>[t(a).modal.value?(p(),h(Xe,D({key:0,ref:t(f)},{...o,...t(m),...g.$attrs}),{default:r(()=>[C(g.$slots,"default")]),_:3},16)):(p(),h(Ye,D({key:1,ref:t(f)},{...o,...t(m),...g.$attrs}),{default:r(()=>[C(g.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),We=k({__name:"DialogOverlayImpl",props:{asChild:{type:Boolean},as:{}},setup(c){const d=A();return ze(!0),I(),(o,u)=>(p(),h(t(K),{as:o.as,"as-child":o.asChild,"data-state":t(d).open.value?"open":"closed",style:{"pointer-events":"auto"}},{default:r(()=>[C(o.$slots,"default")]),_:3},8,["as","as-child","data-state"]))}}),Je=k({__name:"DialogOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(c){const d=A(),{forwardRef:o}=I();return(u,a)=>{var m;return(m=t(d))!=null&&m.modal.value?(p(),h(t(re),{key:0,present:u.forceMount||t(d).open.value},{default:r(()=>[l(We,D(u.$attrs,{ref:t(o),as:u.as,"as-child":u.asChild}),{default:r(()=>[C(u.$slots,"default")]),_:3},16,["as","as-child"])]),_:3},8,["present"])):V("",!0)}}}),pe=k({__name:"DialogClose",props:{asChild:{type:Boolean},as:{default:"button"}},setup(c){const d=c;I();const o=A();return(u,a)=>(p(),h(t(K),D(d,{type:u.as==="button"?"button":void 0,onClick:a[0]||(a[0]=m=>t(o).onOpenChange(!1))}),{default:r(()=>[C(u.$slots,"default")]),_:3},16,["type"]))}}),Ze=k({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{default:"h2"}},setup(c){const d=c,o=A();return I(),(u,a)=>(p(),h(t(K),D(d,{id:t(o).titleId}),{default:r(()=>[C(u.$slots,"default")]),_:3},16,["id"]))}}),et=k({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{default:"p"}},setup(c){const d=c;I();const o=A();return(u,a)=>(p(),h(t(K),D(d,{id:t(o).descriptionId}),{default:r(()=>[C(u.$slots,"default")]),_:3},16,["id"]))}}),tt=k({__name:"DialogPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(c){const d=c;return(o,u)=>(p(),h(t(Ge),he($e(d)),{default:r(()=>[C(o.$slots,"default")]),_:3},16))}}),st=k({__name:"Dialog",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(c,{emit:d}){const a=ie(c,d);return(m,f)=>(p(),h(t(qe),D({"data-slot":"dialog"},t(a)),{default:r(()=>[C(m.$slots,"default")]),_:3},16))}}),ot=k({__name:"DialogClose",props:{asChild:{type:Boolean},as:{}},setup(c){const d=c;return(o,u)=>(p(),h(t(pe),D({"data-slot":"dialog-close"},d),{default:r(()=>[C(o.$slots,"default")]),_:3},16))}}),lt=k({__name:"DialogOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(c){const d=c,o=T(()=>{const{class:u,...a}=d;return a});return(u,a)=>(p(),h(t(Je),D({"data-slot":"dialog-overlay"},o.value,{class:t(U)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-white/20 backdrop-blur-sm",d.class)}),{default:r(()=>[C(u.$slots,"default")]),_:3},16,["class"]))}}),at=k({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(c,{emit:d}){const o=c,u=d,a=T(()=>{const{class:f,...g}=o;return g}),m=ie(a,u);return(f,g)=>(p(),h(t(tt),null,{default:r(()=>[l(lt),l(t(Qe),D({"data-slot":"dialog-content"},t(m),{class:t(U)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",o.class)}),{default:r(()=>[C(f.$slots,"default"),l(t(pe),{class:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4"},{default:r(()=>[l(t(Ke)),g[0]||(g[0]=s("span",{class:"sr-only"},"Close",-1))]),_:1})]),_:3},16,["class"])]),_:3}))}}),nt=k({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(c){const d=c,o=T(()=>{const{class:a,...m}=d;return m}),u=de(o);return(a,m)=>(p(),h(t(et),D({"data-slot":"dialog-description"},t(u),{class:t(U)("text-muted-foreground text-sm",d.class)}),{default:r(()=>[C(a.$slots,"default")]),_:3},16,["class"]))}}),rt=k({__name:"DialogFooter",props:{class:{}},setup(c){const d=c;return(o,u)=>(p(),_("div",{"data-slot":"dialog-footer",class:ae(t(U)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",d.class))},[C(o.$slots,"default")],2))}}),it=k({__name:"DialogHeader",props:{class:{}},setup(c){const d=c;return(o,u)=>(p(),_("div",{"data-slot":"dialog-header",class:ae(t(U)("flex flex-col gap-2 text-center sm:text-left",d.class))},[C(o.$slots,"default")],2))}}),dt=k({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(c){const d=c,o=T(()=>{const{class:a,...m}=d;return m}),u=de(o);return(a,m)=>(p(),h(t(Ze),D({"data-slot":"dialog-title"},t(u),{class:t(U)("text-lg leading-none font-semibold",d.class)}),{default:r(()=>[C(a.$slots,"default")]),_:3},16,["class"]))}}),ut=k({__name:"DialogTrigger",props:{asChild:{type:Boolean},as:{}},setup(c){const d=c;return(o,u)=>(p(),h(t(He),D({"data-slot":"dialog-trigger"},d),{default:r(()=>[C(o.$slots,"default")]),_:3},16))}}),pt={class:"space-y-6"},ct={class:"space-y-4 rounded-lg border border-red-100 bg-red-50 p-4 dark:border-red-200/10 dark:bg-red-700/10"},ft={class:"grid gap-2"},mt=k({__name:"DeleteUser",setup(c){const d=O(null),o=ne({password:""}),u=m=>{m.preventDefault(),o.delete(route("profile.destroy"),{preserveScroll:!0,onSuccess:()=>a(),onError:()=>{var f;return(f=d.value)==null?void 0:f.focus()},onFinish:()=>o.reset()})},a=()=>{o.clearErrors(),o.reset()};return(m,f)=>(p(),_("div",pt,[l(H,{title:"Delete account",description:"Delete your account and all of its resources"}),s("div",ct,[f[7]||(f[7]=s("div",{class:"relative space-y-0.5 text-red-600 dark:text-red-100"},[s("p",{class:"font-medium"},"Warning"),s("p",{class:"text-sm"},"Please proceed with caution, this cannot be undone.")],-1)),l(t(st),null,{default:r(()=>[l(t(ut),{"as-child":""},{default:r(()=>[l(t(L),{variant:"destructive"},{default:r(()=>f[1]||(f[1]=[v("Delete account")])),_:1})]),_:1}),l(t(at),null,{default:r(()=>[s("form",{class:"space-y-6",onSubmit:u},[l(t(it),{class:"space-y-3"},{default:r(()=>[l(t(dt),null,{default:r(()=>f[2]||(f[2]=[v("Are you sure you want to delete your account?")])),_:1}),l(t(nt),null,{default:r(()=>f[3]||(f[3]=[v(" Once your account is deleted, all of its resources and data will also be permanently deleted. Please enter your password to confirm you would like to permanently delete your account. ")])),_:1})]),_:1}),s("div",ft,[l(t(B),{for:"password",class:"sr-only"},{default:r(()=>f[4]||(f[4]=[v("Password")])),_:1}),l(t(M),{id:"password",type:"password",name:"password",ref_key:"passwordInput",ref:d,modelValue:t(o).password,"onUpdate:modelValue":f[0]||(f[0]=g=>t(o).password=g),placeholder:"Password"},null,8,["modelValue"]),l(G,{message:t(o).errors.password},null,8,["message"])]),l(t(rt),{class:"gap-2"},{default:r(()=>[l(t(ot),{"as-child":""},{default:r(()=>[l(t(L),{variant:"secondary",onClick:a},{default:r(()=>f[5]||(f[5]=[v(" Cancel ")])),_:1})]),_:1}),l(t(L),{variant:"destructive",disabled:t(o).processing},{default:r(()=>f[6]||(f[6]=[s("button",{type:"submit"},"Delete account",-1)])),_:1},8,["disabled"])]),_:1})],32)]),_:1})]),_:1})])]))}}),vt={class:"flex flex-col space-y-6"},gt={class:"grid gap-2"},yt={class:"grid gap-2"},_t={key:0},bt={class:"-mt-4 text-sm text-muted-foreground"},xt={key:0,class:"mt-2 text-sm font-medium text-green-600"},wt={class:"flex items-center gap-4"},kt={class:"text-sm text-neutral-600"},Ct={key:0,class:"bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-6"},ht={class:"flex items-center space-x-4"},$t={class:"flex-1"},Pt={key:0,class:"mt-2 space-y-1"},Dt={class:"text-sm text-purple-600"},Bt={class:"text-sm text-purple-600"},Vt={class:"text-sm text-purple-600"},Mt={key:1,class:"mt-2"},Ot={key:1,class:"flex flex-col space-y-6"},Ft={key:0,class:"flex items-center justify-center py-8"},Et={key:1,class:"bg-red-50 border border-red-200 rounded-lg p-4"},It={class:"flex"},At={class:"ml-3"},Lt={class:"text-sm text-red-800"},zt={key:2,class:"bg-green-50 border border-green-200 rounded-lg p-4"},St={class:"flex"},Ut={class:"ml-3"},jt={class:"text-sm text-green-800"},Rt={key:3},Tt={class:"bg-white rounded-lg border border-gray-200 p-6"},Kt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Nt=["value"],qt={class:"mt-4"},Ht={class:"mt-4"},Gt={class:"bg-white rounded-lg border border-gray-200 p-6"},Xt={class:"flex flex-wrap gap-2 mb-4"},Yt=["onClick"],Qt={class:"flex gap-2"},Wt={class:"bg-white rounded-lg border border-gray-200 p-6"},Jt={class:"border border-gray-200 rounded-lg p-4 mb-4"},Zt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},es={class:"md:col-span-2"},ts={class:"flex items-center"},ss={key:0,class:"space-y-3"},os={class:"flex-1"},ls={class:"flex items-center"},as={class:"text-sm font-medium text-gray-900"},ns={key:0,class:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},rs={class:"text-sm text-gray-600"},is={class:"flex items-center space-x-2"},ds=["onClick"],us=["onClick"],ps={key:1,class:"text-center py-4 text-gray-500"},cs={class:"bg-white rounded-lg border border-gray-200 p-6"},fs={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6"},ms={class:"mb-4"},vs={class:"flex items-center mb-3"},gs={key:0},ys={class:"flex gap-2 mb-3"},_s={key:0,class:"flex flex-wrap gap-2"},bs=["onClick"],xs={class:"bg-white rounded-lg border border-gray-200 p-6"},ws={class:"space-y-2 mb-4"},ks={class:"text-sm"},Cs=["onClick"],hs={class:"flex gap-2"},$s={class:"flex justify-end pt-6 border-t"},Ps={key:0,class:"flex items-center"},Ds={key:1},js=k({__name:"Profile",props:{mustVerifyEmail:{type:Boolean},status:{}},setup(c){const d=[{title:"Profile settings",href:"/settings/profile"}],u=Pe().props.auth.user,a=ne({name:u.name,email:u.email}),m=T(()=>u.role==="provider"),f=O(!1),g=O(!1),b=O(null),y=O(""),i=O({specialization:"",bio:"",education:"",license_number:"",gender:"",languages:[],practice_locations:[],accepts_insurance:!1,insurance_providers:[],pricing:{consultation:0,follow_up:0},certifications:[]}),$=O(""),z=O(""),E=O(""),P=O({address:"",city:"",state:"",zip_code:"",coordinates:[0,0],is_primary:!1}),ce=[{value:"male",label:"Male"},{value:"female",label:"Female"},{value:"other",label:"Other"}],fe=()=>{a.patch(route("profile.update"),{preserveScroll:!0})},me=async()=>{var w,e;if(m.value){f.value=!0;try{const n=await oe.get("/provider/get-profile");if(n.data){const x=n.data;i.value={specialization:x.specialization||"",bio:x.bio||"",education:x.education||"",license_number:x.license_number||"",gender:x.gender||"",languages:x.languages||[],practice_locations:x.practice_locations||[],accepts_insurance:x.accepts_insurance||!1,insurance_providers:x.insurance_providers||[],pricing:{consultation:((w=x.pricing)==null?void 0:w.consultation)||x.consultation_fee||0,follow_up:((e=x.pricing)==null?void 0:e.follow_up)||0},certifications:x.certifications||[]}}}catch(n){console.error("Error fetching provider profile:",n),b.value="Failed to load provider profile data"}finally{f.value=!1}}},ve=async()=>{var w,e;g.value=!0,b.value=null,y.value="";try{await oe.post("/provider/profile",i.value),y.value="Provider profile updated successfully!",setTimeout(()=>{y.value=""},3e3)}catch(n){console.error("Error saving provider profile:",n),b.value=((e=(w=n.response)==null?void 0:w.data)==null?void 0:e.message)||"Failed to save provider profile"}finally{g.value=!1}},W=()=>{$.value.trim()&&!i.value.languages.includes($.value.trim())&&(i.value.languages.push($.value.trim()),$.value="")},ge=w=>{i.value.languages.splice(w,1)},J=()=>{z.value.trim()&&(i.value.certifications.push(z.value.trim()),z.value="")},ye=w=>{i.value.certifications.splice(w,1)},_e=()=>{P.value.address.trim()&&(i.value.practice_locations.push({address:P.value.address.trim(),city:P.value.city.trim(),state:P.value.state.trim(),zip_code:P.value.zip_code.trim(),coordinates:P.value.coordinates,is_primary:P.value.is_primary||i.value.practice_locations.length===0}),P.value={address:"",city:"",state:"",zip_code:"",coordinates:[0,0],is_primary:!1})},be=w=>{i.value.practice_locations.splice(w,1)},xe=w=>{i.value.practice_locations.forEach((e,n)=>{e.is_primary=n===w})},Z=()=>{E.value.trim()&&!i.value.insurance_providers.includes(E.value.trim())&&(i.value.insurance_providers.push(E.value.trim()),E.value="")},we=w=>{i.value.insurance_providers.splice(w,1)};return Y(()=>{m.value&&me(),fetchFounderClubInfo()}),(w,e)=>(p(),h(Re,{breadcrumbs:d},{default:r(()=>[l(t(De),{title:"Profile settings"}),l(Te,null,{default:r(()=>[s("div",vt,[l(H,{title:"Profile information",description:"Update your name and email address"}),s("form",{onSubmit:ee(fe,["prevent"]),class:"space-y-6"},[s("div",gt,[l(t(B),{for:"name"},{default:r(()=>e[18]||(e[18]=[v("Name")])),_:1}),l(t(M),{id:"name",class:"mt-1 block w-full",modelValue:t(a).name,"onUpdate:modelValue":e[0]||(e[0]=n=>t(a).name=n),required:"",autocomplete:"name",placeholder:"Full name"},null,8,["modelValue"]),l(G,{class:"mt-2",message:t(a).errors.name},null,8,["message"])]),s("div",yt,[l(t(B),{for:"email"},{default:r(()=>e[19]||(e[19]=[v("Email address")])),_:1}),l(t(M),{id:"email",type:"email",class:"mt-1 block w-full",modelValue:t(a).email,"onUpdate:modelValue":e[1]||(e[1]=n=>t(a).email=n),required:"",autocomplete:"username",placeholder:"Email address"},null,8,["modelValue"]),l(G,{class:"mt-2",message:t(a).errors.email},null,8,["message"])]),w.mustVerifyEmail&&!t(u).email_verified_at?(p(),_("div",_t,[s("p",bt,[e[21]||(e[21]=v(" Your email address is unverified. ")),l(t(Be),{href:w.route("verification.send"),method:"post",as:"button",class:"text-foreground underline decoration-neutral-300 underline-offset-4 transition-colors duration-300 ease-out hover:decoration-current! dark:decoration-neutral-500"},{default:r(()=>e[20]||(e[20]=[v(" Click here to resend the verification email. ")])),_:1},8,["href"])]),w.status==="verification-link-sent"?(p(),_("div",xt," A new verification link has been sent to your email address. ")):V("",!0)])):V("",!0),s("div",wt,[l(t(L),{disabled:t(a).processing},{default:r(()=>e[22]||(e[22]=[v("Save")])),_:1},8,["disabled"]),l(Ve,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:r(()=>[S(s("p",kt,"Saved.",512),[[Me,t(a).recentlySuccessful]])]),_:1})])],32)]),t(u).is_founder_member?(p(),_("div",Ct,[s("div",ht,[e[29]||(e[29]=s("div",{class:"flex-shrink-0"},[s("div",{class:"w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full flex items-center justify-center"},[s("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"})])])],-1)),s("div",$t,[e[27]||(e[27]=s("h3",{class:"text-lg font-semibold text-purple-900"},"Medroid's Founders' Club",-1)),e[28]||(e[28]=s("p",{class:"text-purple-700 text-sm"},"You're part of our exclusive founders community!",-1)),w.founderClubInfo&&!w.loadingClubInfo?(p(),_("div",Pt,[s("p",Dt,[e[23]||(e[23]=s("span",{class:"font-medium"},"Membership Level:",-1)),v(" "+F(w.founderClubInfo.membership_level),1)]),s("p",Bt,[e[24]||(e[24]=s("span",{class:"font-medium"},"Founder Code:",-1)),v(" "+F(w.founderClubInfo.founder_code_used),1)]),s("p",Vt,[e[25]||(e[25]=s("span",{class:"font-medium"},"Member Since:",-1)),v(" "+F(new Date(w.founderClubInfo.joined_at).toLocaleDateString()),1)])])):w.loadingClubInfo?(p(),_("div",Mt,e[26]||(e[26]=[s("div",{class:"animate-pulse flex space-x-2"},[s("div",{class:"h-3 bg-purple-200 rounded w-24"}),s("div",{class:"h-3 bg-purple-200 rounded w-16"})],-1)]))):V("",!0)]),e[30]||(e[30]=s("div",{class:"flex-shrink-0"},[s("span",{class:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800"}," Founder Member ")],-1))])])):V("",!0),m.value?(p(),_("div",Ot,[f.value?(p(),_("div",Ft,e[31]||(e[31]=[s("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),s("span",{class:"ml-2 text-gray-600"},"Loading provider settings...",-1)]))):V("",!0),b.value?(p(),_("div",Et,[s("div",It,[e[32]||(e[32]=s("div",{class:"flex-shrink-0"},[s("svg",{class:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor"},[s("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})])],-1)),s("div",At,[s("p",Lt,F(b.value),1)])])])):V("",!0),y.value?(p(),_("div",zt,[s("div",St,[e[33]||(e[33]=s("div",{class:"flex-shrink-0"},[s("svg",{class:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor"},[s("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])],-1)),s("div",Ut,[s("p",jt,F(y.value),1)])])])):V("",!0),f.value?V("",!0):(p(),_("div",Rt,[l(H,{title:"Provider Settings",description:"Manage your professional profile and appointment preferences"}),s("form",{onSubmit:ee(ve,["prevent"]),class:"space-y-8 mt-6"},[s("div",Tt,[e[40]||(e[40]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Professional Information",-1)),s("div",Kt,[s("div",null,[l(t(B),{for:"specialization"},{default:r(()=>e[34]||(e[34]=[v("Specialization *")])),_:1}),l(t(M),{id:"specialization",modelValue:i.value.specialization,"onUpdate:modelValue":e[2]||(e[2]=n=>i.value.specialization=n),placeholder:"e.g., Cardiology, Dermatology",required:"",class:"mt-1"},null,8,["modelValue"])]),s("div",null,[l(t(B),{for:"license_number"},{default:r(()=>e[35]||(e[35]=[v("License Number")])),_:1}),l(t(M),{id:"license_number",modelValue:i.value.license_number,"onUpdate:modelValue":e[3]||(e[3]=n=>i.value.license_number=n),placeholder:"Medical license number",class:"mt-1"},null,8,["modelValue"])]),s("div",null,[l(t(B),{for:"gender"},{default:r(()=>e[36]||(e[36]=[v("Gender")])),_:1}),S(s("select",{id:"gender","onUpdate:modelValue":e[4]||(e[4]=n=>i.value.gender=n),class:"mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"},[e[37]||(e[37]=s("option",{value:""},"Select Gender",-1)),(p(),_(j,null,R(ce,n=>s("option",{key:n.value,value:n.value},F(n.label),9,Nt)),64))],512),[[Oe,i.value.gender]])])]),s("div",qt,[l(t(B),{for:"education"},{default:r(()=>e[38]||(e[38]=[v("Education")])),_:1}),S(s("textarea",{id:"education","onUpdate:modelValue":e[5]||(e[5]=n=>i.value.education=n),rows:"3",placeholder:"Medical school, residency, fellowships...",class:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"},null,512),[[te,i.value.education]])]),s("div",Ht,[l(t(B),{for:"bio"},{default:r(()=>e[39]||(e[39]=[v("Bio")])),_:1}),S(s("textarea",{id:"bio","onUpdate:modelValue":e[6]||(e[6]=n=>i.value.bio=n),rows:"4",placeholder:"Tell patients about yourself, your approach to care, and your experience...",class:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"},null,512),[[te,i.value.bio]])])]),s("div",Gt,[e[43]||(e[43]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Languages",-1)),s("div",Xt,[(p(!0),_(j,null,R(i.value.languages,(n,x)=>(p(),_("span",{key:x,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"},[v(F(n)+" ",1),s("button",{onClick:N=>ge(x),type:"button",class:"ml-2 text-blue-600 hover:text-blue-800"},e[41]||(e[41]=[s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Yt)]))),128))]),s("div",Qt,[l(t(M),{modelValue:$.value,"onUpdate:modelValue":e[7]||(e[7]=n=>$.value=n),onKeyup:q(W,["enter"]),placeholder:"Add a language",class:"flex-1"},null,8,["modelValue"]),l(t(L),{onClick:W,type:"button",variant:"outline"},{default:r(()=>e[42]||(e[42]=[v(" Add ")])),_:1})])]),s("div",Wt,[e[52]||(e[52]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Practice Locations",-1)),s("div",Jt,[e[50]||(e[50]=s("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"Add New Location",-1)),s("div",Zt,[s("div",es,[l(t(B),{for:"new_address"},{default:r(()=>e[44]||(e[44]=[v("Address")])),_:1}),l(t(M),{id:"new_address",modelValue:P.value.address,"onUpdate:modelValue":e[8]||(e[8]=n=>P.value.address=n),placeholder:"Street address",class:"mt-1"},null,8,["modelValue"])]),s("div",null,[l(t(B),{for:"new_city"},{default:r(()=>e[45]||(e[45]=[v("City")])),_:1}),l(t(M),{id:"new_city",modelValue:P.value.city,"onUpdate:modelValue":e[9]||(e[9]=n=>P.value.city=n),placeholder:"City",class:"mt-1"},null,8,["modelValue"])]),s("div",null,[l(t(B),{for:"new_state"},{default:r(()=>e[46]||(e[46]=[v("State")])),_:1}),l(t(M),{id:"new_state",modelValue:P.value.state,"onUpdate:modelValue":e[10]||(e[10]=n=>P.value.state=n),placeholder:"State",class:"mt-1"},null,8,["modelValue"])]),s("div",null,[l(t(B),{for:"new_zip"},{default:r(()=>e[47]||(e[47]=[v("Postcode")])),_:1}),l(t(M),{id:"new_zip",modelValue:P.value.zip_code,"onUpdate:modelValue":e[11]||(e[11]=n=>P.value.zip_code=n),placeholder:"Postcode",class:"mt-1"},null,8,["modelValue"])]),s("div",ts,[S(s("input",{"onUpdate:modelValue":e[12]||(e[12]=n=>P.value.is_primary=n),type:"checkbox",id:"is_primary_new",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[se,P.value.is_primary]]),e[48]||(e[48]=s("label",{for:"is_primary_new",class:"ml-2 block text-sm text-gray-700"}," Set as primary location ",-1))])]),l(t(L),{onClick:_e,type:"button",variant:"outline",class:"mt-3"},{default:r(()=>e[49]||(e[49]=[s("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),v(" Add Location ")])),_:1})]),i.value.practice_locations.length>0?(p(),_("div",ss,[(p(!0),_(j,null,R(i.value.practice_locations,(n,x)=>(p(),_("div",{key:x,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[s("div",os,[s("div",ls,[s("span",as,F(n.address),1),n.is_primary?(p(),_("span",ns," Primary ")):V("",!0)]),s("p",rs,F(n.city)+", "+F(n.state)+" "+F(n.zip_code),1)]),s("div",is,[n.is_primary?V("",!0):(p(),_("button",{key:0,onClick:N=>xe(x),type:"button",class:"text-sm text-blue-600 hover:text-blue-800"}," Set Primary ",8,ds)),s("button",{onClick:N=>be(x),type:"button",class:"text-red-600 hover:text-red-800"},e[51]||(e[51]=[s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,us)])]))),128))])):(p(),_("div",ps," No practice locations added yet "))]),s("div",cs,[e[59]||(e[59]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Pricing & Insurance",-1)),s("div",fs,[s("div",null,[l(t(B),{for:"consultation_fee"},{default:r(()=>e[53]||(e[53]=[v("Consultation Fee ($)")])),_:1}),l(t(M),{id:"consultation_fee",modelValue:i.value.pricing.consultation,"onUpdate:modelValue":e[13]||(e[13]=n=>i.value.pricing.consultation=n),modelModifiers:{number:!0},type:"number",min:"0",step:"0.01",placeholder:"0.00",class:"mt-1"},null,8,["modelValue"])]),s("div",null,[l(t(B),{for:"follow_up_fee"},{default:r(()=>e[54]||(e[54]=[v("Follow-up Fee ($)")])),_:1}),l(t(M),{id:"follow_up_fee",modelValue:i.value.pricing.follow_up,"onUpdate:modelValue":e[14]||(e[14]=n=>i.value.pricing.follow_up=n),modelModifiers:{number:!0},type:"number",min:"0",step:"0.01",placeholder:"0.00",class:"mt-1"},null,8,["modelValue"])])]),s("div",ms,[s("div",vs,[S(s("input",{"onUpdate:modelValue":e[15]||(e[15]=n=>i.value.accepts_insurance=n),type:"checkbox",id:"accepts_insurance",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[se,i.value.accepts_insurance]]),e[55]||(e[55]=s("label",{for:"accepts_insurance",class:"ml-2 block text-sm font-medium text-gray-700"}," Accept Insurance ",-1))])]),i.value.accepts_insurance?(p(),_("div",gs,[l(t(B),{class:"block text-sm font-medium text-gray-700 mb-2"},{default:r(()=>e[56]||(e[56]=[v("Insurance Providers")])),_:1}),s("div",ys,[l(t(M),{modelValue:E.value,"onUpdate:modelValue":e[16]||(e[16]=n=>E.value=n),placeholder:"Add insurance provider",class:"flex-1",onKeyup:q(Z,["enter"])},null,8,["modelValue"]),l(t(L),{onClick:Z,type:"button",variant:"outline"},{default:r(()=>e[57]||(e[57]=[v(" Add ")])),_:1})]),i.value.insurance_providers.length>0?(p(),_("div",_s,[(p(!0),_(j,null,R(i.value.insurance_providers,(n,x)=>(p(),_("span",{key:x,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"},[v(F(n)+" ",1),s("button",{onClick:N=>we(x),type:"button",class:"ml-2 text-blue-600 hover:text-blue-800"},e[58]||(e[58]=[s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,bs)]))),128))])):V("",!0)])):V("",!0)]),s("div",xs,[e[62]||(e[62]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Certifications",-1)),s("div",ws,[(p(!0),_(j,null,R(i.value.certifications,(n,x)=>(p(),_("div",{key:x,class:"flex justify-between items-center p-3 bg-gray-50 rounded-lg"},[s("span",ks,F(n),1),s("button",{onClick:N=>ye(x),type:"button",class:"text-red-600 hover:text-red-800"},e[60]||(e[60]=[s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Cs)]))),128))]),s("div",hs,[l(t(M),{modelValue:z.value,"onUpdate:modelValue":e[17]||(e[17]=n=>z.value=n),onKeyup:q(J,["enter"]),placeholder:"Add a certification",class:"flex-1"},null,8,["modelValue"]),l(t(L),{onClick:J,type:"button",variant:"outline"},{default:r(()=>e[61]||(e[61]=[v(" Add ")])),_:1})])]),s("div",$s,[l(t(L),{type:"submit",disabled:g.value,class:"min-w-[120px]"},{default:r(()=>[g.value?(p(),_("div",Ps,e[63]||(e[63]=[s("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),v(" Saving... ")]))):(p(),_("span",Ds,"Save Provider Settings"))]),_:1},8,["disabled"])])],32)]))])):V("",!0),l(mt)]),_:1})]),_:1}))}});export{js as default};
