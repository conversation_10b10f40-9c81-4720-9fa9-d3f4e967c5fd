import{r as v,o as T,C as A,d as r,e as l,f as z,u as F,m as I,g as E,i as e,j as x,x as d,t as o,z as U,l as G,v as H,F as W,a as O,W as S}from"./vendor-DkZiYBIF.js";import{_ as q}from"./AppLayout.vue_vue_type_script_setup_true_lang-C2T_8RUB.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-fZxfkI-v.js";import"./createLucideIcon-CSZE2I1R.js";const J={class:"max-w-2xl mx-auto py-8 px-4"},Y={class:"bg-white rounded-lg shadow-md p-6 mb-6"},K={class:"border-l-4 border-blue-500 pl-4 mb-6"},Q={class:"space-y-2 text-sm text-gray-600"},X={class:"mt-1"},Z={key:0,class:"text-gray-600"},ee={key:1,class:"text-gray-600"},te={class:"whitespace-pre-wrap"},se={key:0},ne={class:"bg-gray-50 rounded-lg p-4"},ae={class:"flex justify-between items-center"},oe={class:"text-2xl font-bold text-green-600"},re={key:0,class:"mt-2 text-xs text-gray-500"},le={key:1,class:"mt-2 text-xs text-red-500"},ie={class:"bg-white rounded-lg shadow-md p-6"},de={key:0,class:"bg-red-50 border border-red-200 rounded-md p-4 mb-4"},me={class:"flex"},ce={class:"ml-3"},ue={class:"text-sm text-red-800"},pe={key:1,class:"bg-green-50 border border-green-200 rounded-md p-4 mb-4"},fe={class:"flex"},ve={class:"ml-3"},ye={class:"text-sm text-green-800"},ge={class:"flex space-x-4 pt-6"},be=["disabled"],xe={key:0,class:"flex items-center justify-center"},he={key:1},Se={__name:"AppointmentPayment",props:{appointment:Object,stripe_public_key:String},setup(n){const u=n,p=v(!1),a=v(""),y=v("");let g=null,k=null,f=null;const b=v(""),M=[{name:"Appointments",href:"/appointments"},{name:"Payment",href:"#"}],B=s=>new Date(s).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),j=s=>typeof s=="object"&&s.start_time?`${s.start_time} - ${s.end_time}`:s;T(async()=>{await D()});const D=async()=>{try{if(!window.Stripe){const s=document.createElement("script");s.src="https://js.stripe.com/v3/",s.async=!0,document.head.appendChild(s),await new Promise((t,c)=>{s.onload=t,s.onerror=c})}g=window.Stripe(u.stripe_public_key,{locale:"en-GB"}),k=g.elements({locale:"en-GB"}),f=k.create("card",{style:{base:{fontSize:"16px",color:"#424770","::placeholder":{color:"#aab7c4"}},invalid:{color:"#9e2146"}}}),await A(),f.mount("#card-element"),f.on("change",({error:s})=>{s?a.value=s.message:a.value=""})}catch(s){console.error("Error initializing Stripe:",s),a.value="Failed to initialize payment system. Please refresh the page."}},N=async()=>{var s,t,c,m;if(V()){if(!g||!f){a.value="Payment system not ready. Please refresh the page.";return}p.value=!0,a.value="",y.value="";try{if(!u.appointment.client_secret){a.value="Payment information not found. Please refresh the page and try again.",p.value=!1;return}const{error:i,paymentIntent:w}=await g.confirmCardPayment(u.appointment.client_secret,{payment_method:{card:f,billing_details:{name:b.value}}});if(i){a.value=i.message,p.value=!1;return}const C=await O.post("/confirm-elements-payment",{payment_intent_id:w.id,appointment_id:u.appointment.id});C.data.success?(y.value="Payment processed successfully! Redirecting back to chat...",setTimeout(()=>{const _=new URLSearchParams(window.location.search).get("conversation"),R=_?`/chat?payment_success=true&appointment_id=${u.appointment.id}&conversation=${_}`:`/chat?payment_success=true&appointment_id=${u.appointment.id}`;S.visit(R,{onSuccess:()=>{console.log("Redirected to chat after successful payment")}})},2e3)):a.value=C.data.message||"Payment failed. Please try again."}catch(i){if(console.error("Payment error:",i),(t=(s=i.response)==null?void 0:s.data)!=null&&t.message)a.value=i.response.data.message;else if((m=(c=i.response)==null?void 0:c.data)!=null&&m.errors){const w=Object.values(i.response.data.errors).flat();a.value=w.join(", ")}else a.value="An error occurred while processing your payment. Please try again."}finally{p.value=!1}}},V=()=>b.value.trim()?!0:(a.value="Please enter the cardholder name.",!1),$=()=>{S.visit("/appointments")},L=s=>{if(!s)return"General consultation";const t=s.replace(/\*\*/g,"").replace(/###/g,"").replace(/\n+/g," ").trim();let m=t.split(/[.!?]+/).filter(i=>i.trim().length>0).slice(0,3).join(". ");return m.length>200&&(m=t.substring(0,200)),m.length<t.length&&(m+="..."),m},h=v(!1),P=()=>{h.value=!h.value};return(s,t)=>(l(),r(W,null,[z(F(I),{title:"Payment - Medroid"}),z(q,{breadcrumbs:M},{default:E(()=>[e("div",J,[e("div",Y,[t[11]||(t[11]=e("h1",{class:"text-2xl font-bold text-gray-900 mb-4"},"Complete Payment",-1)),e("div",K,[t[8]||(t[8]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-2"},"Appointment Details",-1)),e("div",Q,[e("p",null,[t[1]||(t[1]=e("span",{class:"font-medium"},"Provider:",-1)),d(" "+o(n.appointment.provider.name),1)]),e("p",null,[t[2]||(t[2]=e("span",{class:"font-medium"},"Specialization:",-1)),d(" "+o(n.appointment.provider.specialization),1)]),e("p",null,[t[3]||(t[3]=e("span",{class:"font-medium"},"Service:",-1)),d(" "+o(n.appointment.service.name),1)]),e("p",null,[t[4]||(t[4]=e("span",{class:"font-medium"},"Date:",-1)),d(" "+o(B(n.appointment.date)),1)]),e("p",null,[t[5]||(t[5]=e("span",{class:"font-medium"},"Time:",-1)),d(" "+o(j(n.appointment.time_slot)),1)]),e("div",null,[t[6]||(t[6]=e("span",{class:"font-medium"},"Reason:",-1)),e("div",X,[h.value?(l(),r("div",ee,[e("p",te,o(n.appointment.reason),1),e("button",{onClick:P,class:"mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium"}," Show less ")])):(l(),r("p",Z,[d(o(L(n.appointment.reason))+" ",1),n.appointment.reason&&n.appointment.reason.length>200?(l(),r("button",{key:0,onClick:P,class:"ml-2 text-blue-600 hover:text-blue-800 text-sm font-medium"}," See more ")):x("",!0)]))])]),n.appointment.notes?(l(),r("p",se,[t[7]||(t[7]=e("span",{class:"font-medium"},"Notes:",-1)),d(" "+o(n.appointment.notes),1)])):x("",!0)])]),e("div",ne,[e("div",ae,[t[9]||(t[9]=e("span",{class:"text-lg font-semibold text-gray-900"},"Total Amount:",-1)),e("span",oe,"$"+o(n.appointment.amount),1)]),n.appointment.client_secret?(l(),r("div",re,[d(" Payment Intent: "+o(n.appointment.payment_intent_id)+" ",1),t[10]||(t[10]=e("br",null,null,-1)),d("Client Secret: "+o(n.appointment.client_secret.substring(0,30))+"... ",1)])):(l(),r("div",le," No payment information found "))])]),e("div",ie,[t[17]||(t[17]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Payment Information",-1)),a.value?(l(),r("div",de,[e("div",me,[t[12]||(t[12]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})])],-1)),e("div",ce,[e("p",ue,o(a.value),1)])])])):x("",!0),y.value?(l(),r("div",pe,[e("div",fe,[t[13]||(t[13]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])],-1)),e("div",ve,[e("p",ye,o(y.value),1)])])])):x("",!0),e("form",{onSubmit:U(N,["prevent"]),class:"space-y-4"},[e("div",null,[t[14]||(t[14]=e("label",{for:"cardholder_name",class:"block text-sm font-medium text-gray-700 mb-1"}," Cardholder Name * ",-1)),G(e("input",{id:"cardholder_name","onUpdate:modelValue":t[0]||(t[0]=c=>b.value=c),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"John Doe"},null,512),[[H,b.value]])]),t[16]||(t[16]=e("div",null,[e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Card Information * "),e("div",{id:"card-element",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",style:{"min-height":"40px"}})],-1)),e("div",ge,[e("button",{type:"button",onClick:$,class:"flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"}," Cancel "),e("button",{type:"submit",disabled:p.value,class:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"},[p.value?(l(),r("span",xe,t[15]||(t[15]=[e("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),d(" Processing... ")]))):(l(),r("span",he," Pay $"+o(n.appointment.amount),1))],8,be)])],32),t[18]||(t[18]=e("div",{class:"mt-6 p-4 bg-gray-50 rounded-md"},[e("div",{class:"flex"},[e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z","clip-rule":"evenodd"})])]),e("div",{class:"ml-3"},[e("p",{class:"text-sm text-gray-600"}," Your payment information is secure and encrypted. We use Stripe for payment processing. ")])])],-1))])])]),_:1})],64))}};export{Se as default};
