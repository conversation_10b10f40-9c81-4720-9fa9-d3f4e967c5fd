<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('providers', function (Blueprint $table) {
            // Add verified_at column if it doesn't exist
            if (!Schema::hasColumn('providers', 'verified_at')) {
                $table->timestamp('verified_at')->nullable()->after('verification_status');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('providers', function (Blueprint $table) {
            // Drop verified_at column if it exists
            if (Schema::hasColumn('providers', 'verified_at')) {
                $table->dropColumn('verified_at');
            }
        });
    }
};
