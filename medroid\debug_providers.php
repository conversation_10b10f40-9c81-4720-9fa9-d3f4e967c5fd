<?php

/**
 * Debug script to check provider verification status and appointment slots
 * Run this script on production to diagnose appointment slots issues
 * 
 * Usage: php debug_providers.php
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel application
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Provider;
use App\Models\User;
use App\Models\Service;
use Carbon\Carbon;

echo "=== MEDROID PROVIDER DEBUG SCRIPT ===\n\n";

// Check total providers
$totalProviders = Provider::count();
echo "Total Providers: {$totalProviders}\n\n";

// Check providers by verification status
$pendingProviders = Provider::where('verification_status', 'pending')->count();
$verifiedProviders = Provider::where('verification_status', 'verified')->count();
$rejectedProviders = Provider::where('verification_status', 'rejected')->count();

echo "Providers by Verification Status:\n";
echo "- Pending: {$pendingProviders}\n";
echo "- Verified: {$verifiedProviders}\n";
echo "- Rejected: {$rejectedProviders}\n\n";

// List all providers with details
echo "=== PROVIDER DETAILS ===\n";
$providers = Provider::with('user', 'services')->get();

foreach ($providers as $provider) {
    echo "Provider ID: {$provider->id}\n";
    echo "Name: " . ($provider->user ? $provider->user->name : 'No User') . "\n";
    echo "Email: " . ($provider->user ? $provider->user->email : 'No Email') . "\n";
    echo "Verification Status: {$provider->verification_status}\n";
    echo "Verified At: " . ($provider->verified_at ? $provider->verified_at->format('Y-m-d H:i:s') : 'Not verified') . "\n";
    echo "Specialization: " . ($provider->specialization ?: 'Not set') . "\n";
    echo "License Number: " . ($provider->license_number ?: 'Not set') . "\n";
    
    // Check services
    $servicesCount = $provider->services()->count();
    $activeServicesCount = $provider->services()->where('active', true)->count();
    echo "Services: {$servicesCount} total, {$activeServicesCount} active\n";
    
    // Check availability
    $weeklyAvailability = $provider->getAvailabilityData();
    $hasAvailability = false;
    foreach ($weeklyAvailability as $day) {
        if (!empty($day['slots'])) {
            $hasAvailability = true;
            break;
        }
    }
    echo "Has Availability: " . ($hasAvailability ? 'Yes' : 'No') . "\n";
    
    // Check available slots for today
    $today = Carbon::today()->format('Y-m-d');
    $todaySlots = $provider->getAvailableTimeSlots($today);
    echo "Available Slots Today: " . count($todaySlots) . "\n";
    
    echo "Created: " . $provider->created_at->format('Y-m-d H:i:s') . "\n";
    echo "Updated: " . $provider->updated_at->format('Y-m-d H:i:s') . "\n";
    echo "---\n\n";
}

// Check appointment slots API simulation
echo "=== APPOINTMENT SLOTS API SIMULATION ===\n";
echo "Simulating chat appointment slots request...\n\n";

// Get verified providers (like the chat system does)
$verifiedProvidersForSlots = Provider::where('verification_status', 'verified')
    ->with(['services', 'user'])
    ->get();

echo "Verified providers found for slots: " . $verifiedProvidersForSlots->count() . "\n\n";

if ($verifiedProvidersForSlots->count() > 0) {
    echo "Checking slots for next 3 days:\n";
    
    for ($i = 0; $i < 3; $i++) {
        $date = Carbon::today()->addDays($i)->format('Y-m-d');
        $dayName = Carbon::today()->addDays($i)->format('l');
        echo "\n{$dayName} ({$date}):\n";
        
        $totalSlotsForDay = 0;
        
        foreach ($verifiedProvidersForSlots as $provider) {
            $slots = $provider->getAvailableTimeSlots($date);
            $slotsCount = count($slots);
            $totalSlotsForDay += $slotsCount;
            
            if ($slotsCount > 0) {
                echo "  - {$provider->user->name}: {$slotsCount} slots\n";
                // Show first few slots as examples
                $exampleSlots = array_slice($slots, 0, 3);
                foreach ($exampleSlots as $slot) {
                    echo "    * {$slot['start_time']} - {$slot['end_time']}\n";
                }
                if ($slotsCount > 3) {
                    echo "    * ... and " . ($slotsCount - 3) . " more\n";
                }
            } else {
                echo "  - {$provider->user->name}: No slots\n";
            }
        }
        
        echo "  Total slots for {$dayName}: {$totalSlotsForDay}\n";
    }
} else {
    echo "❌ NO VERIFIED PROVIDERS FOUND!\n";
    echo "This is why appointment slots are not showing in chat.\n\n";
    
    echo "Providers that need verification:\n";
    $unverifiedProviders = Provider::where('verification_status', '!=', 'verified')
        ->with('user')
        ->get();
    
    foreach ($unverifiedProviders as $provider) {
        echo "- {$provider->user->name} (ID: {$provider->id}) - Status: {$provider->verification_status}\n";
    }
}

echo "\n=== RECOMMENDATIONS ===\n";

if ($verifiedProviders == 0) {
    echo "❌ CRITICAL: No verified providers found!\n";
    echo "✅ SOLUTION: Run the verification script to auto-verify existing providers.\n";
    echo "   Command: php verify_providers.php\n\n";
}

if ($totalProviders == 0) {
    echo "❌ CRITICAL: No providers found in database!\n";
    echo "✅ SOLUTION: Create providers through admin panel or provider signup.\n\n";
}

$providersWithoutAvailability = 0;
foreach ($providers as $provider) {
    $weeklyAvailability = $provider->getAvailabilityData();
    $hasAvailability = false;
    foreach ($weeklyAvailability as $day) {
        if (!empty($day['slots'])) {
            $hasAvailability = true;
            break;
        }
    }
    if (!$hasAvailability) {
        $providersWithoutAvailability++;
    }
}

if ($providersWithoutAvailability > 0) {
    echo "⚠️  WARNING: {$providersWithoutAvailability} providers have no availability set.\n";
    echo "✅ SOLUTION: Providers need to set their availability in settings.\n\n";
}

echo "=== DEBUG COMPLETE ===\n";
