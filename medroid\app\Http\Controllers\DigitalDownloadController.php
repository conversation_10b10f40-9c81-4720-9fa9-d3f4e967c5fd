<?php

namespace App\Http\Controllers;

use App\Models\DigitalProductDownload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class DigitalDownloadController extends Controller
{
    public function index(Request $request)
    {
        $downloads = DigitalProductDownload::with(['product', 'order'])
            ->where('user_id', Auth::id())
            ->active()
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        if ($request->expectsJson()) {
            return response()->json([
                'downloads' => $downloads,
            ]);
        }

        return response()->json($downloads);
    }

    public function download(Request $request, $token)
    {
        $download = DigitalProductDownload::where('download_token', $token)
            ->active()
            ->notExpired()
            ->withinDownloadLimit()
            ->firstOrFail();

        // Check if user owns this download (for authenticated requests)
        if (Auth::check() && $download->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to download');
        }

        if (!$download->canDownload()) {
            abort(403, 'Download no longer available');
        }

        $filePath = storage_path('app/' . $download->file_path);

        if (!file_exists($filePath)) {
            abort(404, 'File not found');
        }

        // Record the download
        $download->recordDownload();

        // Return file download
        return response()->download(
            $filePath,
            $download->original_filename,
            [
                'Content-Type' => mime_content_type($filePath),
                'Content-Disposition' => 'attachment; filename="' . $download->original_filename . '"',
            ]
        );
    }

    public function show(Request $request, $token)
    {
        $download = DigitalProductDownload::with(['product', 'order'])
            ->where('download_token', $token)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        return response()->json([
            'download' => $download,
            'can_download' => $download->canDownload(),
        ]);
    }

    public function userDownloads(Request $request)
    {
        $downloads = DigitalProductDownload::with(['product', 'order'])
            ->where('user_id', Auth::id())
            ->active()
            ->orderBy('created_at', 'desc')
            ->get()
            ->groupBy('order_id');

        return response()->json([
            'downloads_by_order' => $downloads,
        ]);
    }
}
