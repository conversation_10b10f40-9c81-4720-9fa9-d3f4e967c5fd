<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Patient;
use App\Models\PersonalAccessToken;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class FlutterAuthenticationFlowTest extends TestCase
{
    use RefreshDatabase;

    public function test_flutter_authentication_flow_exactly_matches_logs()
    {
        // Create user exactly like in Flutter logs
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'patient',
        ]);

        // Create patient profile
        Patient::create([
            'user_id' => $user->id,
            'emergency_contact_name' => 'Test Contact',
            'emergency_contact_phone' => '**********',
        ]);

        // Step 1: Login with exact Flutter request
        $loginResponse = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'device_type' => 'android',
            'user_agent' => 'medroid_app/1.0.0 (Android Android 15; RMX3771)',
            'device_model' => 'RMX3771',
            'platform' => 'Android',
            'app_version' => '1.0.0',
        ], [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ]);

        $loginResponse->assertStatus(200);
        $token = $loginResponse->json('access_token');

        $this->assertNotNull($token);
        $this->assertStringContainsString('|', $token);

        // Verify token is in database
        $accessToken = PersonalAccessToken::findToken($token);
        $this->assertNotNull($accessToken);
        $this->assertEquals($user->id, $accessToken->user_id);

        // Step 2: Make the exact sequence of API calls Flutter makes after login
        $headers = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $token,
        ];

        // 1. Get public providers (should work without auth)
        $response1 = $this->withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ])->getJson('/api/public/providers');
        $response1->assertStatus(200);

        // 2. Get appointments list (requires auth)
        $response2 = $this->withHeaders($headers)->getJson('/api/appointments-list');
        $response2->assertStatus(200);

        // 3. Get feed (requires auth)
        $response3 = $this->withHeaders($headers)->getJson('/api/feed?page=1');
        $response3->assertStatus(200);

        // 4. Get feed topics (requires auth)
        $response4 = $this->withHeaders($headers)->getJson('/api/feed/topics');
        $response4->assertStatus(200);

        // 5. Get provider specializations (requires auth)
        $response5 = $this->withHeaders($headers)->getJson('/api/providers/specializations');
        $response5->assertStatus(200);

        // 6. Get notifications unread count (requires auth)
        $response6 = $this->withHeaders($headers)->getJson('/api/notifications/unread-count');
        $response6->assertStatus(200);

        // 7. Start chat conversation (requires auth)
        $response7 = $this->withHeaders($headers)->postJson('/api/chat/start', []);
        $response7->assertStatus(200);
        $this->assertArrayHasKey('conversation_id', $response7->json());

        // 8. Get chat history (requires auth)
        $response8 = $this->withHeaders($headers)->getJson('/api/chat/history?per_page=20');
        $response8->assertStatus(200);

        // 9. Get chat history with different per_page (requires auth)
        $response9 = $this->withHeaders($headers)->getJson('/api/chat/history?per_page=50');
        $response9->assertStatus(200);

        // Verify token is still valid after all requests
        $accessToken->refresh();
        $this->assertNotNull($accessToken->last_used_at);

        // Verify user endpoint still works
        $userResponse = $this->withHeaders($headers)->getJson('/api/user');
        $userResponse->assertStatus(200)
                     ->assertJson([
                         'id' => $user->id,
                         'email' => $user->email,
                         'role' => 'patient',
                     ]);
    }

    public function test_flutter_token_persists_across_multiple_app_sessions()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'patient',
        ]);

        Patient::create([
            'user_id' => $user->id,
            'emergency_contact_name' => 'Test Contact',
            'emergency_contact_phone' => '**********',
        ]);

        // Login
        $loginResponse = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'device_name' => 'flutter-app',
        ]);

        $token = $loginResponse->json('access_token');

        // Simulate multiple app sessions (like Flutter app being closed and reopened)
        for ($session = 1; $session <= 5; $session++) {
            $headers = [
                'Authorization' => 'Bearer ' . $token,
                'Accept' => 'application/json',
            ];

            // Make multiple requests per session
            for ($request = 1; $request <= 3; $request++) {
                $response = $this->withHeaders($headers)->getJson('/api/user');
                $response->assertStatus(200);

                $appointmentsResponse = $this->withHeaders($headers)->getJson('/api/appointments-list');
                $appointmentsResponse->assertStatus(200);
            }
        }

        // Token should still be valid
        $accessToken = PersonalAccessToken::findToken($token);
        $this->assertNotNull($accessToken);
        $this->assertEquals($user->id, $accessToken->user_id);
    }

    public function test_flutter_handles_malformed_tokens_correctly()
    {
        $malformedTokens = [
            'invalid-token',
            '123|invalid',
            'Bearer invalid-token',
            '',
            '|',
            '123|',
            '|token',
        ];

        foreach ($malformedTokens as $malformedToken) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $malformedToken,
                'Accept' => 'application/json',
            ])->getJson('/api/user');

            $response->assertStatus(401);
        }
    }

    public function test_flutter_multiple_device_tokens_work_independently()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'patient',
        ]);

        Patient::create([
            'user_id' => $user->id,
            'emergency_contact_name' => 'Test Contact',
            'emergency_contact_phone' => '**********',
        ]);

        // Login from device 1
        $device1Response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'device_name' => 'flutter-device-1',
            'device_type' => 'android',
        ]);

        // Login from device 2
        $device2Response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'device_name' => 'flutter-device-2',
            'device_type' => 'ios',
        ]);

        $token1 = $device1Response->json('access_token');
        $token2 = $device2Response->json('access_token');

        $this->assertNotEquals($token1, $token2);

        // Both tokens should work independently
        $headers1 = ['Authorization' => 'Bearer ' . $token1, 'Accept' => 'application/json'];
        $headers2 = ['Authorization' => 'Bearer ' . $token2, 'Accept' => 'application/json'];

        $response1 = $this->withHeaders($headers1)->getJson('/api/user');
        $response2 = $this->withHeaders($headers2)->getJson('/api/user');

        $response1->assertStatus(200);
        $response2->assertStatus(200);

        // Should have 2 tokens in database
        $this->assertEquals(2, PersonalAccessToken::where('user_id', $user->id)->count());
    }
}
