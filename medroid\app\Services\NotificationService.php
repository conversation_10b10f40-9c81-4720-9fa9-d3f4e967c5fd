<?php

namespace App\Services;

use App\Models\DeviceToken;
use App\Models\Notification;
use App\Models\User;
use App\Facades\Firebase;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Send a push notification to a user.
     *
     * @param  \App\Models\User  $user
     * @param  string  $title
     * @param  string  $body
     * @param  string  $type
     * @param  array  $data
     * @return bool
     */
    public function sendPushNotification(User $user, string $title, string $body, string $type, array $data = [])
    {
        try {
            // Create a notification record in the database
            $notification = Notification::create([
                'user_id' => $user->id,
                'title' => $title,
                'body' => $body,
                'type' => $type,
                'data' => $data,
            ]);

            // Get all device tokens for the user
            $deviceTokens = DeviceToken::where('user_id', $user->id)->pluck('token')->toArray();

            if (empty($deviceTokens)) {
                Log::info('No device tokens found for user', ['user_id' => $user->id]);
                return false;
            }

            // Send notification via Firebase HTTP API
            return $this->sendPushNotificationViaHttp($deviceTokens, $title, $body, $type, $data, $notification);

        } catch (\Exception $e) {
            Log::error('Exception while sending push notification', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return false;
        }
    }

    /**
     * Store a device token for a user.
     *
     * @param  \App\Models\User  $user
     * @param  string  $token
     * @param  string  $deviceType
     * @param  array  $deviceInfo
     * @return \App\Models\DeviceToken
     */
    public function storeDeviceToken(User $user, string $token, string $deviceType = 'android', array $deviceInfo = [])
    {
        return DeviceToken::updateOrCreate(
            ['token' => $token],
            array_merge([
                'user_id' => $user->id,
                'device_type' => $deviceType,
                'last_used_at' => now(),
            ], $deviceInfo)
        );
    }

    /**
     * Update a device token for a user.
     *
     * @param  string  $oldToken
     * @param  string  $newToken
     * @param  string  $deviceType
     * @return \App\Models\DeviceToken|null
     */
    public function updateDeviceToken(string $oldToken, string $newToken, string $deviceType = 'android')
    {
        $deviceToken = DeviceToken::where('token', $oldToken)->first();

        if (!$deviceToken) {
            Log::warning('Attempted to update non-existent device token', [
                'old_token' => substr($oldToken, 0, 10) . '...',
                'new_token' => substr($newToken, 0, 10) . '...',
            ]);
            return null;
        }

        $deviceToken->token = $newToken;
        $deviceToken->device_type = $deviceType;
        $deviceToken->last_used_at = now();
        $deviceToken->save();

        Log::info('Device token updated', [
            'user_id' => $deviceToken->user_id,
            'device_type' => $deviceType,
        ]);

        return $deviceToken;
    }

    /**
     * Get all notifications for a user.
     *
     * @param  \App\Models\User  $user
     * @param  int  $limit
     * @param  int  $offset
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getUserNotifications(User $user, int $limit = 20, int $offset = 0)
    {
        return Notification::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->skip($offset)
            ->take($limit)
            ->get();
    }

    /**
     * Get unread notification count for a user.
     *
     * @param  \App\Models\User  $user
     * @return int
     */
    public function getUnreadCount(User $user)
    {
        return Notification::where('user_id', $user->id)
            ->whereNull('read_at')
            ->count();
    }

    /**
     * Mark a notification as read.
     *
     * @param  int  $notificationId
     * @param  \App\Models\User  $user
     * @return bool
     */
    public function markAsRead(int $notificationId, User $user)
    {
        $notification = Notification::where('id', $notificationId)
            ->where('user_id', $user->id)
            ->first();

        if (!$notification) {
            return false;
        }

        return $notification->markAsRead();
    }

    /**
     * Mark all notifications as read for a user.
     *
     * @param  \App\Models\User  $user
     * @return bool
     */
    public function markAllAsRead(User $user)
    {
        return Notification::where('user_id', $user->id)
            ->whereNull('read_at')
            ->update(['read_at' => now()]);
    }

    /**
     * Send push notification using Firebase.
     *
     * @param array $deviceTokens
     * @param string $title
     * @param string $body
     * @param string $type
     * @param array $data
     * @param Notification $notification
     * @return bool
     */
    private function sendPushNotificationViaHttp(array $deviceTokens, string $title, string $body, string $type, array $data, Notification $notification)
    {
        try {
            $notificationData = array_merge($data, [
                'type' => $type,
                'notification_id' => (string)$notification->id,
            ]);

            $success = Firebase::sendMulticastNotification(
                $deviceTokens,
                $title,
                $body,
                $notificationData
            );

            if ($success) {
                Log::info('Push notifications sent successfully', [
                    'notification_id' => $notification->id,
                    'token_count' => count($deviceTokens)
                ]);
            } else {
                Log::error('Failed to send push notifications', [
                    'notification_id' => $notification->id
                ]);
            }

            return $success;
        } catch (\Exception $e) {
            Log::error('Exception while sending push notification', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Send notification to users by device type.
     */
    public function sendNotificationByDeviceType(string $deviceType, string $title, string $body, string $type, array $data = [])
    {
        $deviceTokens = DeviceToken::where('device_type', $deviceType)->pluck('token')->toArray();

        if (empty($deviceTokens)) {
            return false;
        }

        return $this->sendPushNotificationToTokens($deviceTokens, $title, $body, $type, $data);
    }

    /**
     * Send notification to users by browser.
     */
    public function sendNotificationByBrowser(string $browser, string $title, string $body, string $type, array $data = [])
    {
        $deviceTokens = DeviceToken::where('browser', $browser)->pluck('token')->toArray();

        if (empty($deviceTokens)) {
            return false;
        }

        return $this->sendPushNotificationToTokens($deviceTokens, $title, $body, $type, $data);
    }

    /**
     * Send notification to users by platform.
     */
    public function sendNotificationByPlatform(string $platform, string $title, string $body, string $type, array $data = [])
    {
        $deviceTokens = DeviceToken::where('platform', $platform)->pluck('token')->toArray();

        if (empty($deviceTokens)) {
            return false;
        }

        return $this->sendPushNotificationToTokens($deviceTokens, $title, $body, $type, $data);
    }

    /**
     * Send push notification to specific tokens.
     */
    private function sendPushNotificationToTokens(array $deviceTokens, string $title, string $body, string $type, array $data = [])
    {
        try {
            $notificationData = array_merge($data, [
                'type' => $type,
            ]);

            return Firebase::sendMulticastNotification(
                $deviceTokens,
                $title,
                $body,
                $notificationData
            );
        } catch (\Exception $e) {
            Log::error('Error sending push notification to tokens', [
                'error' => $e->getMessage(),
                'tokens_count' => count($deviceTokens),
            ]);
            return false;
        }
    }
}
