<script setup>
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, Link } from '@inertiajs/vue3'
import { ref, onMounted } from 'vue'
import axios from 'axios'

const breadcrumbs = [
    {
        title: 'Chat History',
        href: '/chat-history',
    },
]

// Reactive data
const chatHistory = ref([])
const loading = ref(false)
const error = ref(null)

// Methods
const fetchChatHistory = async () => {
  loading.value = true
  error.value = null

  try {
    // First, try to update conversation titles for better display
    try {
      await axios.post('/web-api/chat/update-titles', {}, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
          'X-Requested-With': 'XMLHttpRequest',
        },
        withCredentials: true
      })
    } catch (titleError) {
      console.log('Title update failed (non-critical):', titleError)
    }

    const response = await axios.get('/web-api/chat/history')
    const data = response.data

    // Handle different response formats
    let conversations = []
    if (Array.isArray(data)) {
      conversations = data
    } else if (data.conversations && Array.isArray(data.conversations)) {
      conversations = data.conversations
    } else if (data.data && Array.isArray(data.data)) {
      conversations = data.data
    }

    // Filter out empty conversations (conversations with no messages or only empty messages)
    const filteredConversations = conversations.filter(conversation => {
      if (!conversation.messages || !Array.isArray(conversation.messages)) {
        return false
      }

      // Check if conversation has at least one non-empty message
      return conversation.messages.some(message =>
        message && message.content && message.content.trim().length > 0
      )
    })

    chatHistory.value = filteredConversations
  } catch (err) {
    console.error('Error fetching chat history:', err)
    error.value = 'Failed to load chat history'
  } finally {
    loading.value = false
  }
}

const formatChatTitle = (conversation) => {
  if (conversation.title && conversation.title.trim()) {
    return conversation.title
  }

  // Fallback to first message or default
  if (conversation.messages && conversation.messages.length > 0) {
    const firstMessage = conversation.messages.find(m => m.content && m.content.trim())
    if (firstMessage) {
      return firstMessage.content.length > 50
        ? firstMessage.content.substring(0, 50) + '...'
        : firstMessage.content
    }
  }

  return 'New Chat'
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInDays = Math.floor((now - date) / (1000 * 60 * 60 * 24))

  if (diffInDays === 0) return 'Today'
  if (diffInDays === 1) return 'Yesterday'
  if (diffInDays < 7) return `${diffInDays} days ago`
  return date.toLocaleDateString()
}

const getMessageCount = (conversation) => {
  if (!conversation.messages) return 0
  return conversation.messages.filter(m => m.content && m.content.trim()).length
}

// Lifecycle
onMounted(() => {
  fetchChatHistory()
})
</script>

<template>
    <Head title="Chat History - Medroid" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900">Chat History</h1>
                                <p class="text-gray-600 mt-1">View your previous conversations with Medroid AI</p>
                            </div>
                            <Link
                                href="/chat"
                                class="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-teal-600 rounded-lg hover:bg-teal-700 transition-colors duration-200"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                <span>New Chat</span>
                            </Link>
                        </div>
                    </div>
                </div>

                <!-- Loading State -->
                <div v-if="loading" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
                        <span class="ml-2 text-gray-600">Loading chat history...</span>
                    </div>
                </div>

                <!-- Error State -->
                <div v-else-if="error" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">Error loading chat history</h3>
                        <p class="mt-1 text-sm text-gray-500">{{ error }}</p>
                        <button
                            @click="fetchChatHistory"
                            class="mt-4 px-4 py-2 text-sm font-medium text-white bg-teal-600 rounded-lg hover:bg-teal-700"
                        >
                            Try Again
                        </button>
                    </div>
                </div>

                <!-- Chat History List -->
                <div v-else-if="chatHistory.length > 0" class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="p-6">
                        <div class="space-y-4">
                            <Link
                                v-for="chat in chatHistory"
                                :key="chat._id || chat.id"
                                :href="`/chat?conversation=${chat._id || chat.id}`"
                                class="block p-4 border border-gray-200 rounded-lg hover:border-teal-500 hover:bg-teal-50 transition-colors duration-200"
                            >
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <h3 class="text-lg font-medium text-gray-900 mb-1">
                                            {{ formatChatTitle(chat) }}
                                        </h3>
                                        <div class="flex items-center space-x-4 text-sm text-gray-500">
                                            <span>{{ getMessageCount(chat) }} messages</span>
                                            <span>{{ formatDate(chat.updated_at || chat.createdAt) }}</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center text-teal-600">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </div>
                                </div>
                            </Link>
                        </div>
                    </div>
                </div>

                <!-- Empty State -->
                <div v-else class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="text-center py-8">
                        <div class="w-20 h-20 bg-teal-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No chat history yet</h3>
                        <p class="text-gray-600 mb-6">Start a conversation with Medroid AI to see your chat history here.</p>
                        <Link
                            href="/chat"
                            class="inline-flex items-center space-x-2 px-6 py-3 text-sm font-medium text-white bg-teal-600 rounded-lg hover:bg-teal-700 transition-colors duration-200"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                            </svg>
                            <span>Start Your First Chat</span>
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
