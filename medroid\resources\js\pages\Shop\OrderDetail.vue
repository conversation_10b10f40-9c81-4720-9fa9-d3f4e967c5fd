<script setup>
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, Link } from '@inertiajs/vue3'
import { computed } from 'vue'

const props = defineProps({
    order: {
        type: Object,
        required: true
    }
})

const breadcrumbs = [
    { title: 'Shop', href: '/shop' },
    { title: 'Orders', href: '/shop/orders' },
    { title: `Order #${props.order.order_number}`, href: `/shop/orders/${props.order.order_number}` },
]

// Computed properties
const formattedSubtotal = computed(() => '$' + parseFloat(props.order.subtotal || 0).toFixed(2))
const formattedTax = computed(() => '$' + parseFloat(props.order.tax_amount || 0).toFixed(2))
const formattedShipping = computed(() => '$' + parseFloat(props.order.shipping_amount || 0).toFixed(2))
const formattedTotal = computed(() => '$' + parseFloat(props.order.total_amount || 0).toFixed(2))

const statusColor = computed(() => {
    switch (props.order.status) {
        case 'pending': return 'bg-yellow-100 text-yellow-800'
        case 'processing': return 'bg-blue-100 text-blue-800'
        case 'shipped': return 'bg-purple-100 text-purple-800'
        case 'delivered': return 'bg-green-100 text-green-800'
        case 'cancelled': return 'bg-red-100 text-red-800'
        default: return 'bg-gray-100 text-gray-800'
    }
})

const paymentStatusColor = computed(() => {
    switch (props.order.payment_status) {
        case 'pending': return 'bg-yellow-100 text-yellow-800'
        case 'paid': return 'bg-green-100 text-green-800'
        case 'failed': return 'bg-red-100 text-red-800'
        case 'refunded': return 'bg-gray-100 text-gray-800'
        default: return 'bg-gray-100 text-gray-800'
    }
})

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    })
}
</script>

<template>
    <Head :title="`Order #${order.order_number} - Medroid`" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900">Order #{{ order.order_number }}</h1>
                                <p class="text-gray-600 mt-1">Placed on {{ formatDate(order.created_at) }}</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span :class="statusColor" class="px-3 py-1 rounded-full text-sm font-medium">
                                    {{ order.status.charAt(0).toUpperCase() + order.status.slice(1) }}
                                </span>
                                <span :class="paymentStatusColor" class="px-3 py-1 rounded-full text-sm font-medium">
                                    {{ order.payment_status.charAt(0).toUpperCase() + order.payment_status.slice(1) }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Order Items -->
                    <div class="lg:col-span-2">
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">Order Items</h2>
                                
                                <div class="space-y-4">
                                    <div v-for="item in order.items" :key="item.id" class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                                        <div class="flex-shrink-0">
                                            <img
                                                :src="item.product?.images?.[0]?.url || '/images/placeholder.jpg'"
                                                :alt="item.product_name"
                                                class="w-16 h-16 object-cover rounded-lg"
                                            />
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <h3 class="text-sm font-medium text-gray-900">{{ item.product_name }}</h3>
                                            <p class="text-sm text-gray-500">SKU: {{ item.product_sku }}</p>
                                            <p class="text-sm text-gray-500">Type: {{ item.product_type }}</p>
                                            <p class="text-sm text-gray-500">Quantity: {{ item.quantity }}</p>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-sm font-medium text-gray-900">${{ parseFloat(item.total_price).toFixed(2) }}</p>
                                            <p class="text-sm text-gray-500">${{ parseFloat(item.unit_price).toFixed(2) }} each</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Digital Downloads -->
                                <div v-if="order.digital_downloads && order.digital_downloads.length > 0" class="mt-6">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Digital Downloads</h3>
                                    <div class="space-y-2">
                                        <div v-for="download in order.digital_downloads" :key="download.id" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                            <div>
                                                <p class="text-sm font-medium text-gray-900">{{ download.file_name }}</p>
                                                <p class="text-sm text-gray-500">{{ download.file_size }} MB</p>
                                            </div>
                                            <a
                                                :href="`/download/${download.download_token}`"
                                                class="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                                                target="_blank"
                                            >
                                                Download
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="lg:col-span-1 space-y-6">
                        <!-- Order Totals -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>
                                
                                <div class="space-y-2">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Subtotal</span>
                                        <span class="text-gray-900">{{ formattedSubtotal }}</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Tax</span>
                                        <span class="text-gray-900">{{ formattedTax }}</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Shipping</span>
                                        <span class="text-gray-900">{{ formattedShipping }}</span>
                                    </div>
                                    <div class="border-t border-gray-200 pt-2">
                                        <div class="flex justify-between text-lg font-semibold">
                                            <span class="text-gray-900">Total</span>
                                            <span class="text-gray-900">{{ formattedTotal }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Shipping Address -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">Shipping Address</h2>
                                
                                <div v-if="order.shipping_address" class="text-sm text-gray-600">
                                    <p class="font-medium text-gray-900">
                                        {{ order.shipping_address.first_name }} {{ order.shipping_address.last_name }}
                                    </p>
                                    <p>{{ order.shipping_address.address_line_1 }}</p>
                                    <p v-if="order.shipping_address.address_line_2">{{ order.shipping_address.address_line_2 }}</p>
                                    <p>{{ order.shipping_address.city }}, {{ order.shipping_address.state }} {{ order.shipping_address.postal_code }}</p>
                                    <p>{{ order.shipping_address.country }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Billing Address -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">Billing Address</h2>
                                
                                <div v-if="order.billing_address" class="text-sm text-gray-600">
                                    <p class="font-medium text-gray-900">
                                        {{ order.billing_address.first_name }} {{ order.billing_address.last_name }}
                                    </p>
                                    <p>{{ order.billing_address.address_line_1 }}</p>
                                    <p v-if="order.billing_address.address_line_2">{{ order.billing_address.address_line_2 }}</p>
                                    <p>{{ order.billing_address.city }}, {{ order.billing_address.state }} {{ order.billing_address.postal_code }}</p>
                                    <p>{{ order.billing_address.country }}</p>
                                    <p v-if="order.billing_address.email" class="mt-2">{{ order.billing_address.email }}</p>
                                    <p v-if="order.billing_address.phone">{{ order.billing_address.phone }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">Actions</h2>
                                
                                <div class="space-y-3">
                                    <Link 
                                        href="/shop/orders" 
                                        class="w-full inline-flex justify-center items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                                    >
                                        Back to Orders
                                    </Link>
                                    
                                    <Link 
                                        href="/shop" 
                                        class="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                    >
                                        Continue Shopping
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
