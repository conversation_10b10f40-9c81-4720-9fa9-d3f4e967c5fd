<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class DeepgramService
{
    private Client $client;
    private string $apiKey;
    private string $apiUrl;
    private string $model;
    private string $language;

    public function __construct()
    {
        $this->client = new Client();
        $this->apiKey = config('services.deepgram.api_key');
        $this->apiUrl = config('services.deepgram.api_url');
        $this->model = config('services.deepgram.model');
        $this->language = config('services.deepgram.language');
    }

    /**
     * Transcribe audio from URL
     */
    public function transcribeFromUrl(string $audioUrl, array $options = []): array
    {
        try {
            $defaultOptions = [
                'model' => $this->model,
                'language' => $this->language,
                'punctuate' => true,
                'diarize' => true,
                'smart_format' => true,
                'utterances' => true,
                'detect_language' => false,
            ];

            $options = array_merge($defaultOptions, $options);

            $response = $this->client->post($this->apiUrl, [
                'headers' => [
                    'Authorization' => 'Token ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'url' => $audioUrl,
                ] + $options,
                'timeout' => 300, // 5 minutes timeout
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            Log::info('Deepgram transcription successful', [
                'audio_url' => $audioUrl,
                'response_size' => strlen($response->getBody()->getContents()),
            ]);

            return [
                'success' => true,
                'data' => $data,
            ];

        } catch (RequestException $e) {
            Log::error('Deepgram API request failed', [
                'audio_url' => $audioUrl,
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
            ]);

            return [
                'success' => false,
                'error' => 'Transcription failed: ' . $e->getMessage(),
            ];
        } catch (\Exception $e) {
            Log::error('Deepgram transcription error', [
                'audio_url' => $audioUrl,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Unexpected error during transcription: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Transcribe audio from file
     */
    public function transcribeFromFile(string $filePath, array $options = []): array
    {
        try {
            if (!file_exists($filePath)) {
                return [
                    'success' => false,
                    'error' => 'Audio file not found',
                ];
            }

            $defaultOptions = [
                'model' => $this->model,
                'language' => $this->language,
                'punctuate' => true,
                'diarize' => true,
                'smart_format' => true,
                'utterances' => true,
                'detect_language' => false,
            ];

            $options = array_merge($defaultOptions, $options);

            // Build query parameters
            $queryParams = http_build_query($options);

            $response = $this->client->post($this->apiUrl . '?' . $queryParams, [
                'headers' => [
                    'Authorization' => 'Token ' . $this->apiKey,
                    'Content-Type' => 'audio/wav', // Adjust based on file type
                ],
                'body' => file_get_contents($filePath),
                'timeout' => 300, // 5 minutes timeout
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            Log::info('Deepgram file transcription successful', [
                'file_path' => $filePath,
                'file_size' => filesize($filePath),
            ]);

            return [
                'success' => true,
                'data' => $data,
            ];

        } catch (RequestException $e) {
            Log::error('Deepgram file transcription failed', [
                'file_path' => $filePath,
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
            ]);

            return [
                'success' => false,
                'error' => 'File transcription failed: ' . $e->getMessage(),
            ];
        } catch (\Exception $e) {
            Log::error('Deepgram file transcription error', [
                'file_path' => $filePath,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Unexpected error during file transcription: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Start real-time transcription session
     */
    public function startRealTimeTranscription(array $options = []): array
    {
        try {
            $defaultOptions = [
                'model' => $this->model,
                'language' => $this->language,
                'punctuate' => true,
                'diarize' => true,
                'smart_format' => true,
                'interim_results' => true,
                'encoding' => 'linear16',
                'sample_rate' => 16000,
                'channels' => 1,
            ];

            $options = array_merge($defaultOptions, $options);

            // Build WebSocket URL
            $wsUrl = str_replace('https://', 'wss://', $this->apiUrl);
            $wsUrl = str_replace('/v1/listen', '/v1/listen', $wsUrl);
            
            $queryParams = http_build_query($options);
            $wsUrl .= '?' . $queryParams;

            Log::info('Deepgram real-time transcription session started', [
                'ws_url' => $wsUrl,
                'options' => $options,
            ]);

            return [
                'success' => true,
                'ws_url' => $wsUrl,
                'auth_header' => 'Authorization: Token ' . $this->apiKey,
                'options' => $options,
            ];

        } catch (\Exception $e) {
            Log::error('Deepgram real-time transcription setup failed', [
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Failed to setup real-time transcription: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Extract transcript text from Deepgram response
     */
    public function extractTranscriptText(array $deepgramResponse): string
    {
        try {
            $transcript = '';
            
            if (isset($deepgramResponse['results']['channels'][0]['alternatives'][0]['transcript'])) {
                $transcript = $deepgramResponse['results']['channels'][0]['alternatives'][0]['transcript'];
            }
            
            return trim($transcript);
        } catch (\Exception $e) {
            Log::error('Failed to extract transcript text', [
                'error' => $e->getMessage(),
                'response' => $deepgramResponse,
            ]);
            
            return '';
        }
    }

    /**
     * Extract utterances with timestamps and speaker information
     */
    public function extractUtterances(array $deepgramResponse): array
    {
        try {
            $utterances = [];
            
            if (isset($deepgramResponse['results']['utterances'])) {
                foreach ($deepgramResponse['results']['utterances'] as $utterance) {
                    $utterances[] = [
                        'speaker' => $utterance['speaker'] ?? 0,
                        'transcript' => $utterance['transcript'] ?? '',
                        'start' => $utterance['start'] ?? 0,
                        'end' => $utterance['end'] ?? 0,
                        'confidence' => $utterance['confidence'] ?? 0,
                    ];
                }
            }
            
            return $utterances;
        } catch (\Exception $e) {
            Log::error('Failed to extract utterances', [
                'error' => $e->getMessage(),
                'response' => $deepgramResponse,
            ]);
            
            return [];
        }
    }

    /**
     * Check if Deepgram API is configured
     */
    public function isConfigured(): bool
    {
        return !empty($this->apiKey);
    }
}