<?php

namespace App\Console\Commands;

use App\Models\DeviceToken;
use App\Models\User;
use Illuminate\Console\Command;

class TestFlutterDeviceInfo extends Command
{
    protected $signature = 'test:flutter-device-info';
    protected $description = 'Test Flutter device information capture';

    public function handle()
    {
        $this->info('Testing Flutter Device Information Integration...');

        // Show recent device tokens with enhanced info
        $recentTokens = DeviceToken::with('user:id,name,email')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        if ($recentTokens->isEmpty()) {
            $this->warn('No device tokens found. Register a device from Flutter app first.');
            return;
        }

        $this->info('Recent Device Tokens:');
        $this->line('');

        foreach ($recentTokens as $token) {
            $this->line("User: {$token->user->name} ({$token->user->email})");
            $this->line("Device Type: {$token->device_type}");
            $this->line("Platform: {$token->platform}");
            $this->line("Browser: {$token->browser}");
            $this->line("Device Model: {$token->device_model}");
            $this->line("App Version: {$token->app_version}");
            $this->line("IP Address: {$token->ip_address}");
            $this->line("Last Used: {$token->last_used_at}");
            $this->line("User Agent: " . substr($token->user_agent ?? 'N/A', 0, 80) . '...');
            $this->line('---');
        }

        // Show device statistics
        $this->info('Device Statistics:');
        
        $deviceTypes = DeviceToken::selectRaw('device_type, COUNT(*) as count')
            ->groupBy('device_type')
            ->get();
            
        foreach ($deviceTypes as $stat) {
            $this->line("{$stat->device_type}: {$stat->count} devices");
        }

        $this->line('');
        $platforms = DeviceToken::selectRaw('platform, COUNT(*) as count')
            ->whereNotNull('platform')
            ->groupBy('platform')
            ->get();
            
        foreach ($platforms as $stat) {
            $this->line("{$stat->platform}: {$stat->count} devices");
        }

        $this->info('Flutter Device Information test completed!');
    }
}
