<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Patient;
use App\Models\PersonalAccessToken;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class FlutterScenarioTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test the exact scenario from Flutter <NAME_EMAIL> logs in
     * and then makes multiple API calls that were failing with 401 errors
     */
    public function test_exact_flutter_scenario_from_logs()
    {
        // Create the exact user from the logs
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'patient',
        ]);

        // Create patient profile (required for appointments endpoint)
        Patient::create([
            'user_id' => $user->id,
            'emergency_contact_name' => 'Emergency Contact',
            'emergency_contact_phone' => '**********',
        ]);

        // Step 1: Login with exact Flutter request data
        $loginResponse = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'device_type' => 'android',
            'user_agent' => 'medroid_app/1.0.0 (Android Android 15; RMX3771)',
            'device_model' => 'RMX3771',
            'platform' => 'Android',
            'app_version' => '1.0.0',
        ], [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ]);

        // Verify login was successful
        $loginResponse->assertStatus(200);
        $responseData = $loginResponse->json();
        
        $this->assertArrayHasKey('access_token', $responseData);
        $this->assertArrayHasKey('user', $responseData);
        $this->assertArrayHasKey('token_type', $responseData);
        $this->assertEquals('Bearer', $responseData['token_type']);
        
        $token = $responseData['access_token'];
        $this->assertNotEmpty($token);
        $this->assertStringContainsString('|', $token);

        // Verify token is properly stored in database
        $accessToken = PersonalAccessToken::findToken($token);
        $this->assertNotNull($accessToken, 'Token should be found in database');
        $this->assertEquals($user->id, $accessToken->user_id);

        // Step 2: Make the exact sequence of API calls that were failing in Flutter
        $headers = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $token,
        ];

        // These are the exact API calls from the Flutter logs that were returning 401
        $apiCalls = [
            ['method' => 'GET', 'url' => '/api/appointments-list'],
            ['method' => 'GET', 'url' => '/api/feed?page=1'],
            ['method' => 'GET', 'url' => '/api/feed/topics'],
            ['method' => 'GET', 'url' => '/api/providers/specializations'],
            ['method' => 'GET', 'url' => '/api/notifications/unread-count'],
            ['method' => 'POST', 'url' => '/api/chat/start', 'data' => []],
            ['method' => 'GET', 'url' => '/api/chat/history?per_page=20'],
            ['method' => 'GET', 'url' => '/api/chat/history?per_page=50'],
        ];

        foreach ($apiCalls as $call) {
            if ($call['method'] === 'GET') {
                $response = $this->withHeaders($headers)->getJson($call['url']);
            } else {
                $response = $this->withHeaders($headers)->postJson($call['url'], $call['data'] ?? []);
            }
            
            // All these calls should now return 200 instead of 401
            $response->assertStatus(200, "API call {$call['method']} {$call['url']} should return 200, not 401");
        }

        // Step 3: Verify token is still valid after all requests
        $accessToken->refresh();
        $this->assertNotNull($accessToken->last_used_at, 'Token should have last_used_at updated');
        
        // Step 4: Verify user endpoint still works (this was working in the logs)
        $userResponse = $this->withHeaders($headers)->getJson('/api/user');
        $userResponse->assertStatus(200)
                     ->assertJson([
                         'id' => $user->id,
                         'email' => '<EMAIL>',
                         'role' => 'patient',
                     ]);

        // Step 5: Verify public endpoints work without authentication (these were working)
        $publicResponse = $this->getJson('/api/public/providers');
        $publicResponse->assertStatus(200);
    }

    /**
     * Test that tokens don't expire unexpectedly like they were in the Flutter app
     */
    public function test_tokens_do_not_expire_unexpectedly()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'patient',
        ]);

        Patient::create([
            'user_id' => $user->id,
            'emergency_contact_name' => 'Emergency Contact',
            'emergency_contact_phone' => '**********',
        ]);

        // Login
        $loginResponse = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'device_name' => 'flutter-app',
        ]);

        $token = $loginResponse->json('access_token');
        $headers = [
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json',
        ];

        // Make requests over time to simulate real app usage
        for ($i = 0; $i < 10; $i++) {
            // Simulate some time passing
            if ($i > 0) {
                sleep(1);
            }

            $response = $this->withHeaders($headers)->getJson('/api/user');
            $response->assertStatus(200, "Request $i should not fail with 401");

            $appointmentsResponse = $this->withHeaders($headers)->getJson('/api/appointments-list');
            $appointmentsResponse->assertStatus(200, "Appointments request $i should not fail with 401");
        }

        // Token should still be valid
        $accessToken = PersonalAccessToken::findToken($token);
        $this->assertNotNull($accessToken, 'Token should still exist after multiple requests');
        $this->assertEquals($user->id, $accessToken->user_id);
    }

    /**
     * Test that the authentication system handles the exact headers Flutter sends
     */
    public function test_flutter_headers_are_handled_correctly()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'patient',
        ]);

        // Login with Flutter-style headers
        $loginResponse = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'device_type' => 'android',
            'user_agent' => 'medroid_app/1.0.0 (Android Android 15; RMX3771)',
            'device_model' => 'RMX3771',
            'platform' => 'Android',
            'app_version' => '1.0.0',
        ], [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ]);

        $loginResponse->assertStatus(200);
        $token = $loginResponse->json('access_token');

        // Test with exact Flutter headers
        $flutterHeaders = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $token,
        ];

        $response = $this->withHeaders($flutterHeaders)->getJson('/api/user');
        $response->assertStatus(200);

        // Verify the response structure matches what Flutter expects
        $userData = $response->json();
        $this->assertArrayHasKey('id', $userData);
        $this->assertArrayHasKey('email', $userData);
        $this->assertArrayHasKey('role', $userData);
        $this->assertEquals($user->id, $userData['id']);
        $this->assertEquals($user->email, $userData['email']);
    }
}
