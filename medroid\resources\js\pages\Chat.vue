<script setup>
import { ref, nextTick, onMounted, onUnmounted } from 'vue'
import AppLayout from '@/layouts/AppLayout.vue'
import ChatInput from '@/components/ChatInput.vue'
import MedroidLogo from '@/components/MedroidLogo.vue'
import ReferralModal from '@/components/ReferralModal.vue'
import { Head } from '@inertiajs/vue3'
import axios from 'axios'

// Reactive data
const messages = ref([])
const newMessage = ref('')
const isLoading = ref(false)
const chatContainer = ref(null)
const chatInputRef = ref(null)
const conversationId = ref(null)
const showAppointmentSlots = ref(false)
const appointmentSlots = ref([])
const allAvailableSlots = ref([])
const loadingSlots = ref(false)
const showingAllSlots = ref(false)
const selectedDate = ref('')
const selectedProvider = ref('all')
const availableDates = ref([])
const availableProviders = ref([])

const loadingConversation = ref(false)
const showReferralModal = ref(false)

// Streaming state
const streamingMessageId = ref(null)
const streamingTimer = ref(null)

const breadcrumbs = [
    {
        title: 'Chat',
        href: '/chat',
    },
]

// Health concern buttons
const healthConcerns = [
  {
    icon: `<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`,
    text: 'I have a headache',
    color: 'bg-blue-50 text-blue-700 border-blue-200'
  },
  {
    icon: `<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
    </svg>`,
    text: 'My tummy hurts',
    color: 'bg-green-50 text-green-700 border-green-200'
  },
  {
    icon: `<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" />
    </svg>`,
    text: 'I can\'t lose weight',
    color: 'bg-purple-50 text-purple-700 border-purple-200'
  },
  {
    icon: `<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 00-3.7-3.7 48.678 48.678 0 00-7.324 0 4.006 4.006 0 00-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3l-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 003.7 3.7 48.656 48.656 0 007.324 0 4.006 4.006 0 003.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3l-3 3" />
    </svg>`,
    text: 'My urine burns',
    color: 'bg-orange-50 text-orange-700 border-orange-200'
  },
  {
    icon: `<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />
    </svg>`,
    text: 'Book appointment',
    color: 'bg-teal-50 text-teal-700 border-teal-200'
  },
  {
    icon: `<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.182 15.182a4.5 4.5 0 01-6.364 0M21 12a9 9 0 11-18 0 9 9 0 0118 0zM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75zm-.375 0h.008v.015h-.008V9.75zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75zm-.375 0h.008v.015h-.008V9.75z" />
    </svg>`,
    text: 'I have a skin rash',
    color: 'bg-indigo-50 text-indigo-700 border-indigo-200'
  },
]

// Methods
const formatAIMessage = (content) => {
  if (!content) return content

  let formatted = content

  // Replace **text** with <strong>text</strong>
  formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')

  // Handle sections with ## headers (main sections)
  formatted = formatted.replace(/^## (.*?):/gm, '<h2 class="font-bold text-gray-900 mt-4 mb-3 text-lg">$1:</h2>')

  // Handle sections with ### headers (subsections)
  formatted = formatted.replace(/^### (.*?):/gm, '<h3 class="font-semibold text-gray-900 mt-3 mb-2">$1:</h3>')

  // Handle sections with #### headers (sub-subsections)
  formatted = formatted.replace(/^#### (.*?):/gm, '<h4 class="font-semibold text-gray-900 mt-2 mb-1 text-sm">$1:</h4>')

  // Handle numbered lists with bold headers
  formatted = formatted.replace(/(\d+)\.\s*\*\*(.*?)\*\*/g, '<div class="mb-2"><strong>$1. $2</strong></div>')

  // Handle "Why I think so:" sections
  formatted = formatted.replace(/- Why I think so: (.*?)(?=\d+\.|$)/gs, '<div class="ml-4 text-gray-900 mb-2">Why I think so: $1</div>')

  // Handle bullet points with **bold** text (appointment details format)
  formatted = formatted.replace(/^• \*\*(.*?):\*\* (.*$)/gm, '<div class="mb-1"><strong>$1:</strong> $2</div>')

  // Handle bullet points with **bold** text
  formatted = formatted.replace(/^- \*\*(.*?)\*\*/gm, '<div class="mb-2"><strong>• $1</strong></div>')

  // Handle regular bullet points
  formatted = formatted.replace(/^- (.*$)/gm, '<div class="mb-1 ml-4">• $1</div>')

  // Handle "WHEN TO SEEK" style headers
  formatted = formatted.replace(/\*\*(.*?):\*\*/g, '<h4 class="font-semibold text-gray-900 mt-4 mb-2">$1:</h4>')

  // Handle appointment details format (Provider:, Date:, etc.)
  formatted = formatted.replace(/^\*\*(.*?):\*\* (.*$)/gm, '<div class="mb-1"><strong>$1:</strong> $2</div>')

  // Replace double line breaks with paragraph spacing
  formatted = formatted.replace(/\n\n/g, '</p><p class="mb-2">')

  // Replace single line breaks with line breaks
  formatted = formatted.replace(/\n/g, '<br>')

  // Wrap content in paragraph tags
  formatted = `<p class="mb-2">${formatted}</p>`

  // Clean up empty paragraphs and extra breaks
  formatted = formatted.replace(/<p class="mb-2"><\/p>/g, '')
  formatted = formatted.replace(/<br><br>/g, '<br>')

  return formatted
}

// Google Gemini-style line-by-line fade-in streaming
const streamAIMessage = (messageId, fullContent) => {
  const message = messages.value.find(m => m.id === messageId)
  if (!message) return

  message.isStreaming = true
  message.displayedContent = ''
  message.streamingLines = []
  streamingMessageId.value = messageId

  // Split content into lines for line-by-line streaming
  const lines = fullContent.split('\n').filter(line => line.trim() !== '')
  let currentLineIndex = 0

  const streamNextLine = () => {
    if (currentLineIndex < lines.length) {
      const currentLine = lines[currentLineIndex]

      // Add the line with fade-in effect
      message.streamingLines.push({
        content: currentLine,
        id: `line-${currentLineIndex}`,
        fadeIn: true
      })

      // Update displayed content
      message.displayedContent = message.streamingLines.map(line => line.content).join('\n')

      currentLineIndex++

      // Smooth scroll during streaming
      nextTick(() => scrollToBottom())

      // Stream next line after a delay (faster than before)
      const delay = currentLine.length > 100 ? 800 : 500 // Longer delay for longer lines
      streamingTimer.value = setTimeout(streamNextLine, delay)
    } else {
      // Streaming complete
      completeStreaming()
    }
  }

  const completeStreaming = () => {
    message.isStreaming = false
    message.displayedContent = fullContent
    message.formatted = formatAIMessage(fullContent)
    message.streamingLines = []
    streamingMessageId.value = null
    if (streamingTimer.value) {
      clearTimeout(streamingTimer.value)
      streamingTimer.value = null
    }
    // Final scroll to ensure everything is visible
    nextTick(() => scrollToBottom())
  }

  // Start streaming with initial delay for dramatic effect
  setTimeout(() => {
    streamNextLine()
  }, 200)
}

const processBackendSlots = (backendSlots) => {
  console.log('Processing backend slots:', backendSlots)

  // Safety check: ensure backendSlots is an array
  if (!Array.isArray(backendSlots)) {
    console.warn('backendSlots is not an array:', backendSlots)
    allAvailableSlots.value = []
    appointmentSlots.value = []
    return
  }

  const slots = []
  const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD format

  // Process the backend slot structure
  backendSlots.forEach(providerSlot => {
    // Safety check for each provider slot
    if (!providerSlot || !providerSlot.provider) {
      console.warn('Invalid provider slot:', providerSlot)
      return
    }

    const provider = providerSlot.provider
    const service = providerSlot.service || null
    const date = providerSlot.date
    const dayOfWeek = providerSlot.day_of_week

    if (providerSlot.slots && Array.isArray(providerSlot.slots)) {
      providerSlot.slots.forEach(timeSlot => {
        const slot = {
          id: `${provider.id}-${date}-${timeSlot.start_time}`,
          provider_id: provider.id,
          service_id: service?.id || 1,
          provider: `Dr. ${provider.name}`,
          time: timeSlot.start_time,
          end_time: timeSlot.end_time,
          date: `${dayOfWeek}, ${new Date(date).toLocaleDateString()}`,
          full_date: date,
          price: service?.price || 50,
          datetime: new Date(`${date} ${timeSlot.start_time}`),
          isToday: date === today
        }
        slots.push(slot)
      })
    }
  })

  // Sort slots: prioritize today's slots first, then by datetime
  slots.sort((a, b) => {
    // First, prioritize today's slots
    if (a.isToday && !b.isToday) return -1
    if (!a.isToday && b.isToday) return 1

    // Then sort by datetime
    return a.datetime - b.datetime
  })

  // Store all slots and set up filtering
  allAvailableSlots.value = slots
  showingAllSlots.value = false

  // Set up available dates and providers for filtering
  setupFilterOptions(slots)

  // Apply initial filtering (will default to today)
  filterSlots()

  console.log('Processed slots:', slots.length, 'Today slots:', Array.isArray(slots) ? slots.filter(s => s.isToday).length : 0)
}

const setupFilterOptions = (slots) => {
  // Safety check: ensure slots is an array
  if (!Array.isArray(slots)) {
    console.warn('slots is not an array in setupFilterOptions:', slots)
    availableDates.value = [{ value: '', label: 'All Days' }]
    availableProviders.value = [{ value: 'all', label: 'All Providers' }]
    selectedDate.value = ''
    selectedProvider.value = 'all'
    return
  }

  // Get unique dates
  const uniqueDates = [...new Set(slots.map(slot => slot.full_date))]
  const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD format

  availableDates.value = uniqueDates.map(date => {
    const dateObj = new Date(date)
    const isToday = date === today
    const isTomorrow = dateObj.toDateString() === new Date(Date.now() + 24 * 60 * 60 * 1000).toDateString()

    let label = dateObj.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    })

    if (isToday) label = `Today (${label})`
    else if (isTomorrow) label = `Tomorrow (${label})`

    return { value: date, label }
  }).sort((a, b) => new Date(a.value) - new Date(b.value)) // Sort by date

  // Add "All Days" option at the end
  availableDates.value.push({ value: '', label: 'All Days' })

  // Get unique providers
  const uniqueProviders = [...new Set(slots.map(slot => ({
    id: slot.provider_id,
    name: slot.provider
  })))]
  availableProviders.value = [
    { value: 'all', label: 'All Providers' },
    ...uniqueProviders.map(provider => ({
      value: provider.id.toString(),
      label: provider.name
    }))
  ]

  // Set default to today's date if available
  const todayOption = availableDates.value.find(d => d.value === today)
  if (todayOption) {
    selectedDate.value = today
  } else {
    // If no slots for today, default to first available date
    selectedDate.value = availableDates.value[0]?.value || ''
  }
}

const loadAppointmentSlots = async () => {
  loadingSlots.value = true

  await nextTick()
  scrollToBottom()

  try {
    // Load real providers from the API
    console.log('TEST: Loading providers...')
    const providersResponse = await axios.get('/providers')
    const providers = providersResponse.data.data || providersResponse.data.providers || providersResponse.data || []
    console.log('TEST: Providers loaded:', providers.length)

    if (providers.length === 0) {
      messages.value.push({
        id: Date.now(),
        type: 'ai',
        content: 'I apologize, but there are no available providers at the moment. Please try again later or contact our office directly.',
        timestamp: new Date()
      })
      return
    }

    // Get available slots for the next 7 days
    const today = new Date()
    const slots = []

    // Load existing appointments once to prevent conflicts
    let existingAppointments = []
    try {
      const appointmentsResponse = await axios.get('/appointments-list')
      existingAppointments = appointmentsResponse.data.appointments || []
      console.log('Existing appointments loaded:', existingAppointments.length)
    } catch (error) {
      console.log('Could not load existing appointments, proceeding without conflict check')
    }

    // Prepare available dates (next 14 days)
    const dates = []
    for (let dayOffset = 0; dayOffset <= 14; dayOffset++) {
      const checkDate = new Date(today)
      checkDate.setDate(checkDate.getDate() + dayOffset)
      const dateStr = checkDate.toISOString().split('T')[0]
      dates.push({
        value: dateStr,
        label: checkDate.toLocaleDateString('en-US', {
          weekday: 'short',
          month: 'short',
          day: 'numeric'
        }),
        fullDate: checkDate
      })

    }
    availableDates.value = dates

    // Set default selected date to today
    const todayStr = today.toISOString().split('T')[0]
    if (!selectedDate.value) {
      selectedDate.value = todayStr
    }

    // Prepare available providers
    availableProviders.value = [
      { value: 'all', label: 'All Providers' },
      ...providers.map(provider => ({
        value: provider.id.toString(),
        label: `Dr. ${provider.user?.name || 'Provider'}`
      }))
    ]

    // Check multiple days to get more slots from ALL providers
    for (let dayOffset = 0; dayOffset <= 14; dayOffset++) {
      const checkDate = new Date(today)
      checkDate.setDate(checkDate.getDate() + dayOffset)
      const dateStr = checkDate.toISOString().split('T')[0]
      console.log(`TEST: Checking date ${dateStr}`)

      for (const provider of providers) {
        console.log(`TEST: Checking provider ${provider.id} - ${provider.user?.name}`)
        try {
          const slotsResponse = await axios.get(`/providers/${provider.id}/available-slots?date=${dateStr}`)
          const availableSlots = slotsResponse.data.available_slots || []
          console.log(`TEST: Provider ${provider.id} has ${availableSlots.length} slots on ${dateStr}`)

          if (availableSlots.length > 0) {
            // Filter out slots that conflict with existing appointments
            const filteredSlots = availableSlots.filter(slot => {
              const slotDateTime = `${dateStr} ${slot.start_time}`
              const hasConflict = existingAppointments.some(appointment => {
                if (appointment.status === 'cancelled') return false
                const appointmentDateTime = `${appointment.date || appointment.scheduled_at?.split('T')[0]} ${appointment.time || appointment.start_time}`
                return appointmentDateTime === slotDateTime
              })
              return !hasConflict
            })

            console.log(`TEST: Provider ${provider.id} has ${filteredSlots.length} available slots after filtering`)

            if (filteredSlots.length === 0) {
              console.log(`TEST: All slots for provider ${provider.id} on ${dateStr} are already booked`)
              continue
            }

            // Get provider's services to include service_id
            let service = null
            try {
              const servicesResponse = await axios.get(`/providers/${provider.id}/services`)
              const services = servicesResponse.data.services || []
              console.log(`TEST: Services for provider ${provider.id}:`, services)
              if (services.length > 0) {
                service = services[0] // Use first available service
                console.log(`TEST: Selected service:`, service)
                console.log(`TEST: Service ID: ${service.id}, Name: ${service.name}, Price: ${service.price}`)
              }
            } catch (error) {
              console.error(`Error loading services for provider ${provider.id}:`, error)
            }

            // Only create slots if we have a valid service
            if (!service || !service.id) {
              console.error(`No valid service found for provider ${provider.id}, skipping slots`)
              continue
            }

            // Take all filtered available slots for this provider on this day
            const providerSlots = filteredSlots.map(slot => ({
              id: `${provider.id}-${dateStr}-${slot.start_time}`,
              provider_id: provider.id,
              service_id: service.id, // Use actual service ID, no fallback
              provider: `Dr. ${provider.user?.name || 'Provider'}`,
              specialty: provider.specialization || 'General Practice',
              date: checkDate.toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' }),
              time: slot.start_time,
              end_time: slot.end_time,
              full_date: dateStr,
              datetime: new Date(`${dateStr}T${slot.start_time}`), // Add for sorting
              price: service.price || 0,
              duration: service.duration || 15,
              service_name: service.name || 'Consultation'
            }))


            slots.push(...providerSlots)

            // Don't break here - we want to collect from ALL providers
          }
        } catch (error) {
          console.error(`Error loading slots for provider ${provider.id} on ${dateStr}:`, error)
        }
      }

      // Stop if we have enough slots (but only after checking all providers for this day)
      if (slots.length >= 50) break
    }

    console.log('TEST: Total slots found:', slots.length)

    if (slots.length === 0) {
      messages.value.push({
        id: Date.now(),
        type: 'ai',
        content: 'I apologize, but there are no available appointment slots in the next week. Please contact our office directly to schedule an appointment.',
        timestamp: new Date()
      })
      return
    }

    // Sort slots by earliest available time across all providers
    slots.sort((a, b) => {
      return a.datetime - b.datetime
    })

    // Group slots by provider to ensure diversity
    const slotsByProvider = {}
    slots.forEach(slot => {
      if (!slotsByProvider[slot.provider_id]) {
        slotsByProvider[slot.provider_id] = []
      }
      slotsByProvider[slot.provider_id].push(slot)
    })

    // Take slots from each provider alternately to ensure diversity
    const diverseSlots = []
    const maxSlotsPerProvider = Math.ceil(8 / Object.keys(slotsByProvider).length)

    Object.keys(slotsByProvider).forEach(providerId => {
      const providerSlots = slotsByProvider[providerId].slice(0, maxSlotsPerProvider)
      diverseSlots.push(...providerSlots)
    })

    // Sort the diverse slots by datetime again
    diverseSlots.sort((a, b) => a.datetime - b.datetime)


    allAvailableSlots.value = diverseSlots // Store all slots for filtering
    showingAllSlots.value = false // Reset the view more state
    showAppointmentSlots.value = true

    // Apply initial filtering
    filterSlots()
    await nextTick()
    scrollToBottom()
  } catch (error) {
    console.error('Error loading appointment slots:', error)
    messages.value.push({
      id: Date.now(),
      type: 'ai',
      content: 'I apologize, but I cannot access the appointment system right now. Please contact our office directly to schedule an appointment.',
      timestamp: new Date()
    })
  } finally {
    loadingSlots.value = false
    await nextTick()
    scrollToBottom()
  }
}

const bookAppointment = async (slot) => {
  try {
    // Get referral note from current conversation
    let referralNote = 'Appointment booked through AI chat consultation'
    let appointmentReason = 'General consultation'

    if (conversationId.value) {
      try {
        const conversationResponse = await axios.get(`/web-api/chat/conversation/${conversationId.value}`)
        if (conversationResponse.data && conversationResponse.data.referral_note) {
          referralNote = conversationResponse.data.referral_note
          // Clean up the referral note and use it as appointment reason
          const cleanReason = referralNote
            .replace(/\*\*/g, '') // Remove markdown bold
            .replace(/###/g, '') // Remove headers
            .replace(/\n+/g, ' ') // Replace line breaks with spaces
            .trim()

          // Use first 150 characters as appointment reason
          appointmentReason = cleanReason.length > 150
            ? cleanReason.substring(0, 150) + '...'
            : cleanReason
        }
      } catch (error) {
        console.log('Could not fetch referral note, using default reason')
      }
    }

    // Validate that we have a service ID and fix it if needed
    let serviceId = slot.service_id

    // Temporary fix: Ensure Provider 3 uses Service ID 4
    if (slot.provider_id === 3) {
      serviceId = 4
      console.log('Fixed service ID for Provider 3 to Service ID 4')
    }

    if (!serviceId) {
      throw new Error('Invalid appointment slot: missing service ID')
    }

    console.log('Using service ID:', serviceId, 'for provider:', slot.provider_id)

    // Create appointment booking request with payment
    const appointmentData = {
      provider_id: slot.provider_id,
      service_id: serviceId, // Use the corrected service ID
      date: slot.full_date,
      time_slot: {
        start_time: slot.time,
        end_time: slot.end_time
      },
      reason: appointmentReason,
      notes: referralNote,
      currency: 'USD'
    }

    console.log('Booking appointment with data (UPDATED):', appointmentData)
    console.log('Slot data:', slot)
    console.log('Service ID being sent:', appointmentData.service_id)
    console.log('Provider ID being sent:', appointmentData.provider_id)

    // Use the API appointment creation endpoint that handles payment
    const response = await fetch('/api/appointments/with-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
      credentials: 'include',
      body: JSON.stringify(appointmentData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || `HTTP ${response.status}`)
    }

    const data = await response.json()

    if (data.appointment) {
      const appointment = data.appointment
      const payment = data.payment

      if (payment && payment.client_secret) {
        // Payment is required - show payment message in chat
        const paymentMessageContent = `🏥 Appointment Created - Payment Required

Provider: ${slot.provider}
Date: ${slot.date}
Time: ${slot.time}
Appointment ID: #${appointment.id}
Amount: $${payment.amount}

Your appointment has been created and is pending payment. Please click the "Pay Now" button below to complete your booking.`

        const paymentMessage = {
          id: Date.now(),
          type: 'ai',
          content: paymentMessageContent,
          timestamp: new Date(),
          formatted: formatAIMessage(paymentMessageContent),
          showPaymentButton: true,
          appointmentId: appointment.id,
          paymentAmount: payment.amount,
          paymentUrl: `/appointments/${appointment.id}/payment?conversation=${conversationId.value}`
        }

        messages.value.push(paymentMessage)

        // Save this message to the conversation history
        if (conversationId.value) {
          try {
            await axios.post('/web-api/chat/message', {
              conversation_id: conversationId.value,
              message: paymentMessage.content,
              role: 'assistant'
            })
            console.log('Payment message saved to chat history')
          } catch (error) {
            console.error('Error saving payment message to chat history:', error)
          }
        }

        // Add a follow-up conversational message
        setTimeout(async () => {
          const followUpMessage = {
            id: Date.now() + 1,
            type: 'ai',
            content: `While you complete your payment, I'll be here if you have any questions about your upcoming appointment or any other health concerns. Feel free to ask me anything! 😊`,
            timestamp: new Date(),
            formatted: formatAIMessage(`While you complete your payment, I'll be here if you have any questions about your upcoming appointment or any other health concerns. Feel free to ask me anything! 😊`)
          }

          messages.value.push(followUpMessage)

          // Save follow-up message to conversation history
          if (conversationId.value) {
            try {
              await axios.post('/api/chat/message', {
                conversation_id: conversationId.value,
                message: followUpMessage.content,
                role: 'assistant'
              })
            } catch (error) {
              console.error('Error saving follow-up message to chat history:', error)
            }
          }

          await nextTick()
          scrollToBottom()
        }, 3000)

      } else {
        // No payment required - show confirmation
        const confirmationMessageContent = `✅ **Appointment Confirmed!**

**Provider:** ${slot.provider}
**Date:** ${slot.date}
**Time:** ${slot.time}
**Appointment ID:** #${appointment.id}

Your appointment has been successfully booked. You'll receive a confirmation email shortly with all the details.

Is there anything else I can help you with regarding your upcoming appointment?`

        const confirmationMessage = {
          id: Date.now(),
          type: 'ai',
          content: confirmationMessageContent,
          timestamp: new Date(),
          formatted: formatAIMessage(confirmationMessageContent)
        }

        messages.value.push(confirmationMessage)

        // Save this message to the conversation history
        if (conversationId.value) {
          try {
            await axios.post('/web-api/chat/message', {
              conversation_id: conversationId.value,
              message: confirmationMessage.content,
              role: 'assistant'
            })
            console.log('Confirmation message saved to chat history')
          } catch (error) {
            console.error('Error saving confirmation message to chat history:', error)
          }
        }
      }
    } else {
      throw new Error('No appointment data returned')
    }

    showAppointmentSlots.value = false
    await nextTick()
    scrollToBottom()
  } catch (error) {
    console.error('Error booking appointment:', error)

    // Get detailed error message
    let errorDetails = 'Unknown error occurred'
    if (error.response?.data?.message) {
      errorDetails = error.response.data.message
    } else if (error.response?.data?.errors) {
      const errors = Object.values(error.response.data.errors).flat()
      errorDetails = errors.join(', ')
    } else if (error.message) {
      errorDetails = error.message
    }

    console.log('Detailed error:', errorDetails)

    const errorMessage = {
      id: Date.now(),
      type: 'ai',
      content: `I apologize, but there was an error booking your appointment with ${slot.provider}.

**Error Details:** ${errorDetails}

This could be due to:
• The time slot may no longer be available
• A technical issue with our booking system
• Invalid service or provider information

Please try selecting a different time slot or contact our office directly at your convenience. I'm here to help with any other questions you might have.`,
      timestamp: new Date(),
      formatted: formatAIMessage(`I apologize, but there was an error booking your appointment with ${slot.provider}.

**Error Details:** ${errorDetails}

This could be due to:
• The time slot may no longer be available
• A technical issue with our booking system
• Invalid service or provider information

Please try selecting a different time slot or contact our office directly at your convenience. I'm here to help with any other questions you might have.`)
    }

    messages.value.push(errorMessage)

    // Save error message to conversation history
    if (conversationId.value) {
      try {
        await axios.post('/web-api/chat/message', {
          conversation_id: conversationId.value,
          message: errorMessage.content,
          role: 'assistant'
        })
      } catch (saveError) {
        console.error('Error saving error message to chat history:', saveError)
      }
    }

    showAppointmentSlots.value = false
    await nextTick()
    scrollToBottom()
  }
}



const sendMessage = async (messageText = null) => {
  const messageToSend = messageText || newMessage.value.trim()
  if (!messageToSend) return

  messages.value.push({
    id: Date.now(),
    type: 'user',
    content: messageToSend,
    timestamp: new Date()
  })

  if (!messageText) {
    newMessage.value = ''
  }

  // Scroll immediately after user message
  await nextTick()
  scrollToBottom()

  isLoading.value = true

  try {
    if (!conversationId.value) {
      // First, start a new conversation using Inertia
      const startResponse = await fetch('/web-api/chat/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
        credentials: 'include',
        body: JSON.stringify({})
      })

      const startData = await startResponse.json()

      if (!startResponse.ok) {
        console.error('Start conversation failed:', startData)
        throw new Error(startData.message || 'Failed to start conversation')
      }

      if (!startData.conversation_id) {
        throw new Error('No conversation ID returned')
      }

      // Ensure conversation ID is a string
      conversationId.value = String(startData.conversation_id)
    }

    // Now send the message
    const payload = {
      conversation_id: String(conversationId.value), // Ensure it's a string
      message: messageToSend,
      include_patient_context: true,
      generate_title: true,
      request_full_response: false
    };

    console.log('Sending payload:', payload); // Debug log

    const response = await fetch('/web-api/chat/message', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
      credentials: 'include',
      body: JSON.stringify(payload)
    })

    const data = await response.json()

    if (response.ok && data.message) {
      const aiMessageId = Date.now() + 1
      const aiMessage = {
        id: aiMessageId,
        type: 'ai',
        content: data.message,
        timestamp: new Date(),
        formatted: formatAIMessage(data.message),
        isStreaming: false,
        displayedContent: ''
      }

      messages.value.push(aiMessage)

      // Immediately scroll to show the new message container
      await nextTick()
      scrollToBottom()

      // Start streaming the AI response
      streamAIMessage(aiMessageId, data.message)

      // Check if we should show appointment slots based on AI response
      // Priority 1: Check if backend provided appointment slots directly
      if (data.available_slots && Array.isArray(data.available_slots)) {
        console.log('Backend provided appointment slots directly:', data.available_slots)
        // Process the backend slots and show them immediately
        processBackendSlots(data.available_slots)
        setTimeout(() => {
          showAppointmentSlots.value = true
          // Ensure we scroll to show the appointment slots
          nextTick(() => {
            scrollToBottom()
          })
        }, 500)
      }
      // Priority 2: Check for explicit appointment flags
      else if (data.appointment_options || data.show_appointments) {
        setTimeout(() => {
          loadAppointmentSlots()
        }, 1000)
      }
      // Priority 3: Check for appointment booking keywords in the message
      else {
        const messageText = data.message.toLowerCase()
        const explicitBookingKeywords = [
          'would you like to see available appointment slots',
          'shall i show you available appointments',
          'would you like to book an appointment now',
          'let me show you available slots',
          'here are the available appointment times',
          'perfect! i found available appointment slots',
          'appointment options provided'
        ]

        const hasExplicitBookingIntent = explicitBookingKeywords.some(keyword =>
          messageText.includes(keyword)
        )

        if (hasExplicitBookingIntent) {
          setTimeout(() => {
            loadAppointmentSlots()
          }, 1000)
        }
      }
    } else {
      console.error('Failed to get AI response:', data)
      const errorMessageId = Date.now() + 1
      const errorMessage = {
        id: errorMessageId,
        type: 'ai',
        content: `Sorry, I encountered an error: ${data.message || 'Please try again.'}`,
        timestamp: new Date(),
        isStreaming: false,
        displayedContent: ''
      }
      messages.value.push(errorMessage)

      // Start streaming the error message
      await nextTick()
      streamAIMessage(errorMessageId, errorMessage.content)
    }
  } catch (error) {
    console.error('An error occurred while sending your message:', error)
    const errorMessageId = Date.now() + 1
    const errorMessage = {
      id: errorMessageId,
      type: 'ai',
      content: `Sorry, I encountered an error: ${error.message || 'Please try again.'}`,
      timestamp: new Date(),
      isStreaming: false,
      displayedContent: ''
    }
    messages.value.push(errorMessage)

    // Start streaming the error message
    await nextTick()
    streamAIMessage(errorMessageId, errorMessage.content)
  } finally {
    isLoading.value = false
    await nextTick()
    scrollToBottom()

    // Focus the input after sending message
    if (chatInputRef.value && chatInputRef.value.focus) {
      setTimeout(() => {
        chatInputRef.value.focus()
      }, 100)
    }
  }
}

const handleConcernClick = (concern) => {
  sendMessage(concern.text)
}

const handleKeyDown = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

const scrollToBottom = () => {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight
  }
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

const startNewChat = () => {
  messages.value = []
  conversationId.value = null
  showAppointmentSlots.value = false
  newMessage.value = ''

  // Focus the input
  if (chatInputRef.value && chatInputRef.value.focus) {
    setTimeout(() => {
      chatInputRef.value.focus()
    }, 100)
  }
}

const openReferralModal = () => {
  showReferralModal.value = true
}

const closeReferralModal = () => {
  showReferralModal.value = false
}





const filterSlots = () => {
  // Safety check: ensure allAvailableSlots.value is an array
  if (!Array.isArray(allAvailableSlots.value)) {
    console.warn('allAvailableSlots.value is not an array:', allAvailableSlots.value)
    allAvailableSlots.value = []
    appointmentSlots.value = []
    return
  }

  let filteredSlots = [...allAvailableSlots.value]

  // Filter by date if specific date is selected
  if (selectedDate.value && selectedDate.value !== '' && selectedDate.value !== 'all') {
    filteredSlots = filteredSlots.filter(slot => slot.full_date === selectedDate.value)
  }

  // Filter by provider if specific provider is selected
  if (selectedProvider.value && selectedProvider.value !== 'all') {
    filteredSlots = filteredSlots.filter(slot => slot.provider_id.toString() === selectedProvider.value)
  }

  // Show ALL filtered slots - no artificial limits
  appointmentSlots.value = filteredSlots
  showingAllSlots.value = true
}



// Load existing conversation from URL parameter
const loadExistingConversation = async (conversationIdParam) => {
  if (!conversationIdParam) return

  loadingConversation.value = true
  try {
    const response = await axios.get(`/web-api/chat/conversation/${conversationIdParam}`)
    const data = response.data

    // The API returns the conversation directly, not wrapped in a conversation property
    if (data && data.id) {
      conversationId.value = conversationIdParam

      // Load messages if they exist
      if (data.messages && Array.isArray(data.messages)) {
        messages.value = data.messages.map(msg => ({
          id: msg._id || msg.id || Date.now() + Math.random(),
          type: msg.role === 'user' ? 'user' : 'ai',
          content: msg.content || msg.message,
          timestamp: new Date(msg.timestamp || msg.created_at || Date.now()),
          formatted: msg.role !== 'user' ? formatAIMessage(msg.content || msg.message) : undefined
        }))

        // Scroll to bottom after loading messages
        await nextTick()
        scrollToBottom()
      }
    }
  } catch (error) {
    console.error('Error loading conversation:', error)
    // If conversation doesn't exist or error, start fresh
    conversationId.value = null
    messages.value = []
  } finally {
    loadingConversation.value = false
  }
}

// Transfer anonymous conversation if available
const transferAnonymousConversation = async () => {
  try {
    const storedConversation = localStorage.getItem('anonymous_conversation')
    if (!storedConversation) return null

    const conversationData = JSON.parse(storedConversation)
    if (!conversationData.conversation_id || !conversationData.anonymous_id) return null

    console.log('Transferring anonymous conversation:', conversationData.conversation_id)

    // Add a small delay to ensure authentication is established
    await new Promise(resolve => setTimeout(resolve, 500))

    const payload = {
      conversation_id: String(conversationData.conversation_id),
      anonymous_id: String(conversationData.anonymous_id)
    }

    console.log('Transfer payload:', payload)

    const response = await axios.post('/web-api/chat/transfer-anonymous', payload, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        'X-Requested-With': 'XMLHttpRequest',
      },
      withCredentials: true
    })

    if (response.data.success) {
      console.log('Anonymous conversation transferred successfully')

      // Clear the stored anonymous conversation
      localStorage.removeItem('anonymous_conversation')

      // Return the conversation ID to load it
      return conversationData.conversation_id
    }
  } catch (error) {
    console.error('Error transferring anonymous conversation:', error)

    // Log detailed error information
    if (error.response) {
      console.error('Response status:', error.response.status)
      console.error('Response data:', error.response.data)
    }

    // If it's a 401 error, the user might not be authenticated yet
    if (error.response?.status === 401) {
      console.log('Authentication required for transfer, will retry later')
      // Don't clear localStorage yet, we might retry
      return null
    }

    // If it's a 422 error, log validation errors
    if (error.response?.status === 422) {
      console.error('Validation errors:', error.response.data.errors)
      // Don't clear localStorage yet, might be a temporary issue
      return null
    }

    // For other errors, clear the invalid stored conversation
    localStorage.removeItem('anonymous_conversation')
  }

  return null
}

// Handle payment success and show confirmation message
const handlePaymentSuccess = async (appointmentId) => {
  try {
    // Get appointment details
    const response = await axios.get(`/api/appointments/${appointmentId}`)
    const appointment = response.data.appointment

    // Create intelligent confirmation message
    const formatDate = (dateStr) => {
      try {
        return new Date(dateStr).toLocaleDateString('en-US', { 
          weekday: 'long', 
          month: 'long', 
          day: 'numeric', 
          year: 'numeric' 
        })
      } catch (e) {
        return dateStr || 'Date not available'
      }
    }

    const getProviderName = (provider) => {
      if (provider?.user?.name) return provider.user.name
      if (provider?.name) return `Dr. ${provider.name}`
      return 'Your Doctor'
    }

    const confirmationMessageContent = `🎉 **Payment Successful - Appointment Confirmed!**

Excellent! Your payment has been processed successfully and your appointment is now confirmed.

**📅 Appointment Details:**
**Provider:** ${getProviderName(appointment.provider)}
**Date:** ${formatDate(appointment.date)}
**Time:** ${appointment.time_slot?.start_time || 'Time TBD'} - ${appointment.time_slot?.end_time || 'Time TBD'}
**Service:** ${appointment.service?.name || 'Consultation'}
**Amount Paid:** $${appointment.amount || '0.00'}
**Appointment ID:** #${appointment.id || 'N/A'}

**📧 What's Next:**
• You'll receive a confirmation email shortly
• A calendar invite will be sent to your email
• You can view and manage this appointment in your appointments section

Is there anything else I can help you with regarding your upcoming appointment or any other health concerns?`

    const confirmationMessage = {
      id: Date.now(),
      type: 'ai',
      content: confirmationMessageContent,
      timestamp: new Date(),
      formatted: formatAIMessage(confirmationMessageContent)
    }

    // Add the confirmation message to chat
    messages.value.push(confirmationMessage)

    // Save confirmation message to conversation history
    if (conversationId.value) {
      try {
        await axios.post('/web-api/chat/message', {
          conversation_id: conversationId.value,
          message: confirmationMessage.content,
          role: 'assistant'
        })
        console.log('Payment confirmation message saved to chat history')
      } catch (error) {
        console.error('Error saving confirmation message to chat history:', error)
      }
    }

    // Scroll to show the confirmation message
    await nextTick()
    scrollToBottom()

  } catch (error) {
    console.error('Error handling payment success:', error)

    // Fallback confirmation message if appointment details can't be fetched
    const fallbackMessageContent = `🎉 **Payment Successful!**

Great news! Your payment has been processed successfully and your appointment is confirmed.

**Appointment ID:** #${appointmentId}

You'll receive a confirmation email shortly with all the details. You can also view your appointment in the appointments section.

Is there anything else I can help you with today?`

    const fallbackMessage = {
      id: Date.now(),
      type: 'ai',
      content: fallbackMessageContent,
      timestamp: new Date(),
      formatted: formatAIMessage(fallbackMessageContent)
    }

    messages.value.push(fallbackMessage)
    await nextTick()
    scrollToBottom()
  }
}

// Check URL parameters and anonymous conversation on mount
onMounted(async () => {
  const urlParams = new URLSearchParams(window.location.search)
  const conversationParam = urlParams.get('conversation')
  const paymentSuccess = urlParams.get('payment_success')
  const appointmentId = urlParams.get('appointment_id')

  // First, try to transfer any anonymous conversation
  let transferredConversationId = await transferAnonymousConversation()

  // If transfer failed due to authentication, retry after a longer delay
  if (!transferredConversationId && localStorage.getItem('anonymous_conversation')) {
    console.log('Retrying anonymous conversation transfer after authentication delay...')
    await new Promise(resolve => setTimeout(resolve, 1500))
    transferredConversationId = await transferAnonymousConversation()
  }

  // Load conversation (either from URL param or transferred anonymous conversation)
  const conversationToLoad = conversationParam || transferredConversationId

  if (conversationToLoad) {
    loadExistingConversation(conversationToLoad)
  }

  // Handle payment success redirect
  if (paymentSuccess === 'true' && appointmentId) {
    await handlePaymentSuccess(appointmentId)
    // Clean up URL parameters
    window.history.replaceState({}, document.title, window.location.pathname)
  }
})

// Cleanup on unmount
onUnmounted(() => {
  if (streamingTimer.value) {
    clearTimeout(streamingTimer.value)
  }
})
</script>

<template>
    <Head title="Chat - Medroid" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="h-full flex flex-col bg-gray-50 relative overflow-hidden">
            <!-- Floating Logo and Controls -->
            <div class="absolute top-4 left-4 z-20">
                <div class="flex items-center space-x-3">
                    <MedroidLogo :size="32" />
                    <div>
                        <h1 class="text-sm font-semibold text-gray-900">Medroid AI</h1>
                        <p class="text-xs text-gray-500">Your AI Doctor</p>
                    </div>
                </div>
            </div>



            <div class="absolute top-4 right-4 z-20">
                <div class="flex items-center space-x-3">
                    <!-- New Chat Button -->
                    <button
                        @click="startNewChat"
                        class="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white/80 backdrop-blur-sm border border-white/50 rounded-full hover:bg-white/90 transition-all duration-200 shadow-lg"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        <span>New Chat</span>
                    </button>



                    <!-- Refer & Earn Button -->
                    <button
                        @click="openReferralModal"
                        class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-green-600 bg-green-50/80 backdrop-blur-sm border border-green-200/50 rounded-full hover:bg-green-100/80 transition-all duration-200 shadow-lg"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <span>Refer & Earn</span>
                    </button>
                </div>
            </div>

            <!-- Loading Conversation -->
            <div v-if="loadingConversation" class="flex-1 flex flex-col items-center justify-center px-6 py-8">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"></div>
                    <p class="text-gray-600">Loading conversation...</p>
                </div>
            </div>

            <!-- Main Content Area -->
            <div v-else-if="messages.length === 0" class="flex-1 flex flex-col items-center justify-center px-6 py-8 pt-24">
                <!-- Welcome Section -->
                <div class="text-center mb-12">
                    <!-- Medroid Logo -->
                    <div class="mx-auto mb-6">
                        <MedroidLogo :size="80" :show-shadow="true" />
                    </div>

                    <h1 class="text-4xl font-bold text-gray-900 mb-4">Welcome to Medroid</h1>
                    <p class="text-lg text-gray-600 max-w-md mx-auto">
                        Your personal AI Dr. Ask me anything about your symptoms.
                    </p>
                </div>

                <!-- Health Concern Buttons -->
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-12 max-w-2xl">
                    <button
                        v-for="concern in healthConcerns"
                        :key="concern.text"
                        @click="handleConcernClick(concern)"
                        :class="concern.color"
                        class="flex flex-col items-center p-4 rounded-xl border-2 hover:shadow-md transition-all duration-200 hover:scale-105"
                    >
                        <div class="mb-2" v-html="concern.icon"></div>
                        <span class="text-sm font-medium text-center">{{ concern.text }}</span>
                    </button>
                </div>
            </div>

            <!-- Chat Messages Area -->
            <div v-else class="flex-1 overflow-hidden pt-24">
                <div class="h-full flex flex-col">
                    <!-- Messages Container -->
                    <div ref="chatContainer" class="flex-1 overflow-y-auto pt-4 pb-2 space-y-6">
                        <!-- Constrain messages to match input width -->
                        <div class="max-w-4xl mx-auto px-4">
                            <div v-for="message in messages" :key="message.id" class="flex mb-4" :class="message.type === 'user' ? 'justify-end' : 'justify-start'">
                                <!-- AI Message -->
                                <div v-if="message.type === 'ai'" class="flex items-start space-x-3 max-w-[85%] mb-2">
                                    <div class="flex-shrink-0 mt-1">
                                        <MedroidLogo :size="24" />
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <!-- Streaming line-by-line display -->
                                        <div v-if="message.isStreaming && message.streamingLines" class="text-gray-900 leading-snug prose prose-sm max-w-none text-sm break-words">
                                            <div
                                                v-for="line in message.streamingLines"
                                                :key="line.id"
                                                class="animate-fade-in-line"
                                                v-html="formatAIMessage(line.content)"
                                            ></div>
                                        </div>
                                        <!-- Regular display for completed messages -->
                                        <div
                                            v-else
                                            class="text-gray-900 leading-snug prose prose-sm max-w-none text-sm break-words"
                                            v-html="message.formatted || message.content"
                                        ></div>
                                        <!-- Modern futuristic typing cursor -->
                                        <span v-if="message.isStreaming" class="inline-flex items-center ml-1">
                                            <span class="w-0.5 h-4 bg-gradient-to-t from-teal-500 to-teal-300 animate-futuristic-pulse rounded-full shadow-sm"></span>
                                            <span class="ml-1 flex space-x-0.5">
                                                <span class="w-1 h-1 bg-teal-500 rounded-full animate-modern-bounce shadow-sm" style="animation-delay: 0ms"></span>
                                                <span class="w-1 h-1 bg-teal-500 rounded-full animate-modern-bounce shadow-sm" style="animation-delay: 150ms"></span>
                                                <span class="w-1 h-1 bg-teal-500 rounded-full animate-modern-bounce shadow-sm" style="animation-delay: 300ms"></span>
                                            </span>
                                        </span>

                                        <!-- Payment Button for appointment booking -->
                                        <div v-if="message.showPaymentButton" class="mt-3 pt-3 border-t border-gray-200">
                                            <div class="flex items-center justify-between">
                                                <div class="text-xs text-gray-500">
                                                    Appointment #{{ message.appointmentId }} • ${{ message.paymentAmount }}
                                                </div>
                                                <a
                                                    :href="message.paymentUrl"
                                                    class="inline-flex items-center px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                                                >
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                                    </svg>
                                                    Pay Now
                                                </a>
                                            </div>
                                        </div>

                                        <div class="text-xs text-gray-400 mt-0.5">{{ formatTime(message.timestamp) }}</div>
                                    </div>
                                </div>

                                <!-- User Message -->
                                <div v-else class="flex justify-end max-w-[85%] ml-auto mb-2">
                                    <div class="bg-teal-500 text-white px-3 py-2 rounded-2xl rounded-br-md shadow-sm max-w-full">
                                        <div class="text-sm break-words whitespace-pre-wrap">{{ message.content }}</div>
                                        <div class="text-xs text-teal-100 mt-0.5 text-right">{{ formatTime(message.timestamp) }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Modern loading indicator -->
                        <div v-if="isLoading" class="max-w-4xl mx-auto px-4 mb-4">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0 mt-1">
                                    <MedroidLogo :size="24" />
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-1">
                                        <span class="text-sm text-gray-600">AI is thinking</span>
                                        <div class="flex space-x-1 ml-2">
                                            <div class="w-1.5 h-1.5 bg-teal-500 rounded-full animate-modern-bounce"></div>
                                            <div class="w-1.5 h-1.5 bg-teal-500 rounded-full animate-modern-bounce" style="animation-delay: 0.15s"></div>
                                            <div class="w-1.5 h-1.5 bg-teal-500 rounded-full animate-modern-bounce" style="animation-delay: 0.3s"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Appointment Slots -->
                        <div v-if="showAppointmentSlots" class="max-w-4xl mx-auto px-4">
                            <div class="flex items-start space-x-2">
                                <div class="w-6 h-6 bg-teal-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="text-gray-800 leading-snug prose prose-sm max-w-none text-sm break-words">
                                        <div class="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
                                            <div class="mb-4">
                                                <h3 class="text-sm font-medium text-gray-900 mb-2">Available Appointments</h3>
                                                <p class="text-xs text-gray-500 mb-3">
                                                    {{ appointmentSlots.length }} slots available • Click any slot to book
                                                </p>

                                                <!-- Filters -->
                                                <div class="flex flex-wrap gap-2 mb-3">
                                                    <!-- Date Filter -->
                                                    <div class="flex-1 min-w-0">
                                                        <select
                                                            v-model="selectedDate"
                                                            @change="filterSlots"
                                                            class="w-full text-xs border border-gray-300 rounded-md px-2 py-1 focus:ring-1 focus:ring-teal-500 focus:border-teal-500"
                                                        >
                                                            <option value="">All Days</option>
                                                            <option v-for="date in availableDates" :key="date.value" :value="date.value">
                                                                {{ date.label }}
                                                            </option>
                                                        </select>
                                                    </div>

                                                    <!-- Provider Filter -->
                                                    <div class="flex-1 min-w-0">
                                                        <select
                                                            v-model="selectedProvider"
                                                            @change="filterSlots"
                                                            class="w-full text-xs border border-gray-300 rounded-md px-2 py-1 focus:ring-1 focus:ring-teal-500 focus:border-teal-500"
                                                        >
                                                            <option v-for="provider in availableProviders" :key="provider.value" :value="provider.value">
                                                                {{ provider.label }}
                                                            </option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                            <div v-if="loadingSlots" class="flex items-center justify-center py-6">
                                                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-teal-500"></div>
                                                <span class="ml-2 text-xs text-gray-600">Loading slots...</span>
                                            </div>

                                            <div v-else-if="appointmentSlots.length === 0" class="text-center py-6">
                                                <p class="text-xs text-gray-500">No appointments available for selected filters</p>
                                            </div>

                                            <div v-else>
                                                <!-- Grid Layout for Appointment Slots -->
                                                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 mb-3">
                                                    <button
                                                        v-for="slot in (showingAllSlots ? appointmentSlots : appointmentSlots.slice(0, 9))"
                                                        :key="slot.id"
                                                        @click="bookAppointment(slot)"
                                                        class="flex flex-col p-3 bg-gray-50 hover:bg-teal-50 border border-gray-200 hover:border-teal-300 rounded-lg transition-all duration-200 text-left"
                                                    >
                                                        <div class="flex items-center justify-between mb-1">
                                                            <div class="text-sm font-medium text-gray-900">
                                                                {{ slot.time }}
                                                            </div>
                                                            <div class="text-sm font-semibold text-teal-600">
                                                                ${{ slot.price }}
                                                            </div>
                                                        </div>
                                                        <div class="text-xs text-gray-500 mb-1">
                                                            {{ slot.provider.replace('Dr. ', '') }}
                                                        </div>
                                                        <div v-if="selectedDate === ''" class="text-xs text-gray-400">
                                                            {{ slot.date.split(',')[0] }}
                                                        </div>
                                                    </button>
                                                </div>

                                                <div v-if="appointmentSlots.length > 9" class="text-center pt-2">
                                                    <button
                                                        @click="showingAllSlots = !showingAllSlots"
                                                        class="text-xs text-teal-600 hover:text-teal-700 font-medium px-3 py-1 rounded-md hover:bg-teal-50 transition-colors"
                                                    >
                                                        {{ showingAllSlots ? 'Show less' : `Show ${appointmentSlots.length - 9} more slots` }}
                                                    </button>
                                                </div>
                                            </div>

                                            <div class="mt-3 pt-3 border-t border-gray-200 flex items-center justify-between">
                                                <button
                                                    @click="showAppointmentSlots = false"
                                                    class="text-xs text-gray-500 hover:text-gray-700 px-3 py-1 rounded-md hover:bg-gray-100 transition-colors duration-200"
                                                >
                                                    Close
                                                </button>
                                                <div class="text-xs text-gray-400">
                                                    {{ selectedDate ? 'Filtered by date' : 'All available days' }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-xs text-gray-400 mt-1">{{ formatTime(new Date()) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Input (Fixed at bottom with seamless blending) -->
            <div class="relative">
                <!-- Seamless background extension -->
                <div class="absolute inset-x-0 bottom-0 h-24 bg-gray-50 -z-10"></div>

                <div class="bg-gray-50 px-6 py-4 relative">
                    <div class="max-w-4xl mx-auto">


                        <ChatInput
                            ref="chatInputRef"
                            v-model="newMessage"
                            placeholder="Type your health question..."
                            :is-loading="isLoading"
                            :show-tools="false"
                            :show-version="false"
                            @send="sendMessage"
                            @keydown="handleKeyDown"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Referral Modal -->
        <ReferralModal
            :is-open="showReferralModal"
            @close="closeReferralModal"
        />
    </AppLayout>
</template>

<style scoped>
/* Hide scrollbar but keep functionality */
.overflow-y-auto {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.overflow-y-auto::-webkit-scrollbar {
    display: none; /* WebKit */
}

/* Smooth animations */
.transition-all {
    transition: all 0.2s ease-in-out;
}

/* Loading animation for dots */
@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

.animate-bounce {
    animation: bounce 1.4s infinite ease-in-out both;
}

/* Prose styling for AI messages */
.prose {
    color: inherit;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

.prose strong {
    font-weight: 600;
    color: #1f2937;
}

.prose ul {
    margin: 0.25rem 0;
}

.prose ol {
    margin: 0.25rem 0;
}

.prose li {
    margin: 0.125rem 0;
}

.prose p {
    margin: 0.25rem 0;
}

.prose h3 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0.5rem 0 0.25rem 0;
    color: #1f2937;
}

.prose h4 {
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0.375rem 0 0.125rem 0;
    color: #374151;
}

/* Ensure proper text wrapping for long messages */
.break-words {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
}

/* Preserve whitespace and line breaks */
.whitespace-pre-wrap {
    white-space: pre-wrap;
}

/* Modern futuristic typing animations */
@keyframes futuristicPulse {
    0%, 100% {
        opacity: 1;
        transform: scaleY(1);
    }
    50% {
        opacity: 0.6;
        transform: scaleY(1.2);
    }
}

@keyframes modernBounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

.animate-futuristic-pulse {
    animation: futuristicPulse 1.5s ease-in-out infinite;
}

.animate-modern-bounce {
    animation: modernBounce 1.4s ease-in-out infinite;
}

/* Google Gemini-style line fade-in animation */
@keyframes fadeInLine {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-line {
    animation: fadeInLine 0.6s ease-out forwards;
}

/* Gradient text effect for streaming */
.streaming-text {
    background: linear-gradient(90deg, #374151, #6b7280, #374151);
    background-size: 200% 100%;
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}


</style>