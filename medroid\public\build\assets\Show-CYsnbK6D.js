import{_ as C}from"./AppLayout.vue_vue_type_script_setup_true_lang-C2T_8RUB.js";import{n as D,c as y,d as g,e as l,f as c,u,m as P,g as x,i as t,t as s,s as v,F as f,q as _,y as q,j as z,P as w,x as S}from"./vendor-DkZiYBIF.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-fZxfkI-v.js";import"./createLucideIcon-CSZE2I1R.js";const I={class:"flex items-center justify-between"},T={class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"},U={class:"flex mt-2","aria-label":"Breadcrumb"},V={class:"inline-flex items-center space-x-1 md:space-x-3"},$={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},A={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},F={class:"py-12"},L={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},E={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},K={class:"lg:col-span-2"},M={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6"},Q={class:"p-6"},G={class:"grid grid-cols-2 gap-4"},H={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},J={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},R={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},W={class:"p-6"},X={class:"overflow-x-auto"},Y={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Z={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},tt={class:"px-6 py-4 whitespace-nowrap"},et={class:"flex items-center"},st={class:"ml-4"},rt={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},at={class:"text-sm text-gray-500 dark:text-gray-400"},dt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100"},ot={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100"},lt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100"},nt={class:"lg:col-span-1"},it={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6"},gt={class:"p-6"},xt={class:"space-y-3"},mt={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},yt={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},ct={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},ut={class:"p-6"},ft={class:"space-y-3"},pt={class:"flex justify-between"},bt={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},ht={class:"flex justify-between"},kt={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},vt={class:"flex justify-between"},_t={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},wt={class:"border-t border-gray-200 dark:border-gray-600 pt-3"},St={class:"flex justify-between"},Ot={class:"text-base font-bold text-gray-900 dark:text-gray-100"},Pt={__name:"Show",props:{order:{type:Object,required:!0}},setup(a){const p=a,O=D(),b=y(()=>{var r;return(r=O.props.auth)==null?void 0:r.user}),m=[{title:"Dashboard",href:"/dashboard"},{title:"Orders",href:"/admin/orders"},{title:`Order #${p.order.order_number}`,href:`/admin/orders/${p.order.id}`}];y(()=>{var r,e;return((e=(r=b.value)==null?void 0:r.roles)==null?void 0:e.some(d=>d.name==="admin"))||!1}),y(()=>{var r,e;return((e=(r=b.value)==null?void 0:r.user_permissions)==null?void 0:e.includes("manage orders"))||!1});const N=r=>({pending:"bg-yellow-100 text-yellow-800",processing:"bg-blue-100 text-blue-800",shipped:"bg-purple-100 text-purple-800",delivered:"bg-green-100 text-green-800",cancelled:"bg-red-100 text-red-800",refunded:"bg-gray-100 text-gray-800"})[r]||"bg-gray-100 text-gray-800",B=r=>({pending:"bg-yellow-100 text-yellow-800",paid:"bg-green-100 text-green-800",failed:"bg-red-100 text-red-800",refunded:"bg-gray-100 text-gray-800"})[r]||"bg-gray-100 text-gray-800",n=r=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(r),j=r=>new Date(r).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return(r,e)=>(l(),g(f,null,[c(u(P),{title:`Order #${a.order.order_number}`},null,8,["title"]),c(C,null,{header:x(()=>[t("div",I,[t("div",null,[t("h2",T," Order #"+s(a.order.order_number),1),t("nav",U,[t("ol",V,[(l(),g(f,null,_(m,(d,i)=>t("li",{key:i,class:"inline-flex items-center"},[i<m.length-1?(l(),q(u(w),{key:0,href:d.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:x(()=>[S(s(d.title),1)]),_:2},1032,["href"])):(l(),g("span",$,s(d.title),1)),i<m.length-1?(l(),g("svg",A,e[0]||(e[0]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):z("",!0)])),64))])])]),c(u(w),{href:"/admin/orders",class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:x(()=>e[1]||(e[1]=[S(" Back to Orders ")])),_:1})])]),default:x(()=>{var d,i;return[t("div",F,[t("div",L,[t("div",E,[t("div",K,[t("div",M,[t("div",Q,[e[6]||(e[6]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"},"Order Information",-1)),t("div",G,[t("div",null,[e[2]||(e[2]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Order Number",-1)),t("p",H,s(a.order.order_number),1)]),t("div",null,[e[3]||(e[3]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Order Date",-1)),t("p",J,s(j(a.order.created_at)),1)]),t("div",null,[e[4]||(e[4]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Status",-1)),t("span",{class:v([N(a.order.status),"mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full capitalize"])},s(a.order.status),3)]),t("div",null,[e[5]||(e[5]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Payment Status",-1)),t("span",{class:v([B(a.order.payment_status),"mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full capitalize"])},s(a.order.payment_status),3)])])])]),t("div",R,[t("div",W,[e[9]||(e[9]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"},"Order Items",-1)),t("div",X,[t("table",Y,[e[8]||(e[8]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Product "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Quantity "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Price "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Total ")])],-1)),t("tbody",Z,[(l(!0),g(f,null,_(a.order.items,o=>{var h,k;return l(),g("tr",{key:o.id},[t("td",tt,[t("div",et,[e[7]||(e[7]=t("div",{class:"flex-shrink-0 h-10 w-10"},[t("div",{class:"h-10 w-10 rounded bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},[t("i",{class:"fas fa-box text-gray-500 dark:text-gray-400"})])],-1)),t("div",st,[t("div",rt,s(((h=o.product)==null?void 0:h.name)||"Product not found"),1),t("div",at," SKU: "+s(((k=o.product)==null?void 0:k.sku)||"N/A"),1)])])]),t("td",dt,s(o.quantity),1),t("td",ot,s(n(o.price)),1),t("td",lt,s(n(o.quantity*o.price)),1)])}),128))])])])])])]),t("div",nt,[t("div",it,[t("div",gt,[e[12]||(e[12]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"},"Customer Information",-1)),t("div",xt,[t("div",null,[e[10]||(e[10]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Name",-1)),t("p",mt,s(((d=a.order.user)==null?void 0:d.name)||"N/A"),1)]),t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Email",-1)),t("p",yt,s(((i=a.order.user)==null?void 0:i.email)||"N/A"),1)])])])]),t("div",ct,[t("div",ut,[e[17]||(e[17]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"},"Order Summary",-1)),t("div",ft,[t("div",pt,[e[13]||(e[13]=t("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Subtotal",-1)),t("span",bt,s(n(a.order.subtotal_amount||0)),1)]),t("div",ht,[e[14]||(e[14]=t("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Tax",-1)),t("span",kt,s(n(a.order.tax_amount||0)),1)]),t("div",vt,[e[15]||(e[15]=t("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Shipping",-1)),t("span",_t,s(n(a.order.shipping_amount||0)),1)]),t("div",wt,[t("div",St,[e[16]||(e[16]=t("span",{class:"text-base font-medium text-gray-900 dark:text-gray-100"},"Total",-1)),t("span",Ot,s(n(a.order.total_amount)),1)])])])])])])])])])]}),_:1})],64))}};export{Pt as default};
