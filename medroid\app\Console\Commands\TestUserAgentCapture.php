<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\UserAgentService;
use App\Services\NotificationService;
use Illuminate\Console\Command;
use Illuminate\Http\Request;

class TestUserAgentCapture extends Command
{
    protected $signature = 'test:user-agent';
    protected $description = 'Test user agent capture functionality';

    public function handle()
    {
        $this->info('Testing User Agent Capture Implementation...');

        // Test UserAgentService
        $userAgentService = app(UserAgentService::class);
        
        // Create a mock request with user agent
        $request = Request::create('/', 'GET', [], [], [], [
            'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'REMOTE_ADDR' => '*************'
        ]);

        $deviceInfo = $userAgentService->parseUserAgent($request);
        
        $this->info('Parsed Device Info:');
        $this->line('User Agent: ' . $deviceInfo['user_agent']);
        $this->line('Browser: ' . $deviceInfo['browser']);
        $this->line('Platform: ' . $deviceInfo['platform']);
        $this->line('Device Type: ' . $deviceInfo['device_type']);
        $this->line('IP Address: ' . $deviceInfo['ip_address']);

        // Test device token storage with user agent
        $user = User::first();
        if ($user) {
            $notificationService = app(NotificationService::class);
            
            $deviceToken = $notificationService->storeDeviceToken(
                $user,
                'test_token_' . time(),
                'web',
                $deviceInfo
            );

            $this->info('Device token stored with user agent info:');
            $this->line('Token ID: ' . $deviceToken->id);
            $this->line('Browser: ' . $deviceToken->browser);
            $this->line('Platform: ' . $deviceToken->platform);
        }

        $this->info('User Agent Capture test completed successfully!');
    }
}
