<?php

use App\Http\Controllers\Api\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SocialFeedController;
use App\Http\Controllers\AIChatController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\ProviderController;
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\ProviderAvailabilityController;
use App\Http\Controllers\PatientPreferencesController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\VideoConsultationController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\WebhookController;
use App\Http\Controllers\AnonymousChatController;
use App\Http\Controllers\PasswordResetController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\EmailTemplateController;
use App\Http\Controllers\AnalyticsController;
use App\Http\Controllers\DiagnosticFeedbackController;
use App\Http\Controllers\NotificationManagementController;
use App\Http\Controllers\NotificationTemplateController;
use App\Http\Controllers\NotificationAnalyticsController;
use App\Http\Controllers\NotificationSettingsController;
use App\Http\Controllers\ReferralController;
use App\Http\Controllers\UserCreditController;
use App\Http\Controllers\ImageProxyController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ShoppingCartController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\DigitalDownloadController;

// Public routes
Route::post('/login', [AuthController::class, 'login']);
Route::post('/register', [AuthController::class, 'register']);

// Waitlist routes (public)
Route::get('waitlist/status', [\App\Http\Controllers\WaitlistController::class, 'getWaitlistStatus']);
Route::post('waitlist/join', [\App\Http\Controllers\WaitlistController::class, 'joinWaitlist']);

// Public Provider Routes - No authentication required
Route::get('public/providers', [ProviderController::class, 'publicIndex']);
Route::get('providers/public', [ProviderController::class, 'publicIndex']);
Route::get('providers/{id}/services', [ServiceController::class, 'index']);
Route::get('providers/{providerId}/available-slots', [ProviderAvailabilityController::class, 'getAvailableTimeSlots']);
Route::get('providers/specializations', [ProviderController::class, 'getSpecializations']);
Route::get('providers', [ProviderController::class, 'index']);

// Public Ecommerce Routes - No authentication required
Route::prefix('shop')->group(function () {
    Route::get('products', [ProductController::class, 'index']);
    Route::get('products/{slug}', [ProductController::class, 'show']);
    Route::get('categories', [ProductController::class, 'categories']);
    Route::get('featured-products', [ProductController::class, 'featured']);
    Route::get('search-products', [ProductController::class, 'search']);
});

// Stripe Webhook Route - No authentication required
Route::post('stripe/webhook', [WebhookController::class, 'handleWebhook']);

// Anonymous Chat Routes - No authentication required
Route::prefix('anonymous')->group(function () {
    Route::post('chat/start', [AnonymousChatController::class, 'startConversation']);
    Route::post('chat/message', [AnonymousChatController::class, 'sendMessage']);
    Route::get('chat/conversation/{id}', [AnonymousChatController::class, 'getConversation']);
    Route::post('chat/appointment-slots', [AnonymousChatController::class, 'requestAppointmentSlots']);
});

// Provider Registration Routes - No authentication required
Route::prefix('provider')->group(function () {
    Route::get('register', [\App\Http\Controllers\ProviderRegistrationController::class, 'showRegistrationForm']);
    Route::post('register', [\App\Http\Controllers\ProviderRegistrationController::class, 'register']);
    Route::get('register/success', [\App\Http\Controllers\ProviderRegistrationController::class, 'showSuccessPage']);
});

// Stripe Configuration Route - No authentication required
Route::get('stripe/config', function () {
    return response()->json([
        'publishable_key' => config('services.stripe.key'),
    ]);
});

// Password Reset Routes - No authentication required
Route::prefix('password')->group(function () {
    Route::post('email', [PasswordResetController::class, 'sendResetLinkEmail']);
    Route::post('reset', [PasswordResetController::class, 'reset']);
});

// Protected routes
Route::middleware(['auth:sanctum'])->group(function () {
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);

    // User profile image upload route
    Route::post('user/profile-image', [UserController::class, 'uploadProfileImage']);

    // Password change route (authenticated)
    Route::post('password/change', [PasswordResetController::class, 'changePassword']);

    // Dashboard API routes (using services)
    Route::get('dashboard/data', [DashboardController::class, 'getDashboardData']);

    // Permissions route
    Route::get('permissions', [\App\Http\Controllers\Auth\PermissionController::class, 'getUserPermissions']);

    // Provider API routes (using services)
    Route::get('providers/api', [ProviderController::class, 'getProvidersApi']);
    Route::get('providers/specializations/api', [ProviderController::class, 'getSpecializationsApi']);

    // Appointment API routes (using services)
    Route::get('appointments/api', [AppointmentController::class, 'getUserAppointmentsApi']);
    Route::post('appointments/api', [AppointmentController::class, 'createAppointmentApi']);
    Route::delete('appointments/{appointmentId}/api', [AppointmentController::class, 'cancelAppointmentApi']);

    // Provider routes
    Route::middleware('role:provider')->prefix('provider')->group(function () {
        // Provider profile management
        Route::post('profile', [ProviderController::class, 'createOrUpdateProfile']);
        Route::get('profile', [ProviderController::class, 'getProfile']);

        // Provider dashboard and appointments
        Route::get('dashboard-data', [ProviderController::class, 'getDashboardData']);
        Route::get('appointments', [ProviderController::class, 'getAppointments']);

        // Availability routes
        Route::get('get-availability', [ProviderAvailabilityController::class, 'getWeeklyAvailability']);
        Route::get('availability-data', [ProviderAvailabilityController::class, 'availability']);
        Route::put('availability', [ProviderAvailabilityController::class, 'updateWeeklyAvailability']);
        Route::post('availability', [ProviderAvailabilityController::class, 'postWeeklyAvailability']);

        // Absences routes
        Route::get('get-absences', [ProviderAvailabilityController::class, 'getAbsences']);
        Route::get('absences-data', [ProviderAvailabilityController::class, 'absences']);
        Route::put('absences', [ProviderAvailabilityController::class, 'updateAbsences']);
        Route::post('absences', [ProviderAvailabilityController::class, 'postAbsences']);

        // Availability slots
        Route::get('available-slots', [ProviderAvailabilityController::class, 'getAvailableTimeSlots']);
        Route::get('next-available', [ProviderAvailabilityController::class, 'getNextAvailableSlots']);
    });

    // Patient routes
    Route::middleware('ability:patient:access,*')->prefix('patient')->group(function () {
        // Patient Preferences Routes
        Route::get('appointment-preferences', [PatientPreferencesController::class, 'getAppointmentPreferences']);
        Route::put('appointment-preferences', [PatientPreferencesController::class, 'updateAppointmentPreferences']);

        // Patient appointments
        Route::get('appointments', [AppointmentController::class, 'userAppointments']);
    });

    // Admin routes
    Route::middleware('ability:*')->prefix('admin')->group(function () {
        // User Management Routes
        Route::get('users', [\App\Http\Controllers\UserController::class, 'index']);
        Route::post('users', [\App\Http\Controllers\UserController::class, 'store']);
        Route::get('users/{id}', [\App\Http\Controllers\UserController::class, 'show']);
        Route::put('users/{id}', [\App\Http\Controllers\UserController::class, 'update']);
        Route::delete('users/{id}', [\App\Http\Controllers\UserController::class, 'destroy']);

        // Provider Management Routes
        Route::get('providers', [\App\Http\Controllers\ProviderController::class, 'index']);
        Route::post('providers', [\App\Http\Controllers\ProviderController::class, 'store']);
        Route::get('providers/{id}', [\App\Http\Controllers\ProviderController::class, 'show']);
        Route::put('providers/{id}', [\App\Http\Controllers\ProviderController::class, 'update']);
        Route::put('providers/{id}/verification', [\App\Http\Controllers\ProviderController::class, 'updateVerificationStatus']);

        // Patient Management Routes
        Route::get('patients', [\App\Http\Controllers\PatientController::class, 'index']);
        Route::get('patients/{id}', [\App\Http\Controllers\PatientController::class, 'show']);
        Route::put('patients/{id}', [\App\Http\Controllers\PatientController::class, 'update']);
        Route::delete('patients/{id}', [\App\Http\Controllers\PatientController::class, 'destroy']);
        Route::get('patients/{id}/health-records', [\App\Http\Controllers\PatientController::class, 'getHealthRecords']);
        Route::get('patients/{id}/appointments', [\App\Http\Controllers\PatientController::class, 'getAppointmentHistory']);

        // Payment Management Routes
        Route::get('payments', [\App\Http\Controllers\PaymentController::class, 'index']);
        Route::get('payments/stats', [\App\Http\Controllers\PaymentController::class, 'getStatistics']);
        Route::get('payments/{id}', [\App\Http\Controllers\PaymentController::class, 'show']);
        Route::post('payments/{id}/refund', [\App\Http\Controllers\PaymentController::class, 'processRefund']);

        // Chat Management Routes
        Route::get('chats', [\App\Http\Controllers\ChatController::class, 'managementIndex']);
        Route::get('chats/stats', [\App\Http\Controllers\ChatController::class, 'getStats']);
        Route::get('chats/{id}', [\App\Http\Controllers\ChatController::class, 'managementShow']);
        Route::post('chats/{id}/messages/{messageId}/moderate', [\App\Http\Controllers\ChatController::class, 'moderateMessage']);
        Route::put('chats/{id}/archive', [\App\Http\Controllers\ChatController::class, 'archiveChat']);
        Route::put('chats/{id}/flag', [\App\Http\Controllers\ChatController::class, 'flagChat']);
        Route::put('chats/{id}/unflag', [\App\Http\Controllers\ChatController::class, 'unflagChat']);
        Route::get('chats/{id}/messages', [\App\Http\Controllers\ChatController::class, 'getMessages']);
        Route::post('chats/{id}/messages', [\App\Http\Controllers\ChatController::class, 'addManagementMessage']);

        // Clinic Management Routes
        Route::get('clinics', [\App\Http\Controllers\ClinicController::class, 'index']);
        Route::post('clinics', [\App\Http\Controllers\ClinicController::class, 'store']);
        Route::get('clinics/{id}', [\App\Http\Controllers\ClinicController::class, 'show']);
        Route::put('clinics/{id}', [\App\Http\Controllers\ClinicController::class, 'update']);
        Route::delete('clinics/{id}', [\App\Http\Controllers\ClinicController::class, 'destroy']);
        Route::get('clinics/{id}/stats', [\App\Http\Controllers\ClinicController::class, 'getStats']);
        Route::get('clinics/{id}/users', [\App\Http\Controllers\ClinicController::class, 'getUsers']);

        // Enhanced Notification Management Routes
        Route::prefix('notifications')->group(function () {
            Route::post('send-by-device-type', [NotificationManagementController::class, 'sendByDeviceType']);
            Route::post('send-by-browser', [NotificationManagementController::class, 'sendByBrowser']);
            Route::post('send-by-platform', [NotificationManagementController::class, 'sendByPlatform']);
        });

        // Device Analytics Routes
        Route::prefix('analytics')->group(function () {
            Route::get('devices', [\App\Http\Controllers\DeviceAnalyticsController::class, 'getDeviceAnalytics']);
            Route::get('sessions', [\App\Http\Controllers\DeviceAnalyticsController::class, 'getRecentSessions']);
            Route::get('device-tokens', [\App\Http\Controllers\DeviceAnalyticsController::class, 'getDeviceTokens']);
        });
    });

    // Management Dashboard Routes (for admin analytics)
    Route::middleware('ability:*')->prefix('management')->group(function () {
        Route::prefix('dashboard')->group(function () {
            Route::get('kpi', [AnalyticsController::class, 'getKpiMetrics']);
        });
    });

    // Common authenticated routes (available to all authenticated users)

    // Notification Routes
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::get('/unread-count', [NotificationController::class, 'unreadCount']);
        Route::post('/{id}/read', [NotificationController::class, 'markAsRead']);
        Route::post('/read-all', [NotificationController::class, 'markAllAsRead']);
        Route::post('/device-token', [NotificationController::class, 'storeDeviceToken']);
        Route::put('/device-token', [NotificationController::class, 'updateDeviceToken']);
    });

    // Social Feed Routes
    Route::get('feed', [SocialFeedController::class, 'index']);
    Route::get('feed/topics', [SocialFeedController::class, 'topics']);
    Route::post('feed/create', [SocialFeedController::class, 'create']);
    Route::post('feed/save/{contentId}', [SocialFeedController::class, 'saveContent']);
    Route::post('feed/like/{contentId}', [SocialFeedController::class, 'likeContent']);
    Route::delete('feed/{contentId}', [SocialFeedController::class, 'deleteContent']);

    // AI Clinical Assistant Routes
    Route::post('chat/start', [AIChatController::class, 'startConversation']);
    Route::post('chat/message', [AIChatController::class, 'sendMessage']);
    Route::get('chat/history', [AIChatController::class, 'getHistory']);
    Route::get('chat/conversation/{conversationId}', [AIChatController::class, 'getConversation']);
    Route::put('chat/conversation/{conversationId}', [AIChatController::class, 'updateConversation']);
    Route::delete('chat/conversation/{conversationId}', [AIChatController::class, 'deleteConversation']);
    Route::post('chat/conversation/{conversationId}/generate-title', [AIChatController::class, 'generateTitle']);
    Route::get('chat/recommendations', [AIChatController::class, 'getRecommendations']);
    Route::post('chat/analyze-symptoms', [AIChatController::class, 'analyzeSymptoms']);
    Route::post('chat/medication-info', [AIChatController::class, 'getMedicationInfo']);
    Route::post('chat/request-appointment', [AIChatController::class, 'requestAppointment']);

    // Hybrid Database Chat Routes
    Route::prefix('chats')->group(function () {
        Route::get('/', [ChatController::class, 'index']);
        Route::post('/', [ChatController::class, 'store']);
        Route::get('/{id}', [ChatController::class, 'show']);
        Route::post('/{id}/messages', [ChatController::class, 'addMessage']);
        Route::delete('/{id}', [ChatController::class, 'destroy']);
        Route::post('/{id}/share', [ChatController::class, 'share']);
        Route::post('/{id}/private', [ChatController::class, 'makePrivate']);
    });

    // Chat utility routes
    Route::post('chat/transfer-anonymous', [ChatController::class, 'transferAnonymousConversation']);

    // Service Routes
    Route::get('services', [ServiceController::class, 'index']);
    Route::post('services', [ServiceController::class, 'store']);
    Route::get('services/{id}', [ServiceController::class, 'show']);
    Route::put('services/{id}', [ServiceController::class, 'update']);
    Route::delete('services/{id}', [ServiceController::class, 'destroy']);
    Route::get('services/category/{category}', [ServiceController::class, 'getByCategory']);
    Route::get('service-categories', [ServiceController::class, 'getCategories']);

    // Appointment Routes
    Route::post('appointments', [AppointmentController::class, 'store']);
    Route::get('appointments/available-slots', [AppointmentController::class, 'getAvailableSlots']);
    Route::get('appointments/{id}', [AppointmentController::class, 'show']);
    Route::put('appointments/{id}', [AppointmentController::class, 'update']);
    Route::delete('appointments/{id}', [AppointmentController::class, 'destroy']);

    // Payment Routes
    Route::post('payments/create-intent', [PaymentController::class, 'createPaymentIntent']);
    Route::post('payments/confirm', [PaymentController::class, 'confirmPayment']);
    Route::post('payments/process-web-payment', [PaymentController::class, 'processWebPayment']);
    Route::post('payments/confirm-elements-payment', [PaymentController::class, 'confirmElementsPayment']);
    Route::get('payments/history', [PaymentController::class, 'getPaymentHistory']);

    // Notification Routes
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::get('/unread-count', [NotificationController::class, 'unreadCount']);
        Route::post('/{id}/read', [NotificationController::class, 'markAsRead']);
        Route::post('/read-all', [NotificationController::class, 'markAllAsRead']);
        Route::post('/device-token', [NotificationController::class, 'storeDeviceToken']);
        Route::put('/device-token', [NotificationController::class, 'updateDeviceToken']);
    });

    // Social Feed Routes
    Route::get('feed', [SocialFeedController::class, 'index']);
    Route::get('feed/topics', [SocialFeedController::class, 'topics']);
    Route::post('feed/create', [SocialFeedController::class, 'create']);
    Route::post('feed/save/{contentId}', [SocialFeedController::class, 'saveContent']);
    Route::post('feed/like/{contentId}', [SocialFeedController::class, 'likeContent']);
    Route::delete('feed/{contentId}', [SocialFeedController::class, 'deleteContent']);

    // AI Clinical Assistant Routes
    Route::post('chat/start', [AIChatController::class, 'startConversation']);
    Route::post('chat/message', [AIChatController::class, 'sendMessage']);
    Route::get('chat/history', [AIChatController::class, 'getHistory']);
    Route::get('chat/conversation/{conversationId}', [AIChatController::class, 'getConversation']);
    Route::put('chat/conversation/{conversationId}', [AIChatController::class, 'updateConversation']);
    Route::delete('chat/conversation/{conversationId}', [AIChatController::class, 'deleteConversation']);
    Route::post('chat/conversation/{conversationId}/generate-title', [AIChatController::class, 'generateTitle']);
    Route::post('chat/conversation/{conversationId}/share', [ChatController::class, 'shareLegacyConversation']);
    Route::get('chat/recommendations', [AIChatController::class, 'getRecommendations']);
    Route::post('chat/analyze-symptoms', [AIChatController::class, 'analyzeSymptoms']);
    Route::post('chat/medication-info', [AIChatController::class, 'getMedicationInfo']);
    Route::post('chat/request-appointment', [AIChatController::class, 'requestAppointment']);

    // Hybrid Database Chat Routes
    Route::prefix('chats')->group(function () {
        Route::get('/', [ChatController::class, 'index']);
        Route::post('/', [ChatController::class, 'store']);
        Route::get('/{id}', [ChatController::class, 'show']);
        Route::post('/{id}/messages', [ChatController::class, 'addMessage']);
        Route::delete('/{id}', [ChatController::class, 'destroy']);
        Route::post('/{id}/share', [ChatController::class, 'share']);
        Route::post('/{id}/private', [ChatController::class, 'makePrivate']);
    });


    // Provider Marketplace Routes - grouped to avoid route binding conflicts
    Route::prefix('providers')->group(function () {
        // Provider profile management (authenticated)
        Route::post('profile', [ProviderController::class, 'createOrUpdateProfile']);
        Route::get('profile', [ProviderController::class, 'getProfile']);

        // Availability routes
        Route::get('get-availability', [ProviderAvailabilityController::class, 'getWeeklyAvailability']);
        Route::get('availability-data', [ProviderAvailabilityController::class, 'availability']);
        Route::put('availability', [ProviderAvailabilityController::class, 'updateWeeklyAvailability']);
        Route::post('availability', [ProviderAvailabilityController::class, 'postWeeklyAvailability']);

        // Absences routes
        Route::get('get-absences', [ProviderAvailabilityController::class, 'getAbsences']);
        Route::get('absences-data', [ProviderAvailabilityController::class, 'absences']);
        Route::put('absences', [ProviderAvailabilityController::class, 'updateAbsences']);
        Route::post('absences', [ProviderAvailabilityController::class, 'postAbsences']);

        // Availability slots
        Route::get('available-slots', [ProviderAvailabilityController::class, 'getAvailableTimeSlots']);
        Route::get('next-available', [ProviderAvailabilityController::class, 'getNextAvailableSlots']);

        // Individual provider - must be last to avoid conflicts
        Route::get('{id}', [ProviderController::class, 'show']);
    });

    // Service Routes
    Route::get('services', [ServiceController::class, 'index']);
    Route::post('services', [ServiceController::class, 'store']);
    Route::get('services/{id}', [ServiceController::class, 'show']);
    Route::put('services/{id}', [ServiceController::class, 'update']);
    Route::delete('services/{id}', [ServiceController::class, 'destroy']);
    Route::get('services/category/{category}', [ServiceController::class, 'getByCategory']);
    Route::get('service-categories', [ServiceController::class, 'getCategories']);

    // Patient Preferences Routes
    Route::get('patient/appointment-preferences', [PatientPreferencesController::class, 'getAppointmentPreferences']);
    Route::put('patient/appointment-preferences', [PatientPreferencesController::class, 'updateAppointmentPreferences']);

    // Appointment Routes
    Route::post('appointments', [AppointmentController::class, 'store']);
    Route::get('appointments-list', [AppointmentController::class, 'userAppointments']);
    Route::get('appointments/patient/{patientId}', [AppointmentController::class, 'patientAppointments']);
    Route::get('appointments/available-slots', [AppointmentController::class, 'getAvailableSlots']);
    Route::get('appointments/{id}', [AppointmentController::class, 'show']);
    Route::put('appointments/{id}', [AppointmentController::class, 'update']);
    Route::delete('appointments/{id}', [AppointmentController::class, 'destroy']);
    Route::delete('appointments/{id}/delete', [AppointmentController::class, 'permanentDelete']);


    // Referral Routes
    Route::get('referrals/code', [ReferralController::class, 'getReferralCode']);
    Route::post('referrals/invite', [ReferralController::class, 'createReferral']);
    Route::get('referrals/my', [ReferralController::class, 'getUserReferrals']);

    // Credit Routes
    Route::get('credits/balance', [UserCreditController::class, 'getCreditBalance']);
    Route::get('credits/transactions', [UserCreditController::class, 'getCreditTransactions']);
    Route::post('credits/use', [UserCreditController::class, 'useCredits']);

    // Club & Gamification Routes
    Route::prefix('clubs')->group(function () {
        Route::get('profile', [\App\Http\Controllers\Api\ClubController::class, 'getProfile']);
        Route::get('badges', [\App\Http\Controllers\Api\ClubController::class, 'getBadges']);
        Route::get('points', [\App\Http\Controllers\Api\ClubController::class, 'getPoints']);
        Route::get('leaderboard', [\App\Http\Controllers\Api\ClubController::class, 'getLeaderboard']);
        Route::post('check-badges', [\App\Http\Controllers\Api\ClubController::class, 'checkBadges']);
        Route::post('award-time-points', [\App\Http\Controllers\Api\ClubController::class, 'awardTimePoints']);
        Route::get('badge-definitions', [\App\Http\Controllers\Api\ClubController::class, 'getBadgeDefinitions']);
        Route::get('membership-benefits', [\App\Http\Controllers\Api\ClubController::class, 'getMembershipBenefits']);
    });



    // Video Consultation Routes (matching web routes for consistency)
    Route::post('video/initialize/{appointmentId}', [VideoConsultationController::class, 'initializeSession']);
    Route::get('video/session/{appointmentId}', [VideoConsultationController::class, 'getSessionData']);
    Route::post('video/join/{appointmentId}', [VideoConsultationController::class, 'joinSession']);
    Route::post('video/leave/{appointmentId}', [VideoConsultationController::class, 'leaveSession']);
    Route::get('video/status/{appointmentId}', [VideoConsultationController::class, 'getSessionStatus']);
    Route::post('video/participant-disconnected/{appointmentId}', [VideoConsultationController::class, 'markParticipantDisconnected']);
    Route::post('video/end/{appointmentId}', [VideoConsultationController::class, 'endSession']);

    // Legacy routes for backward compatibility
    Route::get('video/status/{appointmentId}', [VideoConsultationController::class, 'checkAppointmentStatus']);
    Route::get('video/session/{appointmentId}', [VideoConsultationController::class, 'getSessionDataApi']);
    Route::get('video/join/{appointment}', [VideoConsultationController::class, 'joinSession']);
    Route::get('video-consultations/session/{appointmentId}', [VideoConsultationController::class, 'getSessionData']);
    Route::get('video-consultations/token/{appointmentId}', [VideoConsultationController::class, 'getProperAgoraToken']);

    // Audio Recording Routes (Provider Only)
    Route::post('video/recording/start/{appointmentId}', [VideoConsultationController::class, 'startRecording']);
    Route::post('video/recording/save/{appointmentId}', [VideoConsultationController::class, 'saveRecording']);
    Route::get('video/recording/{appointmentId}', [VideoConsultationController::class, 'getRecording']);
    Route::post('video/recording/transcription/{appointmentId}', [VideoConsultationController::class, 'saveTranscription']);



    // Comments Routes
    Route::prefix('social-content/{socialContentId}/comments')->group(function () {
        Route::get('/', [\App\Http\Controllers\CommentController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\CommentController::class, 'store']);
        Route::put('/{commentId}', [\App\Http\Controllers\CommentController::class, 'update']);
        Route::delete('/{commentId}', [\App\Http\Controllers\CommentController::class, 'destroy']);
        Route::get('/{commentId}/replies', [\App\Http\Controllers\CommentController::class, 'getReplies']);
        Route::post('/{commentId}/react', [\App\Http\Controllers\CommentController::class, 'react']);
    });

    // Real-time comment count
    Route::get('social-content/{socialContentId}/comment-count', [\App\Http\Controllers\CommentController::class, 'getCommentCount']);

    // Stories Routes
    Route::prefix('stories')->group(function () {
        Route::get('/', [\App\Http\Controllers\StoryController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\StoryController::class, 'store']);
        Route::get('/user/{userId}', [\App\Http\Controllers\StoryController::class, 'getUserStories']);
        Route::post('/{storyId}/view', [\App\Http\Controllers\StoryController::class, 'markAsViewed']);
        Route::delete('/{storyId}', [\App\Http\Controllers\StoryController::class, 'destroy']);
        Route::get('/{storyId}/viewers', [\App\Http\Controllers\StoryController::class, 'getViewers']);
    });

    // Ecommerce Routes (token-based authentication for Flutter app)
    Route::prefix('shop')->group(function () {
        // Shopping cart routes
        Route::get('cart', [ShoppingCartController::class, 'index']);
        Route::post('cart/add', [ShoppingCartController::class, 'add']);
        Route::put('cart/{productId}', [ShoppingCartController::class, 'update']);
        Route::delete('cart/{productId}', [ShoppingCartController::class, 'remove']);
        Route::delete('cart', [ShoppingCartController::class, 'clear']);
        Route::get('cart/count', [ShoppingCartController::class, 'count']);

        // Order routes
        Route::get('orders', [OrderController::class, 'index']);
        Route::get('orders/{orderNumber}', [OrderController::class, 'show']);
        Route::post('checkout', [OrderController::class, 'checkout']);
        Route::post('orders/{orderNumber}/confirm-payment', [OrderController::class, 'confirmPayment']);

        // Digital downloads
        Route::get('downloads', [DigitalDownloadController::class, 'index']);
        Route::get('downloads/user', [DigitalDownloadController::class, 'userDownloads']);
        Route::get('downloads/{token}', [DigitalDownloadController::class, 'show']);
    });
});

// Public digital download route (no auth required)
Route::get('download/{token}', [DigitalDownloadController::class, 'download'])->name('api.digital-download');

// Email Template Routes
Route::prefix('email-templates')->middleware('auth:sanctum')->group(function () {
    Route::get('/', [EmailTemplateController::class, 'index']);
    Route::post('/', [EmailTemplateController::class, 'store']);
    Route::get('/{id}', [EmailTemplateController::class, 'show']);
    Route::put('/{id}', [EmailTemplateController::class, 'update']);
    Route::delete('/{id}', [EmailTemplateController::class, 'destroy']);
    Route::get('/{id}/preview', [EmailTemplateController::class, 'preview']);
    Route::post('/{id}/send-test', [EmailTemplateController::class, 'sendTestEmail']);
    Route::post('/test-config', [EmailTemplateController::class, 'testEmailConfiguration']);
    Route::get('/slug/{slug}', [EmailTemplateController::class, 'getBySlug']);
});