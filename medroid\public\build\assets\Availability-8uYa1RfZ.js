import{r as v,o as F,d as l,e as o,f as j,u as U,m as L,g as W,i as t,j as w,t as c,x as C,F as x,q as T,l as b,p as B,v as h,a as S}from"./vendor-DkZiYBIF.js";import{_ as X}from"./AppLayout.vue_vue_type_script_setup_true_lang-C2T_8RUB.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-fZxfkI-v.js";import"./createLucideIcon-CSZE2I1R.js";const z={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},H={key:0,class:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4"},I={class:"flex"},K={class:"text-red-700"},Y={key:1,class:"mb-6 bg-green-50 border border-green-200 rounded-lg p-4"},G={class:"flex"},J={class:"text-green-700"},Q={key:2,class:"text-center py-12"},Z={key:3,class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},ee={class:"lg:col-span-2"},te={class:"bg-white rounded-lg shadow-sm border p-6"},se={class:"flex justify-between items-center mb-6"},ae={class:"space-y-4"},le={class:"font-medium text-gray-900 mb-3"},oe={key:0,class:"text-gray-500 text-sm"},ne={key:1,class:"space-y-2"},ie={class:"text-sm"},re=["onClick"],de={class:"space-y-6"},ue={class:"bg-white rounded-lg shadow-sm border p-6"},ve={class:"flex justify-between items-center mb-4"},ce={key:0,class:"text-gray-500 text-sm"},me={key:1,class:"space-y-3"},pe={class:"flex justify-between items-start"},ye={class:"text-sm font-medium"},be={class:"text-xs text-gray-600 mt-1"},fe=["onClick"],ge=["disabled"],xe={key:0,class:"fas fa-spinner fa-spin mr-2"},he={key:1,class:"fas fa-save mr-2"},_e={key:0,class:"fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm flex items-center justify-center z-50"},ke={class:"bg-white rounded-lg p-6 w-full max-w-md mx-4"},we={class:"space-y-4"},Te=["value"],Se={class:"flex justify-end space-x-3 mt-6"},Ae={key:1,class:"fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm flex items-center justify-center z-50"},De={class:"bg-white rounded-lg p-6 w-full max-w-md mx-4"},Ce={class:"space-y-4"},Me={class:"flex justify-end space-x-3 mt-6"},Ne={__name:"Availability",setup(Re){const E=[{title:"Dashboard",href:"/dashboard"},{title:"Availability Management",href:"/provider/availability"}],A=v(!1),f=v(!1),n=v(null),g=v(""),D=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],m=v({Monday:[],Tuesday:[],Wednesday:[],Thursday:[],Friday:[],Saturday:[],Sunday:[]}),p=v([]),d=v({day:"Monday",startTime:"09:00",endTime:"17:00"}),r=v({startDate:"",endDate:"",reason:""}),_=v(!1),k=v(!1),V=async()=>{A.value=!0,n.value=null;try{const[a,e]=await Promise.all([S.get("/provider/get-availability"),S.get("/provider/get-absences")]);if(a.data.weekly_availability){const s={};D.forEach(i=>{s[i]=[]}),a.data.weekly_availability.forEach(i=>{i.day&&i.slots&&(s[i.day]=i.slots)}),m.value=s}e.data.absences&&(p.value=e.data.absences)}catch(a){console.error("Error fetching availability data:",a),n.value="Failed to load availability data. Please try again."}finally{A.value=!1}},q=()=>{const a=d.value.day,e={start_time:d.value.startTime,end_time:d.value.endTime};if(e.start_time>=e.end_time){n.value="End time must be after start time";return}if(m.value[a].some(u=>e.start_time<u.end_time&&e.end_time>u.start_time)){n.value="Time slot overlaps with existing slot";return}m.value[a].push(e),m.value[a].sort((u,y)=>u.start_time.localeCompare(y.start_time)),d.value={day:"Monday",startTime:"09:00",endTime:"17:00"},_.value=!1,n.value=null},$=(a,e)=>{m.value[a].splice(e,1)},N=()=>{if(!r.value.startDate||!r.value.endDate){n.value="Please select start and end dates";return}if(r.value.startDate>r.value.endDate){n.value="End date must be after start date";return}p.value.push({start_date:r.value.startDate,end_date:r.value.endDate,reason:r.value.reason||"Personal time off"}),r.value={startDate:"",endDate:"",reason:""},k.value=!1,n.value=null},O=a=>{p.value.splice(a,1)},P=async()=>{var a;f.value=!0,n.value=null,g.value="";try{const e=(a=document.querySelector('meta[name="csrf-token"]'))==null?void 0:a.getAttribute("content");console.log("CSRF Token:",e?"Present":"Missing");const s=Object.keys(m.value).map(y=>({day:y,slots:m.value[y]}));console.log("Saving availability data:",s);const i={headers:{Accept:"application/json","Content-Type":"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":e||""},withCredentials:!0},u=await S.post("/provider/save-availability",{weekly_availability:s},i);if(console.log("Availability saved successfully:",u.data),p.value&&p.value.length>0){const y=await S.post("/provider/absences",{absences:p.value},i);console.log("Absences saved successfully:",y.data)}else console.log("No absences to save, skipping absences API call");g.value="Availability updated successfully!",setTimeout(()=>{g.value=""},3e3)}catch(e){console.error("Error saving availability:",e),e.response?(console.error("Response status:",e.response.status),console.error("Response data:",e.response.data),console.error("Response headers:",e.response.headers),e.response.status===401?n.value="Authentication failed. Please refresh the page and try again.":e.response.status===403?n.value="Access denied. You may not have the required permissions.":e.response.status===419?n.value="Session expired. Please refresh the page and try again.":n.value=`Failed to save availability: ${e.response.data.message||e.message}`):e.request?(console.error("Request made but no response:",e.request),n.value="No response from server. Please check your connection."):(console.error("Error setting up request:",e.message),n.value=`Request error: ${e.message}`)}finally{f.value=!1}},M=a=>new Date(`2000-01-01T${a}`).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0}),R=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return F(()=>{V()}),(a,e)=>(o(),l(x,null,[j(U(L),{title:"Availability Management"}),j(X,{breadcrumbs:E},{default:W(()=>[t("div",z,[e[19]||(e[19]=t("div",{class:"mb-8"},[t("h1",{class:"text-3xl font-bold text-gray-900"},"Availability Management"),t("p",{class:"mt-2 text-gray-600"},"Manage your working hours and time off")],-1)),n.value?(o(),l("div",H,[t("div",I,[e[10]||(e[10]=t("i",{class:"fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"},null,-1)),t("p",K,c(n.value),1)])])):w("",!0),g.value?(o(),l("div",Y,[t("div",G,[e[11]||(e[11]=t("i",{class:"fas fa-check-circle text-green-400 mr-3 mt-0.5"},null,-1)),t("p",J,c(g.value),1)])])):w("",!0),A.value?(o(),l("div",Q,e[12]||(e[12]=[t("i",{class:"fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"},null,-1),t("p",{class:"text-gray-600"},"Loading availability data...",-1)]))):(o(),l("div",Z,[t("div",ee,[t("div",te,[t("div",se,[e[14]||(e[14]=t("h2",{class:"text-xl font-semibold text-gray-900"},"Weekly Availability",-1)),t("button",{onClick:e[0]||(e[0]=s=>_.value=!0),class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"},e[13]||(e[13]=[t("i",{class:"fas fa-plus mr-2"},null,-1),C(" Add Time Slot ")]))]),t("div",ae,[(o(),l(x,null,T(D,s=>t("div",{key:s,class:"border rounded-lg p-4"},[t("h3",le,c(s),1),m.value[s].length===0?(o(),l("div",oe," No availability set for this day ")):(o(),l("div",ne,[(o(!0),l(x,null,T(m.value[s],(i,u)=>(o(),l("div",{key:u,class:"flex justify-between items-center bg-gray-50 rounded p-3"},[t("span",ie,c(M(i.start_time))+" - "+c(M(i.end_time)),1),t("button",{onClick:y=>$(s,u),class:"text-red-600 hover:text-red-800"},e[15]||(e[15]=[t("i",{class:"fas fa-trash text-sm"},null,-1)]),8,re)]))),128))]))])),64))])])]),t("div",de,[t("div",ue,[t("div",ve,[e[17]||(e[17]=t("h2",{class:"text-lg font-semibold text-gray-900"},"Time Off",-1)),t("button",{onClick:e[1]||(e[1]=s=>k.value=!0),class:"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors"},e[16]||(e[16]=[t("i",{class:"fas fa-plus mr-1"},null,-1),C(" Add ")]))]),p.value.length===0?(o(),l("div",ce," No time off scheduled ")):(o(),l("div",me,[(o(!0),l(x,null,T(p.value,(s,i)=>(o(),l("div",{key:i,class:"border rounded p-3"},[t("div",pe,[t("div",null,[t("p",ye,c(R(s.start_date))+" - "+c(R(s.end_date)),1),t("p",be,c(s.reason),1)]),t("button",{onClick:u=>O(i),class:"text-red-600 hover:text-red-800"},e[18]||(e[18]=[t("i",{class:"fas fa-trash text-sm"},null,-1)]),8,fe)])]))),128))]))]),t("button",{onClick:P,disabled:f.value,class:"w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"},[f.value?(o(),l("i",xe)):(o(),l("i",he)),C(" "+c(f.value?"Saving...":"Save Changes"),1)],8,ge)])]))]),_.value?(o(),l("div",_e,[t("div",ke,[e[23]||(e[23]=t("h3",{class:"text-lg font-semibold mb-4"},"Add Time Slot",-1)),t("div",we,[t("div",null,[e[20]||(e[20]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Day",-1)),b(t("select",{"onUpdate:modelValue":e[2]||(e[2]=s=>d.value.day=s),class:"w-full border rounded-lg px-3 py-2"},[(o(),l(x,null,T(D,s=>t("option",{key:s,value:s},c(s),9,Te)),64))],512),[[B,d.value.day]])]),t("div",null,[e[21]||(e[21]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Start Time",-1)),b(t("input",{"onUpdate:modelValue":e[3]||(e[3]=s=>d.value.startTime=s),type:"time",class:"w-full border rounded-lg px-3 py-2"},null,512),[[h,d.value.startTime]])]),t("div",null,[e[22]||(e[22]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"End Time",-1)),b(t("input",{"onUpdate:modelValue":e[4]||(e[4]=s=>d.value.endTime=s),type:"time",class:"w-full border rounded-lg px-3 py-2"},null,512),[[h,d.value.endTime]])])]),t("div",Se,[t("button",{onClick:e[5]||(e[5]=s=>_.value=!1),class:"px-4 py-2 text-gray-600 hover:text-gray-800"}," Cancel "),t("button",{onClick:q,class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"}," Add Slot ")])])])):w("",!0),k.value?(o(),l("div",Ae,[t("div",De,[e[27]||(e[27]=t("h3",{class:"text-lg font-semibold mb-4"},"Add Time Off",-1)),t("div",Ce,[t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Start Date",-1)),b(t("input",{"onUpdate:modelValue":e[6]||(e[6]=s=>r.value.startDate=s),type:"date",class:"w-full border rounded-lg px-3 py-2"},null,512),[[h,r.value.startDate]])]),t("div",null,[e[25]||(e[25]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"End Date",-1)),b(t("input",{"onUpdate:modelValue":e[7]||(e[7]=s=>r.value.endDate=s),type:"date",class:"w-full border rounded-lg px-3 py-2"},null,512),[[h,r.value.endDate]])]),t("div",null,[e[26]||(e[26]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Reason (Optional)",-1)),b(t("input",{"onUpdate:modelValue":e[8]||(e[8]=s=>r.value.reason=s),type:"text",placeholder:"e.g., Vacation, Conference, etc.",class:"w-full border rounded-lg px-3 py-2"},null,512),[[h,r.value.reason]])])]),t("div",Me,[t("button",{onClick:e[9]||(e[9]=s=>k.value=!1),class:"px-4 py-2 text-gray-600 hover:text-gray-800"}," Cancel "),t("button",{onClick:N,class:"bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"}," Add Time Off ")])])])):w("",!0)]),_:1})],64))}};export{Ne as default};
