<?php

namespace App\Services;

use Illuminate\Http\Request;

class UserAgentService
{
    /**
     * Parse user agent string and extract device information.
     */
    public function parseUserAgent(Request $request): array
    {
        $userAgent = $request->userAgent() ?? '';
        $ipAddress = $request->ip();

        return [
            'user_agent' => $userAgent,
            'browser' => $this->getBrowser($userAgent),
            'platform' => $this->getPlatform($userAgent),
            'device_type' => $this->getDeviceType($userAgent),
            'ip_address' => $ipAddress,
        ];
    }

    /**
     * Extract browser information from user agent.
     */
    public function getBrowser(string $userAgent): string
    {
        if (str_contains($userAgent, 'Chrome')) return 'Chrome';
        if (str_contains($userAgent, 'Firefox')) return 'Firefox';
        if (str_contains($userAgent, 'Safari')) return 'Safari';
        if (str_contains($userAgent, 'Edge')) return 'Edge';
        if (str_contains($userAgent, 'Opera')) return 'Opera';
        
        return 'Unknown';
    }

    /**
     * Extract platform information from user agent.
     */
    public function getPlatform(string $userAgent): string
    {
        if (str_contains($userAgent, 'Windows')) return 'Windows';
        if (str_contains($userAgent, 'Macintosh')) return 'macOS';
        if (str_contains($userAgent, 'Linux')) return 'Linux';
        if (str_contains($userAgent, 'Android')) return 'Android';
        if (str_contains($userAgent, 'iPhone') || str_contains($userAgent, 'iPad')) return 'iOS';
        
        return 'Unknown';
    }

    /**
     * Determine device type from user agent.
     */
    private function getDeviceType(string $userAgent): string
    {
        if (str_contains($userAgent, 'Mobile') || str_contains($userAgent, 'Android')) {
            return 'mobile';
        }
        
        if (str_contains($userAgent, 'Tablet') || str_contains($userAgent, 'iPad')) {
            return 'tablet';
        }
        
        return 'web';
    }

    /**
     * Get device type for notifications (maps to existing device_type field).
     */
    public function getNotificationDeviceType(string $userAgent): string
    {
        if (str_contains($userAgent, 'Android')) return 'android';
        if (str_contains($userAgent, 'iPhone') || str_contains($userAgent, 'iPad')) return 'ios';

        return 'web';
    }

    /**
     * Capture and store user agent data for a user.
     */
    public function captureUserAgent($user, string $userAgent, array $additionalData = []): void
    {
        // For now, we'll just log the user agent data
        // This can be extended to store in database if needed
        \Log::info('User agent captured', [
            'user_id' => $user->id,
            'user_agent' => $userAgent,
            'browser' => $this->getBrowser($userAgent),
            'platform' => $this->getPlatform($userAgent),
            'device_type' => $this->getDeviceType($userAgent),
            'additional_data' => $additionalData,
        ]);
    }
}
