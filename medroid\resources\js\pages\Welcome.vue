<script setup lang="ts">
import { Head, Link, usePage } from '@inertiajs/vue3';
import { ref, nextTick, onMounted, onUnmounted } from 'vue';
import ChatInput from '@/components/ChatInput.vue';
import axios from 'axios';

// Reactive data
const messages = ref<Array<{
    id: number;
    type: 'user' | 'ai';
    content: string;
    timestamp: Date;
}>>([]);

const currentMessageIndex = ref(0);
const chatTimer = ref<number | null>(null);
const isPageVisible = ref(false);

// Get auth state from Inertia
const page = usePage();
const user = (page.props as any).auth?.user;

// Check if waitlist is enabled
const isWaitlistEnabled = ref(false); // Default to false (normal signup)

// Fetch waitlist status on mount
const fetchWaitlistStatus = async () => {
    try {
        const response = await axios.get('/api/waitlist/status');
        isWaitlistEnabled.value = response.data.enabled || false;
    } catch (error) {
        console.error('Error fetching waitlist status:', error);
        isWaitlistEnabled.value = false; // Default to normal signup if error
    }
};

// Anonymous chat state
const isChatMode = ref(false);
const isLoading = ref(false);
const userMessage = ref('');
const conversationId = ref<string | null>(null);
const anonymousId = ref<string>('');
const realChatMessages = ref<Array<{
    id: string;
    type: 'user' | 'ai';
    content: string;
    timestamp: Date;
    isStreaming?: boolean;
    displayedContent?: string;
    streamingLines?: Array<{
        content: string;
        id: string;
        fadeIn: boolean;
    }>;
}>>([]);

// Popup states
const showAgeGenderPopup = ref(false);
const showLoginPopup = ref(false);
const userAge = ref('');
const userGender = ref('');
const hasCollectedDemographics = ref(false);
const messageCount = ref(0);

// Chat input ref
const chatInputRef = ref<HTMLTextAreaElement | null>(null);

// Streaming state
const streamingMessageId = ref<string | null>(null);
const streamingTimer = ref<number | null>(null);

// Waitlist form
const waitlistEmail = ref('');
const waitlistName = ref('');
const isSubmittingWaitlist = ref(false);
const waitlistSubmitted = ref(false);
const waitlistError = ref('');

// Chat messages for animation (matching Flutter implementation)
const chatMessages = [
    {
        id: 1,
        type: 'ai' as const,
        content: 'Hi! I\'m your AI Doctor. How are you feeling today?',
        timestamp: new Date()
    },
    {
        id: 2,
        type: 'user' as const,
        content: 'I\'ve been having trouble sleeping lately. Any suggestions?',
        timestamp: new Date()
    },
    {
        id: 3,
        type: 'ai' as const,
        content: 'Sleep issues can be challenging. Try maintaining a consistent bedtime routine and avoiding screens 1 hour before sleep.',
        timestamp: new Date()
    },
    {
        id: 4,
        type: 'user' as const,
        content: 'That makes sense. I\'ll try that tonight. Thank you!',
        timestamp: new Date()
    },
    {
        id: 5,
        type: 'ai' as const,
        content: 'You\'re welcome! Also consider keeping your bedroom cool and dark. Would you like tips for managing stress?',
        timestamp: new Date()
    },
    {
        id: 6,
        type: 'user' as const,
        content: 'Yes, that would be helpful. I think stress might be affecting my sleep.',
        timestamp: new Date()
    },
    {
        id: 7,
        type: 'ai' as const,
        content: 'Try deep breathing exercises before bed. Inhale for 4 counts, hold for 7, exhale for 8. This activates your parasympathetic nervous system.',
        timestamp: new Date()
    },
    {
        id: 8,
        type: 'user' as const,
        content: 'I\'ll definitely try that breathing technique. Thank you for the personalized advice!',
        timestamp: new Date()
    }
];

// Initialize chat animation
const initializeChatAnimation = () => {
    messages.value = [];
    currentMessageIndex.value = 0;
    startChatAnimation();
};

const startChatAnimation = () => {
    if (chatTimer.value) {
        clearInterval(chatTimer.value);
    }

    chatTimer.value = setInterval(() => {
        if (currentMessageIndex.value < chatMessages.length) {
            const message = { ...chatMessages[currentMessageIndex.value] };
            message.timestamp = new Date();
            messages.value.push(message);
            currentMessageIndex.value++;
        } else {
            // Restart animation after a pause
            setTimeout(() => {
                if (isPageVisible.value) {
                    initializeChatAnimation();
                }
            }, 4000);
            if (chatTimer.value) {
                clearInterval(chatTimer.value);
                chatTimer.value = null;
            }
        }
    }, 1500);
};

onMounted(() => {
    isPageVisible.value = true;
    setTimeout(() => {
        initializeChatAnimation();
    }, 1000);
});

// Generate anonymous ID
const generateAnonymousId = () => {
    return 'anon_' + Math.random().toString(36).substring(2, 11) + '_' + Date.now();
};

// Start anonymous chat
const startAnonymousChat = async () => {
    if (!userMessage.value.trim()) return;

    try {
        isLoading.value = true;
        console.log('Starting anonymous chat...');

        // Generate anonymous ID if not exists
        if (!anonymousId.value) {
            anonymousId.value = generateAnonymousId();
        }

        console.log('Anonymous ID:', anonymousId.value);
        console.log('Initial message:', userMessage.value.trim());

        // Start conversation (just creates the conversation, doesn't send message yet)
        const startResponse = await axios.post('/api/anonymous/chat/start');

        console.log('Start API Response:', startResponse.data);

        conversationId.value = startResponse.data.conversation_id;
        anonymousId.value = startResponse.data.anonymous_id;

        // Add user message to chat
        realChatMessages.value.push({
            id: Date.now().toString(),
            type: 'user',
            content: userMessage.value.trim(),
            timestamp: new Date()
        });

        const messageToSend = userMessage.value.trim();
        userMessage.value = '';

        // Transition to chat mode first
        await transitionToChatMode();

        // Now send the first message
        const messageResponse = await axios.post('/api/anonymous/chat/message', {
            conversation_id: String(conversationId.value),
            anonymous_id: anonymousId.value,
            message: messageToSend,
            gender: userGender.value || null,
            age: userAge.value || null,
            request_full_response: false,
            generate_title: true
        });

        console.log('Message API Response:', messageResponse.data);

        // Add AI response with streaming
        if (messageResponse.data.message) {
            const aiMessageId = (Date.now() + 1).toString();
            realChatMessages.value.push({
                id: aiMessageId,
                type: 'ai',
                content: messageResponse.data.message,
                timestamp: new Date(),
                isStreaming: false,
                displayedContent: ''
            });

            // Immediately scroll to show the new message container
            await nextTick();
            scrollToBottom();

            // Start streaming the AI response
            streamAIMessage(aiMessageId, messageResponse.data.message);
        }

        messageCount.value++;

        // Show age/gender popup after first message
        if (messageCount.value === 1 && !hasCollectedDemographics.value) {
            setTimeout(() => {
                showAgeGenderPopup.value = true;
            }, 1000);
        }

        // Check if backend requires authentication (intelligent detection)
        if (messageResponse.data.requires_auth) {
            setTimeout(() => {
                showLoginPopup.value = true;
            }, 1000);
        }

        // Scroll to bottom
        await nextTick();
        scrollToBottom();

        // Refocus the input after starting chat
        setTimeout(() => {
            if (chatInputRef.value && chatInputRef.value.focus) {
                chatInputRef.value.focus();
            }
        }, 100);

    } catch (error: any) {
        console.error('Error starting chat:', error);
        console.error('Error details:', error.response?.data);
        // Show error message to user
        alert('Failed to start chat. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

// Send message in chat mode
const sendMessage = async () => {
    if (!userMessage.value.trim() || !conversationId.value) return;

    try {
        isLoading.value = true;

        // Immediately scroll when loading starts to show loading indicator
        await nextTick();
        scrollToBottom();

        console.log('Sending message...');

        // Add user message to chat
        realChatMessages.value.push({
            id: Date.now().toString(),
            type: 'user',
            content: userMessage.value.trim(),
            timestamp: new Date()
        });

        const messageToSend = userMessage.value.trim();
        userMessage.value = '';

        console.log('Message to send:', messageToSend);
        console.log('Conversation ID:', conversationId.value);

        // Send message to API
        const response = await axios.post('/api/anonymous/chat/message', {
            conversation_id: String(conversationId.value),
            message: messageToSend,
            anonymous_id: anonymousId.value,
            gender: userGender.value || null,
            age: userAge.value || null,
            request_full_response: false,
            generate_title: true
        });

        console.log('Message API Response:', response.data);

        // Add AI response with streaming
        if (response.data.message) {
            const aiMessageId = (Date.now() + 1).toString();
            realChatMessages.value.push({
                id: aiMessageId,
                type: 'ai',
                content: response.data.message,
                timestamp: new Date(),
                isStreaming: false,
                displayedContent: ''
            });

            // Immediately scroll to show the new message container
            await nextTick();
            scrollToBottom();

            // Start streaming the AI response
            streamAIMessage(aiMessageId, response.data.message);
        }

        messageCount.value++;

        // Check if backend requires authentication (intelligent detection)
        if (response.data.requires_auth) {
            setTimeout(() => {
                showLoginPopup.value = true;
            }, 1000);
        }

        // Scroll to bottom
        await nextTick();
        scrollToBottom();

        // Refocus the input after sending message
        setTimeout(() => {
            if (chatInputRef.value && chatInputRef.value.focus) {
                chatInputRef.value.focus();
            }
        }, 100);

    } catch (error: any) {
        console.error('Error sending message:', error);
        console.error('Error details:', error.response?.data);
        // Show error message to user
        alert('Failed to send message. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

// Transition to chat mode
const transitionToChatMode = async () => {
    isChatMode.value = true;

    // Stop demo animation
    if (chatTimer.value) {
        clearInterval(chatTimer.value);
        chatTimer.value = null;
    }

    await nextTick();

    // Focus on chat input after a short delay to allow transition
    setTimeout(() => {
        if (chatInputRef.value && chatInputRef.value.focus) {
            chatInputRef.value.focus();
        }
    }, 300);

    // Scroll to bottom
    scrollToBottom();
};

// Scroll to bottom of chat
const scrollToBottom = () => {
    // In the new layout, we scroll the window/document instead of a specific container
    if (isChatMode.value) {
        // Smooth scroll to bottom of the page
        window.scrollTo({
            top: document.documentElement.scrollHeight,
            behavior: 'smooth'
        });
    }
};

// Google Gemini-style line-by-line fade-in streaming
const streamAIMessage = (messageId: string, fullContent: string) => {
    const message = realChatMessages.value.find(m => m.id === messageId);
    if (!message) return;

    message.isStreaming = true;
    message.displayedContent = '';
    (message as any).streamingLines = [];
    streamingMessageId.value = messageId;

    // Split content into lines for line-by-line streaming
    const lines = fullContent.split('\n').filter(line => line.trim() !== '');
    let currentLineIndex = 0;

    const streamNextLine = () => {
        if (currentLineIndex < lines.length) {
            const currentLine = lines[currentLineIndex];

            // Add the line with fade-in effect
            (message as any).streamingLines.push({
                content: currentLine,
                id: `line-${currentLineIndex}`,
                fadeIn: true
            });

            // Update displayed content
            message.displayedContent = (message as any).streamingLines.map((line: any) => line.content).join('\n');

            currentLineIndex++;

            // Smooth scroll during streaming
            nextTick(() => scrollToBottom());

            // Stream next line after a delay (faster than before)
            const delay = currentLine.length > 100 ? 800 : 500; // Longer delay for longer lines
            streamingTimer.value = setTimeout(streamNextLine, delay);
        } else {
            // Streaming complete
            completeStreaming();
        }
    };

    const completeStreaming = () => {
        message.isStreaming = false;
        message.displayedContent = fullContent;
        (message as any).streamingLines = [];
        streamingMessageId.value = null;
        if (streamingTimer.value) {
            clearTimeout(streamingTimer.value);
            streamingTimer.value = null;
        }
        // Final scroll to ensure everything is visible
        nextTick(() => scrollToBottom());
    };

    // Start streaming with initial delay for dramatic effect
    setTimeout(() => {
        streamNextLine();
    }, 200);
};

// Handle age/gender submission
const submitAgeGender = () => {
    hasCollectedDemographics.value = true;
    showAgeGenderPopup.value = false;
};

// Format message content to handle markdown-like formatting
const formatMessage = (content: string) => {
    let formatted = content;

    // Handle bold text (**text**)
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Handle numbered lists (1. item)
    formatted = formatted.replace(/^(\d+\.\s+)(.+)$/gm, '<div class="list-item"><span class="list-number">$1</span>$2</div>');

    // Handle bullet points (- item) - Remove the bullet character since CSS adds it
    formatted = formatted.replace(/^-\s+(.+)$/gm, '<div class="bullet-item">$1</div>');

    // Handle main section headers (## text)
    formatted = formatted.replace(/^##\s+(.+)$/gm, '<h2 class="main-section-header">$1</h2>');

    // Handle section headers (### text)
    formatted = formatted.replace(/^###\s+(.+)$/gm, '<h3 class="section-header">$1</h3>');

    // Handle sub-section headers (#### text)
    formatted = formatted.replace(/^####\s+(.+)$/gm, '<h4 class="subsection-header">$1</h4>');

    // Handle subsection headers (**SECTION:**)
    formatted = formatted.replace(/\*\*([A-Z\s]+:)\*\*/g, '<h4 class="subsection-header">$1</h4>');

    // Handle line breaks
    formatted = formatted.replace(/\n/g, '<br>');

    return formatted;
};

// Handle login/register
const handleLogin = () => {
    // Store conversation data in localStorage for continuation after login
    localStorage.setItem('anonymous_conversation', JSON.stringify({
        conversation_id: conversationId.value,
        anonymous_id: anonymousId.value,
        messages: realChatMessages.value
    }));

    // Redirect to login
    window.location.href = '/login';
};

const handleRegister = () => {
    // Store conversation data in localStorage for continuation after register
    localStorage.setItem('anonymous_conversation', JSON.stringify({
        conversation_id: conversationId.value,
        anonymous_id: anonymousId.value,
        messages: realChatMessages.value
    }));

    // Redirect to register
    window.location.href = '/register';
};

// Submit waitlist form
const submitWaitlist = async () => {
    if (!waitlistEmail.value.trim()) return;

    try {
        isSubmittingWaitlist.value = true;
        waitlistError.value = '';

        const response = await axios.post('/api/waitlist/join', {
            email: waitlistEmail.value.trim(),
            name: waitlistName.value.trim() || null,
        });

        if (response.data.success) {
            waitlistSubmitted.value = true;
            waitlistEmail.value = '';
            waitlistName.value = '';
        } else {
            waitlistError.value = response.data.message || 'Failed to join waitlist';
        }
    } catch (error: any) {
        console.error('Error joining waitlist:', error);
        waitlistError.value = error.response?.data?.message || 'Failed to join waitlist. Please try again.';
    } finally {
        isSubmittingWaitlist.value = false;
    }
};

// Handle enter key in textarea
const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        if (isChatMode.value) {
            sendMessage();
        } else {
            startAnonymousChat();
        }
    }
};

onMounted(() => {
    isPageVisible.value = true;
    anonymousId.value = generateAnonymousId();
    fetchWaitlistStatus();

    setTimeout(() => {
        initializeChatAnimation();
    }, 1000);
});

onUnmounted(() => {
    isPageVisible.value = false;
    if (chatTimer.value) {
        clearInterval(chatTimer.value);
    }
    if (streamingTimer.value) {
        clearTimeout(streamingTimer.value);
    }
});
</script>

<template>
    <Head title="Medroid AI Doctor">
        <link rel="preconnect" href="https://rsms.me/" />
        <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
        <link href="https://fonts.googleapis.com/css2?family=Material+Icons" rel="stylesheet" />
    </Head>

    <div class="min-h-screen bg-white font-sans overflow-x-hidden" :class="{ 'chat-mode': isChatMode }">
        <!-- Header with Logo and Sign In -->
        <header class="flex justify-between items-center px-8 py-6" :class="{ 'fixed top-0 left-0 right-0 z-10 bg-white border-b border-gray-200': isChatMode }">
            <div class="flex items-center space-x-2">
                <!-- Medroid Logo -->
                <div class="w-8 h-8 rounded-lg flex items-center justify-center">
                    <img src="/medroid_logo.png" alt="Medroid Logo" class="w-8 h-8 object-contain">
                </div>
                <span class="text-xl font-semibold text-gray-900">Medroid</span>
            </div>
        </header>

        <!-- Main Container - Flex Layout for Scrollability -->
        <div class="flex flex-col lg:flex-row min-h-screen" :class="{ 'pt-20': isChatMode }">
            <!-- Left Panel - Scrollable -->
            <div class="w-full lg:w-1/2 bg-gray-50 flex items-center justify-center px-8 lg:px-16 py-8 lg:py-16 min-h-screen lg:min-h-0">
                <div class="max-w-lg w-full">
                    <!-- Main Heading -->
                    <div class="mb-12">
                        <h1 class="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                            Your free<br>
                            personal<br>
                            <span class="text-teal-400">AI Doctor</span> awaits<br>
                            you.
                        </h1>
                        <p class="text-gray-600 text-lg leading-relaxed">
                            Fast, free, and private medical consultations powered by AI.
                        </p>
                    </div>

                    <!-- Features List -->
                    <div class="mb-12 space-y-5">
                        <div class="flex items-center space-x-4">
                            <div class="w-1 h-7 bg-teal-400 rounded-full"></div>
                            <span class="text-gray-800 text-lg font-medium">100% free and private</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="w-1 h-7 bg-orange-400 rounded-full"></div>
                            <span class="text-gray-800 text-lg font-medium">Instant medical advice</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="w-1 h-7 bg-orange-400 rounded-full"></div>
                            <span class="text-gray-800 text-lg font-medium">Book real doctor appointments</span>
                        </div>
                    </div>



                    <!-- Action Button -->
                    <div class="space-y-4">
                        <template v-if="!user">
                            <!-- Waitlist Form (when waitlist is enabled) -->
                            <div v-if="isWaitlistEnabled && !waitlistSubmitted" class="space-y-4">
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                                    <h3 class="text-lg font-semibold text-blue-900 mb-2">🎉 Join the Waitlist</h3>
                                    <p class="text-blue-700 text-sm">
                                        Medroid is currently in invitation-only mode. Join our waitlist and we'll send you an exclusive invitation to experience the future of healthcare!
                                    </p>
                                </div>

                                <div class="space-y-3">
                                    <input
                                        v-model="waitlistName"
                                        type="text"
                                        placeholder="Your name (optional)"
                                        class="w-full max-w-xs px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-400 focus:border-teal-400"
                                    />
                                    <input
                                        v-model="waitlistEmail"
                                        type="email"
                                        placeholder="Enter your email address"
                                        required
                                        class="w-full max-w-xs px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-400 focus:border-teal-400"
                                    />
                                    <button
                                        @click="submitWaitlist"
                                        :disabled="isSubmittingWaitlist || !waitlistEmail.trim()"
                                        class="w-full max-w-xs bg-teal-500 hover:bg-teal-600 disabled:bg-gray-400 text-white font-semibold text-lg py-3 px-8 rounded-xl transition-colors duration-200"
                                    >
                                        {{ isSubmittingWaitlist ? 'Joining...' : 'Join Waitlist' }}
                                    </button>
                                </div>

                                <div v-if="waitlistError" class="text-red-600 text-sm mt-2">
                                    {{ waitlistError }}
                                </div>

                                <p class="text-xs text-gray-500 mt-3">
                                    Already have an invitation?
                                    <Link :href="route('login')" class="text-teal-500 hover:text-teal-600 font-medium">
                                        Sign in here
                                    </Link>
                                </p>
                            </div>

                            <!-- Waitlist Success Message -->
                            <div v-else-if="isWaitlistEnabled && waitlistSubmitted" class="space-y-4">
                                <div class="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
                                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </div>
                                    <h3 class="text-lg font-semibold text-green-900 mb-2">🎉 You're on the list!</h3>
                                    <p class="text-green-700 text-sm">
                                        Thank you for joining our waitlist! We'll send you an exclusive invitation soon. Keep an eye on your inbox!
                                    </p>
                                </div>
                            </div>

                            <!-- Normal signup (when waitlist is disabled) -->
                            <div v-else class="space-y-4">
                                <!-- Sign In Button (Primary) -->
                                <Link
                                    :href="route('login')"
                                    class="w-full max-w-xs bg-gray-900 text-white font-semibold text-lg py-4 px-8 rounded-xl hover:bg-gray-800 transition-colors duration-200 text-center block"
                                >
                                    Sign In
                                </Link>

                                <p class="text-lg text-gray-500">
                                    Don't have an account?
                                    <Link
                                        :href="route('register')"
                                        class="text-teal-400 hover:text-teal-500 font-medium ml-1"
                                    >
                                        Sign up here
                                    </Link>
                                </p>
                            </div>
                        </template>
                        <template v-else>
                            <Link
                                :href="route('dashboard')"
                                class="w-full max-w-xs bg-gray-900 text-white font-semibold text-lg py-4 px-8 rounded-xl hover:bg-gray-800 transition-colors duration-200 text-center block"
                            >
                                Go to Dashboard
                            </Link>
                        </template>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Chat Demo/Active Chat -->
            <div class="w-full lg:w-1/2 bg-white flex flex-col px-8 lg:px-16 justify-center py-8 lg:py-16 min-h-screen lg:min-h-0" :class="{ 'pb-32 lg:pb-40': isChatMode }">
                <div class="max-w-lg mx-auto w-full">
                    <!-- Chat Header -->
                    <div class="mb-8">
                        <div class="flex items-start space-x-3 mb-8">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0">
                                <img src="/medroid_logo.png" alt="Medroid AI" class="w-8 h-8 object-contain rounded-full">
                            </div>
                            <div class="flex-1">
                                <p class="text-gray-900 font-medium">Hi, I'm Medroid, your personal AI doctor.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Messages -->
                    <div v-if="!isChatMode" class="space-y-6 mb-10">
                        <!-- AI Message 1 -->
                        <div class="bg-white rounded-3xl rounded-tl-lg p-5 shadow-sm border border-gray-100">
                            <p class="text-gray-900 leading-relaxed">
                                As an AI doctor, I'm fast and free. I've already conducted 1,000,000+ consultations!
                            </p>
                        </div>

                        <!-- AI Message 2 -->
                        <div class="bg-white rounded-3xl rounded-tl-lg p-5 shadow-sm border border-gray-100">
                            <p class="text-gray-900 leading-relaxed">
                                When you're done, you can have a video consultation with a world class doctor, if you want, for just £55 ($75).
                            </p>
                        </div>
                    </div>

                    <!-- Real Chat Messages (Chat Mode) -->
                    <div v-if="isChatMode" class="space-y-6 mb-6 chat-messages-container">
                        <div
                            v-for="message in realChatMessages"
                            :key="message.id"
                            class="animate-fade-in-up mb-6"
                        >
                            <!-- AI Message (No bubble, left aligned with avatar) -->
                            <div v-if="message.type === 'ai'" class="flex items-start space-x-3 mb-4">
                                <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0">
                                    <img src="/medroid_logo.png" alt="Medroid AI" class="w-8 h-8 object-contain rounded-full">
                                </div>
                                <div class="flex-1">
                                    <!-- Streaming line-by-line display -->
                                    <div v-if="message.isStreaming && message.streamingLines" class="text-gray-900 leading-relaxed text-sm formatted-message">
                                        <div
                                            v-for="line in message.streamingLines"
                                            :key="line.id"
                                            class="animate-fade-in-line"
                                            v-html="formatMessage(line.content)"
                                        ></div>
                                    </div>
                                    <!-- Regular display for completed messages -->
                                    <div
                                        v-else
                                        class="text-gray-900 leading-relaxed text-sm formatted-message"
                                        v-html="formatMessage(message.content)"
                                    ></div>
                                    <!-- Modern futuristic typing cursor -->
                                    <span v-if="message.isStreaming" class="inline-flex items-center ml-1">
                                        <span class="w-0.5 h-4 bg-gradient-to-t from-medroid-orange to-orange-400 animate-futuristic-pulse rounded-full shadow-sm"></span>
                                        <span class="ml-1 flex space-x-0.5">
                                            <span class="w-1 h-1 bg-medroid-orange rounded-full animate-modern-bounce shadow-sm" style="animation-delay: 0ms"></span>
                                            <span class="w-1 h-1 bg-medroid-orange rounded-full animate-modern-bounce shadow-sm" style="animation-delay: 150ms"></span>
                                            <span class="w-1 h-1 bg-medroid-orange rounded-full animate-modern-bounce shadow-sm" style="animation-delay: 300ms"></span>
                                        </span>
                                    </span>
                                </div>
                            </div>

                            <!-- User Message (With bubble, right aligned) -->
                            <div v-else class="flex justify-end mb-4">
                                <div class="bg-medroid-orange text-white px-4 py-3 rounded-2xl rounded-br-md shadow-lg max-w-xs lg:max-w-md text-sm">
                                    {{ message.content }}
                                </div>
                            </div>
                        </div>

                        <!-- Modern loading indicator -->
                        <div v-if="isLoading" class="flex items-start space-x-3 mb-4">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0">
                                <img src="/medroid_logo.png" alt="Medroid AI" class="w-8 h-8 object-contain rounded-full">
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center space-x-1">
                                    <span class="text-sm text-gray-600">AI is thinking</span>
                                    <div class="flex space-x-1 ml-2">
                                        <div class="w-1.5 h-1.5 bg-medroid-orange rounded-full animate-modern-bounce"></div>
                                        <div class="w-1.5 h-1.5 bg-medroid-orange rounded-full animate-modern-bounce" style="animation-delay: 0.15s"></div>
                                        <div class="w-1.5 h-1.5 bg-medroid-orange rounded-full animate-modern-bounce" style="animation-delay: 0.3s"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Input (Demo Mode Only) -->
                    <div v-if="!isChatMode">
                        <ChatInput
                            ref="chatInputRef"
                            v-model="userMessage"
                            placeholder="Type your health question..."
                            :is-loading="isLoading"
                            @send="startAnonymousChat"
                            @keydown="handleKeyDown"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Age/Gender Popup -->
        <div v-if="showAgeGenderPopup" class="fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm flex items-center justify-center z-50">
            <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                <h3 class="text-2xl font-bold text-medroid-navy mb-2">Help us personalize your care</h3>
                <p class="text-medroid-slate mb-6">This information helps us provide better health recommendations.</p>

                <div class="space-y-4">
                    <!-- Age Input -->
                    <div>
                        <label class="block text-sm font-medium text-medroid-navy mb-2">Age (Optional)</label>
                        <select
                            v-model="userAge"
                            class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange bg-white"
                        >
                            <option value="">Select age range</option>
                            <option value="18-25">18-25</option>
                            <option value="26-35">26-35</option>
                            <option value="36-45">36-45</option>
                            <option value="46-60">46-60</option>
                            <option value="61-75">61-75</option>
                            <option value="75+">75+</option>
                        </select>
                    </div>

                    <!-- Gender Selection -->
                    <div>
                        <label class="block text-sm font-medium text-medroid-navy mb-3">Gender (Optional)</label>
                        <div class="flex space-x-4">
                            <label class="flex items-center">
                                <input
                                    v-model="userGender"
                                    type="radio"
                                    value="male"
                                    class="w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"
                                />
                                <span class="ml-2 text-sm text-medroid-navy">Male</span>
                            </label>
                            <label class="flex items-center">
                                <input
                                    v-model="userGender"
                                    type="radio"
                                    value="female"
                                    class="w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"
                                />
                                <span class="ml-2 text-sm text-medroid-navy">Female</span>
                            </label>
                            <label class="flex items-center">
                                <input
                                    v-model="userGender"
                                    type="radio"
                                    value="other"
                                    class="w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"
                                />
                                <span class="ml-2 text-sm text-medroid-navy">Other</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="flex space-x-3 mt-8">
                    <button
                        @click="submitAgeGender"
                        class="flex-1 bg-medroid-orange hover:bg-medroid-orange/90 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
                    >
                        Continue
                    </button>
                    <button
                        @click="showAgeGenderPopup = false"
                        class="px-6 py-3 text-medroid-slate hover:text-medroid-navy transition-colors duration-200"
                    >
                        Skip
                    </button>
                </div>
            </div>
        </div>

        <!-- Login/Register Popup -->
        <div v-if="showLoginPopup" class="fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm flex items-center justify-center z-50">
            <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                <h3 class="text-2xl font-bold text-medroid-navy mb-2">Continue with an account</h3>
                <p class="text-medroid-slate mb-6">To book an appointment, please sign in or create an account. Your chat will continue after login.</p>

                <div class="space-y-3">
                    <button
                        @click="handleLogin"
                        class="w-full bg-medroid-orange hover:bg-medroid-orange/90 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
                    >
                        Sign In
                    </button>
                    <button
                        @click="handleRegister"
                        class="w-full border border-medroid-border text-medroid-navy hover:bg-medroid-sage font-medium py-3 px-4 rounded-lg transition-colors duration-200"
                    >
                        Create Account
                    </button>
                </div>

                <button
                    @click="showLoginPopup = false"
                    class="w-full mt-4 text-medroid-slate hover:text-medroid-navy transition-colors duration-200"
                >
                    Continue without account
                </button>
            </div>
        </div>

        <!-- Chat Input (Fixed at bottom - positioned like right panel) -->
        <div v-if="isChatMode" class="fixed bottom-0 left-0 right-0 lg:left-1/2 lg:right-0 bg-white border-t border-gray-100 z-20">
            <div class="px-8 lg:px-16 py-4">
                <div class="max-w-lg mx-auto w-full">
                    <ChatInput
                        ref="chatInputRef"
                        v-model="userMessage"
                        placeholder="Type your health question..."
                        :is-loading="isLoading"
                        :show-version="true"
                        @send="sendMessage"
                        @keydown="handleKeyDown"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fade-in-up 0.8s ease-out forwards;
}

/* Chat mode transitions - disabled for left panel stability */

/* Chat messages container */
.chat-messages-container {
    scroll-behavior: smooth;
    padding-bottom: 140px; /* Space to prevent messages going under input */
}

/* Auto-resize textarea */
textarea {
    field-sizing: content;
}

/* Smooth transitions for layout changes */
.lg\:w-1\/2 {
    transition: width 0.5s ease-in-out;
}

/* Loading animation for dots */
@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

.animate-bounce {
    animation: bounce 1.4s infinite ease-in-out both;
}

/* Popup animations */
.fixed {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fixed > div {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Formatted message styles */
.formatted-message {
    line-height: 1.5;
}

.formatted-message :deep(strong) {
    font-weight: 600;
    color: #1f2937;
}

.formatted-message :deep(.main-section-header) {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
    margin: 1rem 0 0.5rem 0;
    border-bottom: 2px solid #059669;
    padding-bottom: 0.25rem;
}

.formatted-message :deep(.section-header) {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0.75rem 0 0.375rem 0;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.125rem;
}

.formatted-message :deep(.subsection-header) {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin: 0.5rem 0 0.25rem 0;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.formatted-message :deep(.list-item) {
    display: flex;
    margin: 0.125rem 0;
    padding-left: 0.25rem;
    line-height: 1.4;
}

.formatted-message :deep(.list-number) {
    font-weight: 600;
    color: #059669;
    min-width: 1.25rem;
    flex-shrink: 0;
}

.formatted-message :deep(.bullet-item) {
    margin: 0.125rem 0;
    padding-left: 0.75rem;
    position: relative;
    line-height: 1.4;
}

.formatted-message :deep(.bullet-item::before) {
    content: '';
    position: absolute;
    left: 0.25rem;
    top: 0.5rem;
    width: 3px;
    height: 3px;
    background-color: #059669;
    border-radius: 50%;
}

/* Spacing adjustments */
.formatted-message :deep(br + .main-section-header),
.formatted-message :deep(br + .section-header),
.formatted-message :deep(br + .subsection-header) {
    margin-top: 0.75rem;
}

.formatted-message :deep(.subsection-header + br + .list-item),
.formatted-message :deep(.subsection-header + br + .bullet-item) {
    margin-top: 0.25rem;
}

/* Reduce excessive line breaks */
.formatted-message :deep(br + br) {
    display: none;
}

/* Modern futuristic typing animations */
@keyframes futuristicPulse {
    0%, 100% {
        opacity: 1;
        transform: scaleY(1);
    }
    50% {
        opacity: 0.6;
        transform: scaleY(1.2);
    }
}

@keyframes modernBounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

.animate-futuristic-pulse {
    animation: futuristicPulse 1.5s ease-in-out infinite;
}

.animate-modern-bounce {
    animation: modernBounce 1.4s ease-in-out infinite;
}

/* Google Gemini-style line fade-in animation */
@keyframes fadeInLine {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-line {
    animation: fadeInLine 0.6s ease-out forwards;
}

/* Gradient text effect for streaming */
.streaming-text {
    background: linear-gradient(90deg, #374151, #6b7280, #374151);
    background-size: 200% 100%;
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}


</style>