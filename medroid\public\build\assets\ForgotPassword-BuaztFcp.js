import{N as u,V as _,y as n,e as m,g as r,f as a,d as c,j as d,i as o,u as s,m as g,t as x,z as w,x as l}from"./vendor-DkZiYBIF.js";import{_ as y}from"./InputError.vue_vue_type_script_setup_true_lang-CSFrrJiY.js";import{_ as V}from"./TextLink.vue_vue_type_script_setup_true_lang-Cj4YiVUD.js";import{_ as k}from"./index-DNsgB9iJ.js";import{_ as v,a as $}from"./Label.vue_vue_type_script_setup_true_lang-CbHOsuTJ.js";import{L as b,_ as C}from"./AuthLayout.vue_vue_type_script_setup_true_lang-DMSNRIAp.js";import"./Primitive-fZxfkI-v.js";import"./index-CM-BHwaf.js";import"./createLucideIcon-CSZE2I1R.js";const N={key:0,class:"mb-4 text-center text-sm font-medium text-green-600"},B={class:"space-y-6"},E={class:"grid gap-2"},F={class:"my-6 flex items-center justify-start"},h={class:"space-x-1 text-center text-sm text-muted-foreground"},q=u({__name:"ForgotPassword",props:{status:{}},setup(j){const t=_({email:""}),p=()=>{t.post(route("password.email"))};return(i,e)=>(m(),n(C,{title:"Forgot password",description:"Enter your email to receive a password reset link"},{default:r(()=>[a(s(g),{title:"Forgot password"}),i.status?(m(),c("div",N,x(i.status),1)):d("",!0),o("div",B,[o("form",{onSubmit:w(p,["prevent"])},[o("div",E,[a(s(v),{for:"email"},{default:r(()=>e[1]||(e[1]=[l("Email address")])),_:1}),a(s($),{id:"email",type:"email",name:"email",autocomplete:"off",modelValue:s(t).email,"onUpdate:modelValue":e[0]||(e[0]=f=>s(t).email=f),autofocus:"",placeholder:"<EMAIL>"},null,8,["modelValue"]),a(y,{message:s(t).errors.email},null,8,["message"])]),o("div",F,[a(s(k),{class:"w-full",disabled:s(t).processing},{default:r(()=>[s(t).processing?(m(),n(s(b),{key:0,class:"h-4 w-4 animate-spin"})):d("",!0),e[2]||(e[2]=l(" Email password reset link "))]),_:1},8,["disabled"])])],32),o("div",h,[e[4]||(e[4]=o("span",null,"Or, return to",-1)),a(V,{href:i.route("login")},{default:r(()=>e[3]||(e[3]=[l("log in")])),_:1},8,["href"])])])]),_:1}))}});export{q as default};
