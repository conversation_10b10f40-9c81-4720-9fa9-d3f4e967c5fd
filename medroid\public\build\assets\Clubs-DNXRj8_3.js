import{r as c,c as K,o as O,d as l,e as n,f as L,u as Q,m as Y,g as Z,i as e,j as m,t as a,x as ee,F as y,q as f,s as b,z as V,l as k,v as D,K as T,a as h}from"./vendor-DkZiYBIF.js";import{_ as te}from"./AppLayout.vue_vue_type_script_setup_true_lang-C2T_8RUB.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-fZxfkI-v.js";import"./createLucideIcon-CSZE2I1R.js";const se={class:"p-6"},oe={class:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6"},re={class:"bg-white rounded-lg shadow p-6"},ae={class:"flex items-center"},le={class:"ml-4"},ne={class:"text-2xl font-bold text-gray-900"},ie={class:"bg-white rounded-lg shadow p-6"},de={class:"flex items-center"},ue={class:"ml-4"},ce={class:"text-2xl font-bold text-gray-900"},pe={class:"bg-white rounded-lg shadow p-6"},me={class:"flex items-center"},ge={class:"ml-4"},xe={class:"text-2xl font-bold text-gray-900"},ve={class:"bg-gradient-to-r from-medroid-sage to-medroid-cream border border-medroid-orange/30 rounded-lg shadow mb-6"},be={class:"p-6"},ye={class:"flex items-center space-x-4"},fe={class:"flex-1"},he={class:"flex items-center space-x-3 p-3 bg-white border border-medroid-orange/30 rounded-lg"},we={class:"flex-1 text-sm font-mono text-medroid-navy"},_e={class:"bg-white rounded-lg shadow mb-6"},ke={class:"p-6"},Ce={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Me={class:"flex items-center space-x-3 mb-2"},Ie={class:"text-sm text-gray-600"},Be={class:"mt-3"},Se={class:"text-xs text-gray-600 mt-1 space-y-1"},Ae={key:0},Ee={key:1},je={key:2},Fe={key:3},ze={class:"bg-white rounded-lg shadow mb-6"},Le={key:0,class:"p-6"},Ve={key:1,class:"p-6 text-center text-gray-500"},De={key:2,class:"overflow-x-auto"},Te={class:"min-w-full divide-y divide-gray-200"},He={class:"bg-white divide-y divide-gray-200"},Ue={class:"px-6 py-4 whitespace-nowrap"},Ne={class:"text-sm font-medium text-gray-900"},$e={class:"text-sm text-gray-500"},Pe={class:"px-6 py-4 whitespace-nowrap"},Re={class:"px-6 py-4 whitespace-nowrap"},We={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Ge={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},qe={class:"bg-white rounded-lg shadow"},Je={key:0,class:"p-6 text-center text-gray-500"},Xe={key:1,class:"overflow-x-auto"},Ke={class:"min-w-full divide-y divide-gray-200"},Oe={class:"bg-white divide-y divide-gray-200"},Qe={class:"px-6 py-4 whitespace-nowrap"},Ye={class:"text-sm font-mono font-medium text-gray-900"},Ze={class:"px-6 py-4 whitespace-nowrap"},et={class:"text-sm font-medium text-gray-900"},tt={class:"text-sm text-gray-500"},st={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ot={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},rt={class:"px-6 py-4 whitespace-nowrap"},at={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},lt={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},nt={class:"bg-white rounded-lg shadow-xl max-w-md w-full mx-4"},it={class:"p-6"},dt={key:0,class:"mb-4 p-3 bg-green-50 border border-green-200 rounded-lg"},ut={key:1,class:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg"},ct={class:"text-red-700 text-sm"},pt={class:"space-y-2"},mt=["value"],gt={class:"ml-3 flex-1"},xt={class:"flex items-center space-x-2"},vt={class:"font-medium text-gray-900"},bt={class:"text-sm text-gray-500 mt-1"},yt={class:"flex space-x-3 pt-4"},ft=["disabled"],ht={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},wt={class:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4"},_t={class:"p-6"},kt={key:0,class:"mb-4 p-3 bg-green-50 border border-green-200 rounded-lg"},Ct={key:1,class:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg"},Mt={class:"text-red-700 text-sm"},It={class:"space-y-2"},Bt=["value"],St={class:"ml-3 flex-1"},At={class:"flex items-center space-x-2"},Et={class:"font-medium text-gray-900"},jt={class:"text-sm text-gray-500 mt-1"},Ft={class:"flex space-x-3 pt-4"},zt=["disabled"],Nt={__name:"Clubs",setup(Lt){const H=[{title:"Dashboard",href:"/dashboard"},{title:"Clubs",href:"/clubs"}],C=c(!1),M=c([]),I=c([]),w=c({total_members:0,active_codes:0,total_revenue:0}),B=c(!1),u=c({email:"",club_type:"regular"}),p=c({emails:"",club_type:"regular"}),S=c(!1),_=K(()=>typeof window<"u"?`${window.location.origin}/join-founders`:"/join-founders"),x=c(!1),v=c(!1),i=c(""),F=async()=>{C.value=!0;try{const o=await h.get("/clubs-list");M.value=o.data.data||[]}catch(o){console.error("Error fetching club members:",o)}finally{C.value=!1}},U=async()=>{try{const o=await h.get("/clubs-codes");I.value=o.data.data||[]}catch(o){console.error("Error fetching founder codes:",o)}},N=async()=>{try{const o=await h.get("/clubs-stats");w.value=o.data}catch(o){console.error("Error fetching club stats:",o)}};O(()=>{F(),U(),N()});const A=o=>new Date(o).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),$=o=>{switch(o){case"founder":return"bg-purple-100 text-purple-800";case"premium":return"bg-gold-100 text-gold-800";case"basic":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},z=o=>{switch(o){case"active":return"bg-green-100 text-green-800";case"expired":return"bg-red-100 text-red-800";case"pending":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},P=()=>{B.value=!0,u.value={email:"",club_type:"regular"},v.value=!1,i.value=""},E=()=>{B.value=!1,u.value={email:"",club_type:"regular"},v.value=!1,i.value=""},R=()=>{S.value=!0,p.value={emails:"",club_type:"regular"},v.value=!1,i.value=""},j=()=>{S.value=!1,p.value={emails:"",club_type:"regular"},v.value=!1,i.value=""},W=async()=>{var o,t;if(!u.value.email.trim()){i.value="Email is required";return}try{x.value=!0,i.value="";const s=await h.post("/send-club-invitation",{email:u.value.email.trim(),club_type:u.value.club_type});s.data.success?(v.value=!0,u.value.email="",setTimeout(()=>{E()},2e3)):i.value=s.data.message||"Failed to send invitation"}catch(s){console.error("Error sending invitation:",s),i.value=((t=(o=s.response)==null?void 0:o.data)==null?void 0:t.message)||"Failed to send invitation. Please try again."}finally{x.value=!1}},G=async()=>{var o,t;if(!p.value.emails.trim()){i.value="Please enter at least one email address";return}try{x.value=!0,i.value="";const s=p.value.emails.split(",").map(d=>d.trim()).filter(d=>d.length>0);if(s.length===0){i.value="Please enter valid email addresses";return}const r=await h.post("/send-bulk-club-invitations",{emails:s,club_type:p.value.club_type});r.data.success?(v.value=!0,setTimeout(()=>{j()},3e3),await F()):i.value=r.data.message||"Failed to send bulk invitations"}catch(s){console.error("Error sending bulk invitations:",s),i.value=((t=(o=s.response)==null?void 0:o.data)==null?void 0:t.message)||"Failed to send bulk invitations"}finally{x.value=!1}},g=o=>{const t={founder:{name:"Medroid Founders Club",description:"Exclusive founder membership with all premium benefits",color:"text-purple-600",bgColor:"bg-purple-50",borderColor:"border-purple-200"},premium:{name:"Premium Club",description:"Enhanced features and priority support",color:"text-orange-600",bgColor:"bg-orange-50",borderColor:"border-orange-200"},regular:{name:"Regular Club",description:"Standard membership with basic benefits",color:"text-blue-600",bgColor:"bg-blue-50",borderColor:"border-blue-200"}};return t[o]||t.regular},q=async()=>{const o=_.value;try{await navigator.clipboard.writeText(o),alert("Founder link copied to clipboard!")}catch(t){console.error("Failed to copy: ",t);const s=document.createElement("textarea");s.value=o,document.body.appendChild(s),s.select(),document.execCommand("copy"),document.body.removeChild(s),alert("Founder link copied to clipboard!")}},J=()=>{const o=_.value,t=encodeURIComponent(`🤖 EXCLUSIVE: Welcome to the Medroid Founders' Club

Meet your new AI Doctor!

Get instant medical advice 24/7 with Medroid - powered by advanced medical AI that understands symptoms, suggests treatments, and provides evidence-based care guidance.

Get exclusive access to Medroid with:

✨ Founders Club badge & priority support
🔥 Early access to advanced AI features
💰 $10 welcome bonus
💬 Private Slack access
👩‍⚕️ Direct access to our founders

Join now: ${o}

Be among the first to have an AI Doctor in your pocket 📱⚕️

#AIDoctor #Health #HealthTech #Innovation`),s=`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(o)}&summary=${t}`;window.open(s,"_blank")},X=()=>{const o=_.value,s=`https://wa.me/?text=${encodeURIComponent(`🤖 EXCLUSIVE: Welcome to the Medroid Founders' Club

Meet your new AI Doctor!

Get instant medical advice 24/7 with Medroid - powered by advanced medical AI that understands symptoms, suggests treatments, and provides evidence-based care guidance.

Get exclusive access to Medroid with:

✨ Founders' Club badge & priority support
🔥 Early access to advanced AI features
💰 $10 welcome bonus
💬 Private Slack access
👩‍⚕️ Direct access to our founders

Join now: ${o}

Be among the first to have an AI Doctor in your pocket 📱⚕️

#AIDoctor #Health #HealthTech #Innovation`)}`;window.open(s,"_blank")};return(o,t)=>(n(),l(y,null,[L(Q(Y),{title:"Clubs Management"}),L(te,{breadcrumbs:H},{default:Z(()=>[e("div",se,[t[26]||(t[26]=e("div",{class:"mb-6"},[e("h1",{class:"text-2xl font-bold text-gray-900"},"Clubs Management"),e("p",{class:"text-gray-600"},"Manage club memberships and founder codes")],-1)),e("div",oe,[e("div",re,[e("div",ae,[t[5]||(t[5]=e("div",{class:"p-2 bg-purple-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",le,[t[4]||(t[4]=e("p",{class:"text-sm font-medium text-gray-600"},"Total Members",-1)),e("p",ne,a(w.value.total_members),1)])])]),e("div",ie,[e("div",de,[t[7]||(t[7]=e("div",{class:"p-2 bg-blue-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"})])],-1)),e("div",ue,[t[6]||(t[6]=e("p",{class:"text-sm font-medium text-gray-600"},"Active Codes",-1)),e("p",ce,a(w.value.active_codes),1)])])]),e("div",pe,[e("div",me,[t[9]||(t[9]=e("div",{class:"p-2 bg-green-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",ge,[t[8]||(t[8]=e("p",{class:"text-sm font-medium text-gray-600"},"Total Revenue",-1)),e("p",xe,"$"+a(w.value.total_revenue),1)])])])]),e("div",ve,[t[15]||(t[15]=e("div",{class:"px-6 py-4 border-b border-medroid-orange/30"},[e("div",{class:"flex justify-between items-center"},[e("div",null,[e("h2",{class:"text-lg font-medium text-medroid-navy"},"Medroid's Founders' Club - Shareable Link"),e("p",{class:"text-sm text-medroid-slate"},"Share this link on social media to invite people to join the founders club")]),e("div",{class:"flex items-center space-x-2"},[e("span",{class:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-medroid-orange/20 text-medroid-orange"}," Public Link ")])])],-1)),e("div",be,[e("div",ye,[e("div",fe,[e("div",he,[t[11]||(t[11]=e("svg",{class:"w-5 h-5 text-medroid-orange",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"})],-1)),e("code",we,a(_.value),1),e("button",{onClick:q,class:"text-medroid-orange hover:text-medroid-orange/80 transition-colors",title:"Copy link"},t[10]||(t[10]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})],-1)]))])]),e("div",{class:"flex space-x-2"},[e("button",{onClick:J,class:"bg-medroid-teal hover:bg-medroid-teal/90 text-white px-3 py-2 rounded-lg transition-colors flex items-center space-x-1"},t[12]||(t[12]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24"},[e("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})],-1),e("span",{class:"text-xs"},"LinkedIn",-1)])),e("button",{onClick:X,class:"bg-medroid-orange hover:bg-medroid-orange/90 text-white px-3 py-2 rounded-lg transition-colors flex items-center space-x-1"},t[13]||(t[13]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24"},[e("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"})],-1),e("span",{class:"text-xs"},"WhatsApp",-1)]))])]),t[14]||(t[14]=e("div",{class:"mt-4 text-xs text-medroid-slate"},[e("strong",null,"Note:"),ee(" Anyone who signs up through this link will automatically become a Medroid Founder with platinum membership and receive a $10 welcome bonus. ")],-1))])]),e("div",_e,[e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("div",{class:"flex justify-between items-center"},[t[18]||(t[18]=e("div",null,[e("h2",{class:"text-lg font-medium text-gray-900"},"Send Club Invitation"),e("p",{class:"text-sm text-gray-500"},"Invite users to join specific club types")],-1)),e("div",{class:"flex space-x-3"},[e("button",{onClick:R,class:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"},t[16]||(t[16]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1),e("span",null,"Bulk Invite",-1)])),e("button",{onClick:P,class:"bg-medroid-orange hover:bg-medroid-orange/90 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"},t[17]||(t[17]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"Send Invitation",-1)]))])])]),e("div",ke,[e("div",Ce,[(n(!0),l(y,null,f({founder:g("founder"),premium:g("premium"),regular:g("regular")},(s,r)=>(n(),l("div",{key:r,class:b(["p-4 rounded-lg border-2 cursor-pointer transition-all duration-200",s.bgColor,s.borderColor])},[e("div",Me,[e("div",{class:b(["w-3 h-3 rounded-full",r==="founder"?"bg-purple-500":r==="premium"?"bg-orange-500":"bg-blue-500"])},null,2),e("h3",{class:b(["font-semibold",s.color])},a(s.name),3)]),e("p",Ie,a(s.description),1),e("div",Be,[t[20]||(t[20]=e("span",{class:"text-xs font-medium text-gray-500"},"Benefits include:",-1)),e("ul",Se,[r==="founder"?(n(),l("li",Ae,"• Verified founder badge")):m("",!0),r==="founder"||r==="premium"?(n(),l("li",Ee,"• Priority support")):m("",!0),r==="founder"?(n(),l("li",je,"• Exclusive features access")):m("",!0),t[19]||(t[19]=e("li",null,"• Welcome bonus credits",-1)),r==="founder"?(n(),l("li",Fe,"• Early feature access")):m("",!0)])])],2))),128))])])]),e("div",ze,[t[23]||(t[23]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h2",{class:"text-lg font-medium text-gray-900"},"Club Members")],-1)),C.value?(n(),l("div",Le,t[21]||(t[21]=[e("div",{class:"animate-pulse"},[e("div",{class:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),e("div",{class:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),e("div",{class:"h-4 bg-gray-200 rounded w-5/6"})],-1)]))):M.value.length===0?(n(),l("div",Ve," No club members found. ")):(n(),l("div",De,[e("table",Te,[t[22]||(t[22]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Member"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Membership Type"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Status"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Joined Date"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Expiry Date")])],-1)),e("tbody",He,[(n(!0),l(y,null,f(M.value,s=>{var r,d;return n(),l("tr",{key:s.id},[e("td",Ue,[e("div",Ne,a(((r=s.user)==null?void 0:r.name)||"N/A"),1),e("div",$e,a(((d=s.user)==null?void 0:d.email)||"N/A"),1)]),e("td",Pe,[e("span",{class:b([$(s.membership_type),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(s.membership_type),3)]),e("td",Re,[e("span",{class:b([z(s.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(s.status),3)]),e("td",We,a(A(s.joined_at||s.created_at)),1),e("td",Ge,a(s.expires_at?A(s.expires_at):"Never"),1)])}),128))])])]))]),e("div",qe,[t[25]||(t[25]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h2",{class:"text-lg font-medium text-gray-900"},"Founder Codes")],-1)),I.value.length===0?(n(),l("div",Je," No founder codes found. ")):(n(),l("div",Xe,[e("table",Ke,[t[24]||(t[24]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Code"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Owner"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Uses"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Max Uses"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Status"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Created")])],-1)),e("tbody",Oe,[(n(!0),l(y,null,f(I.value,s=>{var r,d;return n(),l("tr",{key:s.id},[e("td",Qe,[e("div",Ye,a(s.code),1)]),e("td",Ze,[e("div",et,a(((r=s.user)==null?void 0:r.name)||"N/A"),1),e("div",tt,a(((d=s.user)==null?void 0:d.email)||"N/A"),1)]),e("td",st,a(s.uses_count||0),1),e("td",ot,a(s.max_uses||"Unlimited"),1),e("td",rt,[e("span",{class:b([z(s.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(s.status),3)]),e("td",at,a(A(s.created_at)),1)])}),128))])])]))])]),B.value?(n(),l("div",lt,[e("div",nt,[e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("div",{class:"flex justify-between items-center"},[t[28]||(t[28]=e("h3",{class:"text-lg font-medium text-gray-900"},"Send Club Invitation",-1)),e("button",{onClick:E,class:"text-gray-400 hover:text-gray-600 transition-colors duration-200"},t[27]||(t[27]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),e("div",it,[v.value?(n(),l("div",dt,t[29]||(t[29]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-5 h-5 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})]),e("span",{class:"text-green-700 font-medium"},"Invitation sent successfully!")],-1)]))):m("",!0),i.value?(n(),l("div",ut,[e("span",ct,a(i.value),1)])):m("",!0),e("form",{onSubmit:V(W,["prevent"]),class:"space-y-4"},[e("div",null,[t[30]||(t[30]=e("label",{for:"invite_email",class:"block text-sm font-medium text-gray-700 mb-2"}," Email Address ",-1)),k(e("input",{id:"invite_email","onUpdate:modelValue":t[0]||(t[0]=s=>u.value.email=s),type:"email",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange",placeholder:"Enter email address"},null,512),[[D,u.value.email]])]),e("div",null,[t[31]||(t[31]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Club Type ",-1)),e("div",pt,[(n(!0),l(y,null,f({founder:g("founder"),premium:g("premium"),regular:g("regular")},(s,r)=>(n(),l("label",{key:r,class:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200"},[k(e("input",{"onUpdate:modelValue":t[1]||(t[1]=d=>u.value.club_type=d),value:r,type:"radio",class:"w-4 h-4 text-medroid-orange border-gray-300 focus:ring-medroid-orange"},null,8,mt),[[T,u.value.club_type]]),e("div",gt,[e("div",xt,[e("div",{class:b(["w-2 h-2 rounded-full",r==="founder"?"bg-purple-500":r==="premium"?"bg-orange-500":"bg-blue-500"])},null,2),e("span",vt,a(s.name),1)]),e("p",bt,a(s.description),1)])]))),128))])]),e("div",yt,[e("button",{type:"button",onClick:E,class:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"}," Cancel "),e("button",{type:"submit",disabled:x.value||!u.value.email.trim(),class:"flex-1 px-4 py-2 bg-medroid-orange hover:bg-medroid-orange/90 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors duration-200"},a(x.value?"Sending...":"Send Invitation"),9,ft)])],32)])])])):m("",!0),S.value?(n(),l("div",ht,[e("div",wt,[e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("div",{class:"flex justify-between items-center"},[t[33]||(t[33]=e("h3",{class:"text-lg font-medium text-gray-900"},"Send Bulk Club Invitations",-1)),e("button",{onClick:j,class:"text-gray-400 hover:text-gray-600 transition-colors duration-200"},t[32]||(t[32]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),e("div",_t,[v.value?(n(),l("div",kt,t[34]||(t[34]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-5 h-5 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})]),e("span",{class:"text-green-700 font-medium"},"Bulk invitations sent successfully!")],-1)]))):m("",!0),i.value?(n(),l("div",Ct,[e("span",Mt,a(i.value),1)])):m("",!0),e("form",{onSubmit:V(G,["prevent"]),class:"space-y-4"},[e("div",null,[t[35]||(t[35]=e("label",{for:"bulk_emails",class:"block text-sm font-medium text-gray-700 mb-2"}," Email Addresses (comma-separated) ",-1)),k(e("textarea",{id:"bulk_emails","onUpdate:modelValue":t[2]||(t[2]=s=>p.value.emails=s),rows:"6",placeholder:"<EMAIL>, <EMAIL>, <EMAIL>",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"},null,512),[[D,p.value.emails]]),t[36]||(t[36]=e("p",{class:"text-xs text-gray-500 mt-1"},"Enter multiple email addresses separated by commas",-1))]),e("div",null,[t[37]||(t[37]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Club Type ",-1)),e("div",It,[(n(!0),l(y,null,f({founder:g("founder"),premium:g("premium"),regular:g("regular")},(s,r)=>(n(),l("label",{key:r,class:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200"},[k(e("input",{"onUpdate:modelValue":t[3]||(t[3]=d=>p.value.club_type=d),value:r,type:"radio",class:"w-4 h-4 text-purple-600 border-gray-300 focus:ring-purple-500"},null,8,Bt),[[T,p.value.club_type]]),e("div",St,[e("div",At,[e("div",{class:b(["w-2 h-2 rounded-full",r==="founder"?"bg-purple-500":r==="premium"?"bg-orange-500":"bg-blue-500"])},null,2),e("span",Et,a(s.name),1)]),e("p",jt,a(s.description),1)])]))),128))])]),e("div",Ft,[e("button",{type:"button",onClick:j,class:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"}," Cancel "),e("button",{type:"submit",disabled:x.value||!p.value.emails.trim(),class:"flex-1 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors duration-200"},a(x.value?"Sending...":"Send Bulk Invitations"),9,zt)])],32)])])])):m("",!0)]),_:1})],64))}};export{Nt as default};
