<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Provider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    /**
     * Display a listing of the users.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Check if user has permission to view users
        if (!$request->user()->can('view users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = User::query();

        // Apply search filter if provided
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by role if provided
        if ($request->has('role') && $request->role) {
            $query->where('role', $request->role);
        }

        // Apply sorting
        $sortBy = $request->input('sort_by', 'created_at');
        $sortDir = $request->input('sort_dir', 'desc');
        $query->orderBy($sortBy, $sortDir);

        // Load relationships based on role
        $query->with('roles');

        // Paginate results
        $perPage = $request->input('per_page', 10);
        $users = $query->paginate($perPage);

        return response()->json($users);
    }

    /**
     * Store a newly created user in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Check if user has permission to create users
        if (!$request->user()->can('create users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:patient,provider,admin',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => bcrypt($request->password),
            'role' => $request->role,
        ]);

        // Assign role
        if ($request->has('role')) {
            // Make sure the role exists in the database
            if (!\Spatie\Permission\Models\Role::where('name', $request->role)->exists()) {
                \Spatie\Permission\Models\Role::create(['name' => $request->role]);
            }
            $user->assignRole($request->role);
        }

        // Create related profile if needed
        if ($request->role === 'provider' && $request->has('provider')) {
            $provider = new Provider([
                'specialization' => $request->provider['specialization'] ?? null,
                'license_number' => $request->provider['license_number'] ?? null,
                'verification_status' => 'pending',
            ]);
            $user->provider()->save($provider);
        }

        // Load relationships
        $user->load('roles');
        if ($user->hasRole('patient')) {
            $user->load('patient');
        } else if ($user->hasRole('provider')) {
            $user->load('provider');
        }

        return response()->json($user, 201);
    }

    /**
     * Display the specified user.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        // Check if user has permission to view users
        if (!$request->user()->can('view users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $user = User::with('roles')->findOrFail($id);

        // Load role-specific relationships
        if ($user->hasRole('patient')) {
            $user->load('patient');
        } else if ($user->hasRole('provider')) {
            $user->load('provider');
        }

        return response()->json($user);
    }

    /**
     * Update the specified user in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id = null)
    {
        // If no ID is provided, update the authenticated user's profile
        if ($id === null) {
            return $this->updateProfile($request);
        }

        // Check if user has permission to edit users
        if (!$request->user()->can('edit users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $user = User::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:users,email,' . $id,
            'role' => 'sometimes|required|in:patient,provider,admin',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Update basic user information
        if ($request->has('name')) {
            $user->name = $request->name;
        }

        if ($request->has('email')) {
            $user->email = $request->email;
        }

        if ($request->has('role') && $request->role !== $user->role) {
            $user->role = $request->role;

            // Update roles
            // Make sure the role exists in the database
            if (!\Spatie\Permission\Models\Role::where('name', $request->role)->exists()) {
                \Spatie\Permission\Models\Role::create(['name' => $request->role]);
            }
            $user->syncRoles([$request->role]);
        }

        $user->save();

        // Update password if provided
        if ($request->filled('password') && $request->filled('password_confirmation')) {
            $passwordValidator = Validator::make($request->all(), [
                'password' => 'required|string|min:8|confirmed',
            ]);

            if ($passwordValidator->fails()) {
                return response()->json(['errors' => $passwordValidator->errors()], 422);
            }

            $user->password = bcrypt($request->password);
            $user->save();
        }

        // Update provider information if applicable
        if ($user->role === 'provider' && $request->has('provider')) {
            $provider = $user->provider ?? new Provider();

            if (isset($request->provider['specialization'])) {
                $provider->specialization = $request->provider['specialization'];
            }

            if (isset($request->provider['license_number'])) {
                $provider->license_number = $request->provider['license_number'];
            }

            if (isset($request->provider['verification_status'])) {
                $provider->verification_status = $request->provider['verification_status'];
            }

            $user->provider()->save($provider);
        }

        // Reload user with relationships
        $user->load('roles');
        if ($user->role === 'patient') {
            $user->load('patient');
        } else if ($user->role === 'provider') {
            $user->load('provider');
        }

        return response()->json($user);
    }

    /**
     * Update the authenticated user's profile
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    private function updateProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:users,email,' . $request->user()->id,
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();

        if ($request->has('name')) {
            $user->name = $request->name;
        }

        if ($request->has('email')) {
            $user->email = $request->email;
        }

        $user->save();

        // Reload user with relationships
        if ($user->role === 'patient') {
            $user->load('patient');
        } else if ($user->role === 'provider') {
            $user->load('provider');
        }

        return response()->json($user);
    }

    /**
     * Upload profile image for the authenticated user
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadProfileImage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'profile_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $user = $request->user();

            // Handle file upload
            if ($request->hasFile('profile_image')) {
                $file = $request->file('profile_image');
                $filename = 'profile_' . $user->id . '_' . time() . '.' . $file->getClientOriginalExtension();

                // Make sure the directory exists
                $directory = storage_path('app/public/profile_images');
                if (!file_exists($directory)) {
                    mkdir($directory, 0755, true);
                }

                // Store the file in the public disk under profile_images folder
                $path = $file->storeAs('profile_images', $filename, 'public');

                // Log the file path for debugging
                \Illuminate\Support\Facades\Log::info('Profile image stored', [
                    'user_id' => $user->id,
                    'path' => $path,
                    'full_path' => storage_path('app/public/' . $path),
                    'url' => \Illuminate\Support\Facades\Storage::url($path)
                ]);

                // Update user profile_image field with the URL
                $user->profile_image = \Illuminate\Support\Facades\Storage::url($path);
                $user->save();

                // Reload user with relationships
                if ($user->role === 'patient') {
                    $user->load('patient');
                } else if ($user->role === 'provider') {
                    $user->load('provider');
                }

                return response()->json($user);
            }

            return response()->json(['message' => 'No image file provided'], 400);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to upload profile image', [
                'user_id' => $request->user()->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to upload profile image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified user from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        // Check if user has permission to delete users
        if (!$request->user()->can('delete users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $user = User::findOrFail($id);

        // Prevent deleting your own account
        if ($request->user()->id === (int)$id) {
            return response()->json(['message' => 'You cannot delete your own account'], 400);
        }

        // Begin transaction
        DB::beginTransaction();

        try {
            // Remove roles from user before deleting to avoid model_has_roles issues
            $user->roles()->detach();

            // Delete the user
            $user->delete();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to delete user', [
                'user_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to delete user: ' . $e->getMessage()
            ], 500);
        }

        return response()->json(['message' => 'User deleted successfully']);
    }

    /**
     * Get users without provider profiles for provider creation
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUsersWithoutProvider(Request $request)
    {
        // Check if user has permission to view users
        if (!$request->user()->can('view users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Get users who don't have a provider profile
        $users = User::whereDoesntHave('provider')
            ->where('role', '!=', 'patient') // Exclude patients as they shouldn't be providers
            ->select('id', 'name', 'email', 'role')
            ->orderBy('name')
            ->get();

        return response()->json(['users' => $users]);
    }

    /**
     * Debug route to check user permissions
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function debugPermissions(Request $request)
    {
        $user = auth()->user();
        return response()->json([
            'user_id' => $user->id,
            'user_role' => $user->role,
            'spatie_roles' => $user->roles->pluck('name'),
            'spatie_permissions' => $user->getAllPermissions()->pluck('name'),
            'can_view_clinics' => $user->can('view clinics'),
            'has_admin_role' => $user->hasRole('admin'),
        ]);
    }


}
