<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ShoppingCart;
use App\Models\DigitalProductDownload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Stripe\Stripe;
use Stripe\PaymentIntent;

class OrderController extends Controller
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    public function index(Request $request)
    {
        $orders = Order::with(['items.product'])
            ->forUser(Auth::id())
            ->recent()
            ->paginate(10);

        if ($request->expectsJson()) {
            return response()->json([
                'orders' => $orders,
            ]);
        }

        return Inertia::render('Shop/Orders', [
            'orders' => $orders,
        ]);
    }

    public function show(Request $request, $orderNumber)
    {
        $order = Order::with(['items.product.images', 'digitalDownloads'])
            ->where('order_number', $orderNumber)
            ->forUser(Auth::id())
            ->firstOrFail();

        if ($request->expectsJson()) {
            return response()->json([
                'order' => $order,
            ]);
        }

        return Inertia::render('Shop/OrderDetail', [
            'order' => $order,
        ]);
    }

    public function showCheckout(Request $request)
    {
        // Get cart items for the user
        $cartItems = ShoppingCart::with(['product.category', 'product.images'])
            ->forUser(Auth::id())
            ->get();

        if ($cartItems->isEmpty()) {
            return redirect('/shop/cart')->with('error', 'Your cart is empty');
        }

        // Calculate totals
        $subtotal = $cartItems->sum('total_price');
        $taxAmount = $subtotal * 0.08; // 8% tax rate
        $shippingAmount = 0; // Default shipping, can be calculated based on shipping method
        $totalAmount = $subtotal + $taxAmount + $shippingAmount;

        if ($request->expectsJson()) {
            return response()->json([
                'cart_items' => $cartItems,
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'shipping_amount' => $shippingAmount,
                'total_amount' => $totalAmount,
                'stripe_key' => config('services.stripe.key'),
            ]);
        }

        return Inertia::render('Shop/Checkout', [
            'cartItems' => $cartItems,
            'subtotal' => $subtotal,
            'taxAmount' => $taxAmount,
            'shippingAmount' => $shippingAmount,
            'totalAmount' => $totalAmount,
            'stripeKey' => config('services.stripe.key'),
        ]);
    }

    public function checkout(Request $request)
    {
        $request->validate([
            'billing_address' => 'required|array',
            'billing_address.first_name' => 'required|string|max:255',
            'billing_address.last_name' => 'required|string|max:255',
            'billing_address.email' => 'required|email|max:255',
            'billing_address.phone' => 'required|string|max:20',
            'billing_address.address_line_1' => 'required|string|max:255',
            'billing_address.city' => 'required|string|max:255',
            'billing_address.state' => 'required|string|max:255',
            'billing_address.postal_code' => 'required|string|max:20',
            'billing_address.country' => 'required|string|max:255',
            'shipping_address' => 'nullable|array',
            'shipping_method' => 'nullable|string',
            'payment_method_id' => 'required|string',
        ]);

        try {
            DB::beginTransaction();

            // Get cart items
            $cartItems = ShoppingCart::with(['product'])
                ->forUser(Auth::id())
                ->get();

            if ($cartItems->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cart is empty',
                ], 400);
            }

            // Calculate totals
            $subtotal = $cartItems->sum('total_price');
            $taxAmount = $subtotal * 0.08; // 8% tax rate
            $shippingAmount = $this->calculateShipping($cartItems, $request->shipping_method);
            $totalAmount = $subtotal + $taxAmount + $shippingAmount;

            // Create order
            $order = Order::create([
                'order_number' => Order::generateOrderNumber(),
                'user_id' => Auth::id(),
                'status' => 'pending',
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'shipping_amount' => $shippingAmount,
                'total_amount' => $totalAmount,
                'currency' => 'USD',
                'payment_status' => 'pending',
                'billing_address' => $request->billing_address,
                'shipping_address' => $request->shipping_address ?? $request->billing_address,
                'shipping_method' => $request->shipping_method,
            ]);

            // Create order items
            foreach ($cartItems as $cartItem) {
                $orderItem = OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $cartItem->product_id,
                    'product_name' => $cartItem->product->name,
                    'product_sku' => $cartItem->product->sku,
                    'product_type' => $cartItem->product->type,
                    'quantity' => $cartItem->quantity,
                    'unit_price' => $cartItem->price,
                    'total_price' => $cartItem->total_price,
                    'product_options' => $cartItem->product_options,
                    'digital_files' => $cartItem->product->digital_files,
                ]);

                // Decrease stock for physical products
                if ($cartItem->product->isPhysical()) {
                    $cartItem->product->decreaseStock($cartItem->quantity);
                }
            }

            // Create Stripe payment intent
            $paymentIntent = PaymentIntent::create([
                'amount' => round($totalAmount * 100), // Convert to cents
                'currency' => 'usd',
                'payment_method' => $request->payment_method_id,
                'confirm' => true,
                'return_url' => url('/shop/orders'),
                'metadata' => [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'user_id' => Auth::id(),
                ],
            ]);

            // Update order with payment intent
            $order->update([
                'payment_intent_id' => $paymentIntent->id,
            ]);

            if ($paymentIntent->status === 'succeeded') {
                $this->completeOrder($order);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'order' => $order->load(['items.product']),
                'payment_intent' => [
                    'id' => $paymentIntent->id,
                    'status' => $paymentIntent->status,
                    'client_secret' => $paymentIntent->client_secret,
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Order creation failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function confirmPayment(Request $request, $orderNumber)
    {
        $request->validate([
            'payment_intent_id' => 'required|string',
        ]);

        try {
            $order = Order::where('order_number', $orderNumber)
                ->forUser(Auth::id())
                ->firstOrFail();

            $paymentIntent = PaymentIntent::retrieve($request->payment_intent_id);

            if ($paymentIntent->status === 'succeeded') {
                $this->completeOrder($order);

                return response()->json([
                    'success' => true,
                    'message' => 'Payment confirmed',
                    'order' => $order->load(['items.product']),
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Payment not completed',
                'payment_status' => $paymentIntent->status,
            ], 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment confirmation failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    private function calculateShipping($cartItems, $shippingMethod)
    {
        // Check if cart has only digital products
        $hasPhysicalProducts = $cartItems->contains(function ($item) {
            return $item->product->isPhysical();
        });

        if (!$hasPhysicalProducts) {
            return 0; // Free shipping for digital products
        }

        // Simple shipping calculation
        switch ($shippingMethod) {
            case 'standard':
                return 5.99;
            case 'express':
                return 12.99;
            case 'overnight':
                return 24.99;
            default:
                return 5.99;
        }
    }

    private function completeOrder($order)
    {
        $order->update([
            'status' => 'processing',
            'payment_status' => 'paid',
        ]);

        // Create digital downloads for digital products
        foreach ($order->items as $item) {
            if ($item->isDigital()) {
                $item->createDigitalDownloads();
            }
        }

        // Clear user's cart
        ShoppingCart::clearCart($order->user_id);

        // TODO: Send order confirmation email
        // TODO: Send notification to admin
    }
}
