<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'product_id',
        'product_name',
        'product_sku',
        'product_type',
        'quantity',
        'unit_price',
        'total_price',
        'product_options',
        'digital_files',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'product_options' => 'array',
        'digital_files' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'formatted_unit_price',
        'formatted_total_price',
    ];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function digitalDownloads()
    {
        return $this->hasMany(DigitalProductDownload::class);
    }

    public function getFormattedUnitPriceAttribute()
    {
        return '$' . number_format($this->unit_price, 2);
    }

    public function getFormattedTotalPriceAttribute()
    {
        return '$' . number_format($this->total_price, 2);
    }

    public function isDigital()
    {
        return $this->product_type === 'digital';
    }

    public function isPhysical()
    {
        return $this->product_type === 'physical';
    }

    public function hasDigitalDownloads()
    {
        return $this->isDigital() && $this->digitalDownloads()->exists();
    }

    public function createDigitalDownloads()
    {
        if (!$this->isDigital() || !$this->digital_files) {
            return;
        }

        foreach ($this->digital_files as $file) {
            DigitalProductDownload::create([
                'user_id' => $this->order->user_id,
                'order_id' => $this->order_id,
                'order_item_id' => $this->id,
                'product_id' => $this->product_id,
                'download_token' => \Str::random(64),
                'file_path' => $file['path'],
                'original_filename' => $file['name'],
                'download_limit' => $this->product->download_limit,
                'expires_at' => $this->product->download_expiry_days 
                    ? now()->addDays($this->product->download_expiry_days) 
                    : null,
            ]);
        }
    }
}
