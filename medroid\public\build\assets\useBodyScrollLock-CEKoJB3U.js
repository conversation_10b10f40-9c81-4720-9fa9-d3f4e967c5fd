import{ab as de,aa as fe,ag as H,r as b,w as L,C,H as X,c as g,N as W,G as ve,X as K,h as pe,ah as me,ai as G,aj as ye,a4 as T,B as J,y as Q,e as Z,g as ee,Q as te,u as D,J as he}from"./vendor-DkZiYBIF.js";import{a as I,g as Ee,h as be,j as ge,k as z}from"./index-CM-BHwaf.js";import{e as we,u as ne,b as se,o as Oe,f as Pe}from"./Label.vue_vue_type_script_setup_true_lang-CbHOsuTJ.js";import{r as Se,P as oe}from"./Primitive-fZxfkI-v.js";function Ae(t,e){const n=typeof t=="string"?`${t}Context`:e,s=Symbol(n);return[i=>{const u=de(s,i);if(u||u===null)return u;throw new Error(`Injection \`${s.toString()}\` not found. Component must be used within ${Array.isArray(t)?`one of the following components: ${t.join(", ")}`:`\`${t}\``}`)},i=>(fe(s,i),i)]}const[re,tt]=Ae("ConfigProvider");let De=0;function nt(t,e="reka"){const n=re({useId:void 0});return H?`${e}-${H()}`:n.useId?`${e}-${n.useId()}`:`${e}-${++De}`}function Ce(t,e){const n=b(t);function s(a){return e[n.value][a]??n.value}return{state:n,dispatch:a=>{n.value=s(a)}}}function Te(t,e){var m;const n=b({}),s=b("none"),r=b(t),a=t.value?"mounted":"unmounted";let i;const u=((m=e.value)==null?void 0:m.ownerDocument.defaultView)??we,{state:f,dispatch:p}=Ce(a,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}}),c=v=>{var h;if(I){const F=new CustomEvent(v,{bubbles:!1,cancelable:!1});(h=e.value)==null||h.dispatchEvent(F)}};L(t,async(v,h)=>{var M;const F=h!==v;if(await C(),F){const B=s.value,S=k(e.value);v?(p("MOUNT"),c("enter"),S==="none"&&c("after-enter")):S==="none"||S==="undefined"||((M=n.value)==null?void 0:M.display)==="none"?(p("UNMOUNT"),c("leave"),c("after-leave")):h&&B!==S?(p("ANIMATION_OUT"),c("leave")):(p("UNMOUNT"),c("after-leave"))}},{immediate:!0});const o=v=>{const h=k(e.value),F=h.includes(v.animationName),M=f.value==="mounted"?"enter":"leave";if(v.target===e.value&&F&&(c(`after-${M}`),p("ANIMATION_END"),!r.value)){const B=e.value.style.animationFillMode;e.value.style.animationFillMode="forwards",i=u==null?void 0:u.setTimeout(()=>{var S;((S=e.value)==null?void 0:S.style.animationFillMode)==="forwards"&&(e.value.style.animationFillMode=B)})}v.target===e.value&&h==="none"&&p("ANIMATION_END")},d=v=>{v.target===e.value&&(s.value=k(e.value))},E=L(e,(v,h)=>{v?(n.value=getComputedStyle(v),v.addEventListener("animationstart",d),v.addEventListener("animationcancel",o),v.addEventListener("animationend",o)):(p("ANIMATION_END"),i!==void 0&&(u==null||u.clearTimeout(i)),h==null||h.removeEventListener("animationstart",d),h==null||h.removeEventListener("animationcancel",o),h==null||h.removeEventListener("animationend",o))},{immediate:!0}),l=L(f,()=>{const v=k(e.value);s.value=f.value==="mounted"?v:"none"});return X(()=>{E(),l()}),{isPresent:g(()=>["mounted","unmountSuspended"].includes(f.value))}}function k(t){return t&&getComputedStyle(t).animationName||"none"}const st=W({name:"Presence",props:{present:{type:Boolean,required:!0},forceMount:{type:Boolean}},slots:{},setup(t,{slots:e,expose:n}){var p;const{present:s,forceMount:r}=ve(t),a=b(),{isPresent:i}=Te(s,a);n({present:i});let u=e.default({present:i.value});u=Se(u||[]);const f=K();if(u&&(u==null?void 0:u.length)>1){const c=(p=f==null?void 0:f.parent)!=null&&p.type.name?`<${f.parent.type.name} />`:"component";throw new Error([`Detected an invalid children for \`${c}\` for  \`Presence\` component.`,"","Note: Presence works similarly to `v-if` directly, but it waits for animation/transition to finished before unmounting. So it expect only one direct child of valid VNode type.","You can apply a few solutions:",["Provide a single child element so that `presence` directive attach correctly.","Ensure the first child is an actual element instead of a raw text node or comment node."].map(o=>`  - ${o}`).join(`
`)].join(`
`))}return()=>r.value||s.value||i.value?pe(e.default({present:i.value})[0],{ref:c=>{const o=ne(c);return typeof(o==null?void 0:o.hasAttribute)>"u"||(o!=null&&o.hasAttribute("data-reka-popper-content-wrapper")?a.value=o.firstElementChild:a.value=o),o}}):null}});function Fe(t){const e=K(),n=e==null?void 0:e.type.emits,s={};return n!=null&&n.length||console.warn(`No emitted event found. Please check component: ${e==null?void 0:e.type.__name}`),n==null||n.forEach(r=>{s[me(G(r))]=(...a)=>t(r,...a)}),s}function Le(t){const e=K(),n=Object.keys((e==null?void 0:e.type.props)??{}).reduce((r,a)=>{const i=(e==null?void 0:e.type.props[a]).default;return i!==void 0&&(r[a]=i),r},{}),s=ye(t);return g(()=>{const r={},a=(e==null?void 0:e.vnode.props)??{};return Object.keys(a).forEach(i=>{r[G(i)]=a[i]}),Object.keys({...n,...r}).reduce((i,u)=>(s.value[u]!==void 0&&(i[u]=s.value[u]),i),{})})}function ot(t,e){const n=Le(t),s=e?Fe(e):{};return g(()=>({...n.value,...s}))}function ae(t,e,n){const s=n.originalEvent.target,r=new CustomEvent(t,{bubbles:!1,cancelable:!0,detail:n});e&&s.addEventListener(t,e,{once:!0}),s.dispatchEvent(r)}const Ie="dismissableLayer.pointerDownOutside",Me="dismissableLayer.focusOutside";function ie(t,e){const n=e.closest("[data-dismissable-layer]"),s=t.dataset.dismissableLayer===""?t:t.querySelector("[data-dismissable-layer]"),r=Array.from(t.ownerDocument.querySelectorAll("[data-dismissable-layer]"));return!!(n&&s===n||r.indexOf(s)<r.indexOf(n))}function ke(t,e){var a;const n=((a=e==null?void 0:e.value)==null?void 0:a.ownerDocument)??(globalThis==null?void 0:globalThis.document),s=b(!1),r=b(()=>{});return T(i=>{if(!I)return;const u=async p=>{const c=p.target;if(e!=null&&e.value){if(ie(e.value,c)){s.value=!1;return}if(p.target&&!s.value){let o=function(){ae(Ie,t,d)};const d={originalEvent:p};p.pointerType==="touch"?(n.removeEventListener("click",r.value),r.value=o,n.addEventListener("click",r.value,{once:!0})):o()}else n.removeEventListener("click",r.value);s.value=!1}},f=window.setTimeout(()=>{n.addEventListener("pointerdown",u)},0);i(()=>{window.clearTimeout(f),n.removeEventListener("pointerdown",u),n.removeEventListener("click",r.value)})}),{onPointerDownCapture:()=>s.value=!0}}function Ne(t,e){var r;const n=((r=e==null?void 0:e.value)==null?void 0:r.ownerDocument)??(globalThis==null?void 0:globalThis.document),s=b(!1);return T(a=>{if(!I)return;const i=async u=>{e!=null&&e.value&&(await C(),await C(),!(!e.value||ie(e.value,u.target))&&u.target&&!s.value&&ae(Me,t,{originalEvent:u}))};n.addEventListener("focusin",i),a(()=>n.removeEventListener("focusin",i))}),{onFocusCapture:()=>s.value=!0,onBlurCapture:()=>s.value=!1}}const w=J({layersRoot:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),rt=W({__name:"DismissableLayer",props:{disableOutsidePointerEvents:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","dismiss"],setup(t,{emit:e}){const n=t,s=e,{forwardRef:r,currentElement:a}=se(),i=g(()=>{var l;return((l=a.value)==null?void 0:l.ownerDocument)??globalThis.document}),u=g(()=>w.layersRoot),f=g(()=>a.value?Array.from(u.value).indexOf(a.value):-1),p=g(()=>w.layersWithOutsidePointerEventsDisabled.size>0),c=g(()=>{const l=Array.from(u.value),[y]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),m=l.indexOf(y);return f.value>=m}),o=ke(async l=>{const y=[...w.branches].some(m=>m==null?void 0:m.contains(l.target));!c.value||y||(s("pointerDownOutside",l),s("interactOutside",l),await C(),l.defaultPrevented||s("dismiss"))},a),d=Ne(l=>{[...w.branches].some(m=>m==null?void 0:m.contains(l.target))||(s("focusOutside",l),s("interactOutside",l),l.defaultPrevented||s("dismiss"))},a);Oe("Escape",l=>{f.value===u.value.size-1&&(s("escapeKeyDown",l),l.defaultPrevented||s("dismiss"))});let E;return T(l=>{a.value&&(n.disableOutsidePointerEvents&&(w.layersWithOutsidePointerEventsDisabled.size===0&&(E=i.value.body.style.pointerEvents,i.value.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(a.value)),u.value.add(a.value),l(()=>{n.disableOutsidePointerEvents&&w.layersWithOutsidePointerEventsDisabled.size===1&&(i.value.body.style.pointerEvents=E)}))}),T(l=>{l(()=>{a.value&&(u.value.delete(a.value),w.layersWithOutsidePointerEventsDisabled.delete(a.value))})}),(l,y)=>(Z(),Q(D(oe),{ref:D(r),"as-child":l.asChild,as:l.as,"data-dismissable-layer":"",style:he({pointerEvents:p.value?c.value?"auto":"none":void 0}),onFocusCapture:D(d).onFocusCapture,onBlurCapture:D(d).onBlurCapture,onPointerdownCapture:D(o).onPointerDownCapture},{default:ee(()=>[te(l.$slots,"default")]),_:3},8,["as-child","as","style","onFocusCapture","onBlurCapture","onPointerdownCapture"]))}});function P(){let t=document.activeElement;if(t==null)return null;for(;t!=null&&t.shadowRoot!=null&&t.shadowRoot.activeElement!=null;)t=t.shadowRoot.activeElement;return t}function at(t){return t?"open":"closed"}function it(t){const e=P();for(const n of t)if(n===e||(n.focus(),P()!==e))return}const x="focusScope.autoFocusOnMount",U="focusScope.autoFocusOnUnmount",V={bubbles:!1,cancelable:!0};function _e(t,{select:e=!1}={}){const n=P();for(const s of t)if(O(s,{select:e}),P()!==n)return!0}function Be(t){const e=ue(t),n=Y(e,t),s=Y(e.reverse(),t);return[n,s]}function ue(t){const e=[],n=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,{acceptNode:s=>{const r=s.tagName==="INPUT"&&s.type==="hidden";return s.disabled||s.hidden||r?NodeFilter.FILTER_SKIP:s.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)e.push(n.currentNode);return e}function Y(t,e){for(const n of t)if(!xe(n,{upTo:e}))return n}function xe(t,{upTo:e}){if(getComputedStyle(t).visibility==="hidden")return!0;for(;t;){if(e!==void 0&&t===e)return!1;if(getComputedStyle(t).display==="none")return!0;t=t.parentElement}return!1}function Ue(t){return t instanceof HTMLInputElement&&"select"in t}function O(t,{select:e=!1}={}){if(t&&t.focus){const n=P();t.focus({preventScroll:!0}),t!==n&&Ue(t)&&e&&t.select()}}const Re=Ee(()=>b([]));function $e(){const t=Re();return{add(e){const n=t.value[0];e!==n&&(n==null||n.pause()),t.value=q(t.value,e),t.value.unshift(e)},remove(e){var n;t.value=q(t.value,e),(n=t.value[0])==null||n.resume()}}}function q(t,e){const n=[...t],s=n.indexOf(e);return s!==-1&&n.splice(s,1),n}function je(t){return t.filter(e=>e.tagName!=="A")}const ut=W({__name:"FocusScope",props:{loop:{type:Boolean,default:!1},trapped:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["mountAutoFocus","unmountAutoFocus"],setup(t,{emit:e}){const n=t,s=e,{currentRef:r,currentElement:a}=se(),i=b(null),u=$e(),f=J({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}});T(c=>{if(!I)return;const o=a.value;if(!n.trapped)return;function d(m){if(f.paused||!o)return;const v=m.target;o.contains(v)?i.value=v:O(i.value,{select:!0})}function E(m){if(f.paused||!o)return;const v=m.relatedTarget;v!==null&&(o.contains(v)||O(i.value,{select:!0}))}function l(m){o.contains(i.value)||O(o)}document.addEventListener("focusin",d),document.addEventListener("focusout",E);const y=new MutationObserver(l);o&&y.observe(o,{childList:!0,subtree:!0}),c(()=>{document.removeEventListener("focusin",d),document.removeEventListener("focusout",E),y.disconnect()})}),T(async c=>{const o=a.value;if(await C(),!o)return;u.add(f);const d=P();if(!o.contains(d)){const l=new CustomEvent(x,V);o.addEventListener(x,y=>s("mountAutoFocus",y)),o.dispatchEvent(l),l.defaultPrevented||(_e(je(ue(o)),{select:!0}),P()===d&&O(o))}c(()=>{o.removeEventListener(x,m=>s("mountAutoFocus",m));const l=new CustomEvent(U,V),y=m=>{s("unmountAutoFocus",m)};o.addEventListener(U,y),o.dispatchEvent(l),setTimeout(()=>{l.defaultPrevented||O(d??document.body,{select:!0}),o.removeEventListener(U,y),u.remove(f)},0)})});function p(c){if(!n.loop&&!n.trapped||f.paused)return;const o=c.key==="Tab"&&!c.altKey&&!c.ctrlKey&&!c.metaKey,d=P();if(o&&d){const E=c.currentTarget,[l,y]=Be(E);l&&y?!c.shiftKey&&d===y?(c.preventDefault(),n.loop&&O(l,{select:!0})):c.shiftKey&&d===l&&(c.preventDefault(),n.loop&&O(y,{select:!0})):d===E&&c.preventDefault()}}return(c,o)=>(Z(),Q(D(oe),{ref_key:"currentRef",ref:r,tabindex:"-1","as-child":c.asChild,as:c.as,onKeydown:p},{default:ee(()=>[te(c.$slots,"default")]),_:3},8,["as-child","as"]))}});var We=function(t){if(typeof document>"u")return null;var e=Array.isArray(t)?t[0]:t;return e.ownerDocument.body},A=new WeakMap,N=new WeakMap,_={},R=0,ce=function(t){return t&&(t.host||ce(t.parentNode))},Ke=function(t,e){return e.map(function(n){if(t.contains(n))return n;var s=ce(n);return s&&t.contains(s)?s:(console.error("aria-hidden",n,"in not contained inside",t,". Doing nothing"),null)}).filter(function(n){return!!n})},He=function(t,e,n,s){var r=Ke(e,Array.isArray(t)?t:[t]);_[n]||(_[n]=new WeakMap);var a=_[n],i=[],u=new Set,f=new Set(r),p=function(o){!o||u.has(o)||(u.add(o),p(o.parentNode))};r.forEach(p);var c=function(o){!o||f.has(o)||Array.prototype.forEach.call(o.children,function(d){if(u.has(d))c(d);else try{var E=d.getAttribute(s),l=E!==null&&E!=="false",y=(A.get(d)||0)+1,m=(a.get(d)||0)+1;A.set(d,y),a.set(d,m),i.push(d),y===1&&l&&N.set(d,!0),m===1&&d.setAttribute(n,"true"),l||d.setAttribute(s,"true")}catch(v){console.error("aria-hidden: cannot operate on ",d,v)}})};return c(e),u.clear(),R++,function(){i.forEach(function(o){var d=A.get(o)-1,E=a.get(o)-1;A.set(o,d),a.set(o,E),d||(N.has(o)||o.removeAttribute(s),N.delete(o)),E||o.removeAttribute(n)}),R--,R||(A=new WeakMap,A=new WeakMap,N=new WeakMap,_={})}},ze=function(t,e,n){n===void 0&&(n="data-aria-hidden");var s=Array.from(Array.isArray(t)?t:[t]),r=We(t);return r?(s.push.apply(s,Array.from(r.querySelectorAll("[aria-live]"))),He(s,r,n,"aria-hidden")):function(){return null}};function ct(t){let e;L(()=>ne(t),n=>{n?e=ze(n):e&&e()}),X(()=>{e&&e()})}function $(t){if(t===null||typeof t!="object")return!1;const e=Object.getPrototypeOf(t);return e!==null&&e!==Object.prototype&&Object.getPrototypeOf(e)!==null||Symbol.iterator in t?!1:Symbol.toStringTag in t?Object.prototype.toString.call(t)==="[object Module]":!0}function j(t,e,n=".",s){if(!$(e))return j(t,{},n,s);const r=Object.assign({},e);for(const a in t){if(a==="__proto__"||a==="constructor")continue;const i=t[a];i!=null&&(s&&s(r,a,i,n)||(Array.isArray(i)&&Array.isArray(r[a])?r[a]=[...i,...r[a]]:$(i)&&$(r[a])?r[a]=j(i,r[a],(n?`${n}.`:"")+a.toString(),s):r[a]=i))}return r}function Ve(t){return(...e)=>e.reduce((n,s)=>j(n,s,"",t),{})}const Ye=Ve(),qe=be(()=>{const t=b(new Map),e=b(),n=g(()=>{for(const i of t.value.values())if(i)return!0;return!1}),s=re({scrollBody:b(!0)});let r=null;const a=()=>{document.body.style.paddingRight="",document.body.style.marginRight="",document.body.style.pointerEvents="",document.body.style.removeProperty("--scrollbar-width"),document.body.style.overflow=e.value??"",z&&(r==null||r()),e.value=void 0};return L(n,(i,u)=>{var o;if(!I)return;if(!i){u&&a();return}e.value===void 0&&(e.value=document.body.style.overflow);const f=window.innerWidth-document.documentElement.clientWidth,p={padding:f,margin:0},c=(o=s.scrollBody)!=null&&o.value?typeof s.scrollBody.value=="object"?Ye({padding:s.scrollBody.value.padding===!0?f:s.scrollBody.value.padding,margin:s.scrollBody.value.margin===!0?f:s.scrollBody.value.margin},p):p:{padding:0,margin:0};f>0&&(document.body.style.paddingRight=typeof c.padding=="number"?`${c.padding}px`:String(c.padding),document.body.style.marginRight=typeof c.margin=="number"?`${c.margin}px`:String(c.margin),document.body.style.setProperty("--scrollbar-width",`${f}px`),document.body.style.overflow="hidden"),z&&(r=Pe(document,"touchmove",d=>Xe(d),{passive:!1})),C(()=>{document.body.style.pointerEvents="none",document.body.style.overflow="hidden"})},{immediate:!0,flush:"sync"}),t});function lt(t){const e=Math.random().toString(36).substring(2,7),n=qe();n.value.set(e,t??!1);const s=g({get:()=>n.value.get(e)??!1,set:r=>n.value.set(e,r)});return ge(()=>{n.value.delete(e)}),s}function le(t){const e=window.getComputedStyle(t);if(e.overflowX==="scroll"||e.overflowY==="scroll"||e.overflowX==="auto"&&t.clientWidth<t.scrollWidth||e.overflowY==="auto"&&t.clientHeight<t.scrollHeight)return!0;{const n=t.parentNode;return!(n instanceof Element)||n.tagName==="BODY"?!1:le(n)}}function Xe(t){const e=t||window.event,n=e.target;return n instanceof Element&&le(n)?!1:e.touches.length>1?!0:(e.preventDefault&&e.cancelable&&e.preventDefault(),!1)}export{st as P,ut as _,Le as a,lt as b,Ae as c,ct as d,rt as e,it as f,P as g,ot as h,re as i,ae as j,at as k,Fe as l,nt as u};
