import 'package:flutter/foundation.dart';

/// Web notification service for handling notifications in web environment
/// This is a no-op implementation for mobile platforms
class WebNotificationService {
  static const String _tag = 'WebNotificationService';

  /// Initialize web notifications (no-op for mobile)
  Future<void> initialize() async {
    if (kDebugMode) {
      debugPrint('$_tag: Web notifications not supported on mobile platforms');
    }
  }

  /// Request notification permissions (no-op for mobile)
  Future<bool> requestPermission() async {
    if (kDebugMode) {
      debugPrint(
          '$_tag: Web notification permissions not applicable for mobile');
    }
    return false;
  }

  /// Show a notification (no-op for mobile)
  Future<void> showNotification({
    required String title,
    required String body,
    String? icon,
    Map<String, dynamic>? data,
  }) async {
    if (kDebugMode) {
      debugPrint(
          '$_tag: Web notification not shown (mobile platform): $title - $body');
    }
  }

  /// Get notification count (always 0 for mobile)
  Future<int> getNotificationCount() async {
    return 0;
  }

  /// Clear all notifications (no-op for mobile)
  Future<void> clearAllNotifications() async {
    if (kDebugMode) {
      debugPrint('$_tag: Web notifications cleared (no-op for mobile)');
    }
  }

  /// Handle notification click (no-op for mobile)
  void handleNotificationClick(Map<String, dynamic> data) {
    if (kDebugMode) {
      debugPrint(
          '$_tag: Web notification click handled (no-op for mobile): $data');
    }
  }

  /// Dispose resources (no-op for mobile)
  void dispose() {
    if (kDebugMode) {
      debugPrint('$_tag: Web notification service disposed');
    }
  }
}
