<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\User;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ensure roles exist before assigning them
        $this->ensureRolesExist();

        // Assign Spatie roles to existing users based on their role column
        $this->assignRolesToUsers();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove all role assignments
        User::all()->each(function ($user) {
            $user->syncRoles([]);
        });
    }

    /**
     * Ensure all required roles exist
     */
    private function ensureRolesExist(): void
    {
        $roles = ['admin', 'manager', 'provider', 'patient'];

        foreach ($roles as $roleName) {
            Role::firstOrCreate(['name' => $roleName, 'guard_name' => 'web']);
        }
    }

    /**
     * Assign roles to existing users
     */
    private function assignRolesToUsers(): void
    {
        User::whereNotNull('role')->chunk(100, function ($users) {
            foreach ($users as $user) {
                // Only assign if user doesn't already have this role
                if (!$user->hasRole($user->role)) {
                    try {
                        $user->assignRole($user->role);
                        echo "Assigned {$user->role} role to user {$user->email}\n";
                    } catch (\Exception $e) {
                        echo "Failed to assign role to user {$user->email}: {$e->getMessage()}\n";
                    }
                }
            }
        });
    }
};
