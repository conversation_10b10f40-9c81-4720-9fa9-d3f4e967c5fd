<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserSession;
use Illuminate\Http\Request;

class UserSessionService
{
    protected $userAgentService;

    public function __construct(UserAgentService $userAgentService)
    {
        $this->userAgentService = $userAgentService;
    }

    /**
     * Create or update user session.
     */
    public function createOrUpdateSession(User $user, Request $request): UserSession
    {
        try {
            $sessionId = $request->session()->getId();
            $deviceInfo = $this->userAgentService->parseUserAgent($request);

            return UserSession::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'session_id' => $sessionId,
                ],
                array_merge($deviceInfo, [
                    'last_activity' => now(),
                    'is_active' => true,
                ])
            );
        } catch (\Exception $e) {
            // If session tracking fails, don't break the login process
            \Log::warning('Failed to track user session: ' . $e->getMessage());

            // Return a dummy session object to prevent errors
            return new UserSession([
                'user_id' => $user->id,
                'session_id' => 'fallback',
                'last_activity' => now(),
                'is_active' => true,
            ]);
        }
    }

    /**
     * Mark session as inactive.
     */
    public function deactivateSession(string $sessionId): void
    {
        UserSession::where('session_id', $sessionId)
            ->update(['is_active' => false]);
    }

    /**
     * Get active sessions for user.
     */
    public function getActiveSessions(User $user)
    {
        return UserSession::where('user_id', $user->id)
            ->active()
            ->recent()
            ->orderBy('last_activity', 'desc')
            ->get();
    }

    /**
     * Clean up old inactive sessions.
     */
    public function cleanupOldSessions(): void
    {
        UserSession::where('last_activity', '<', now()->subDays(30))
            ->delete();
    }
}
