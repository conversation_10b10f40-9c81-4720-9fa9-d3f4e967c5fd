<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class OrderManagementController extends Controller
{
    public function index(Request $request)
    {
        if (!Auth::user()->can('manage orders') && !Auth::user()->hasRole('admin')) {
            abort(403, 'Unauthorized');
        }

        $query = Order::with(['user', 'items.product'])
            ->orderBy('created_at', 'desc');

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment status
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        // Search by order number or user
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $orders = $query->paginate(20);

        // Get summary statistics
        $stats = [
            'total_orders' => Order::count(),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'processing_orders' => Order::where('status', 'processing')->count(),
            'shipped_orders' => Order::where('status', 'shipped')->count(),
            'delivered_orders' => Order::where('status', 'delivered')->count(),
            'total_revenue' => Order::where('payment_status', 'paid')->sum('total_amount'),
            'pending_payments' => Order::where('payment_status', 'pending')->sum('total_amount'),
        ];

        if ($request->expectsJson()) {
            return response()->json([
                'orders' => $orders,
                'stats' => $stats,
            ]);
        }

        return Inertia::render('Admin/Orders/Index', [
            'orders' => $orders,
            'stats' => $stats,
            'filters' => $request->only(['status', 'payment_status', 'search', 'date_from', 'date_to']),
        ]);
    }

    public function show(Request $request, $id)
    {
        if (!Auth::user()->can('view orders')) {
            abort(403, 'Unauthorized');
        }

        $order = Order::with(['user', 'items.product.images', 'digitalDownloads'])
            ->findOrFail($id);

        if ($request->expectsJson()) {
            return response()->json([
                'order' => $order,
            ]);
        }

        return Inertia::render('Admin/Orders/Show', [
            'order' => $order,
        ]);
    }

    public function updateStatus(Request $request, $id)
    {
        if (!Auth::user()->can('manage orders') && !Auth::user()->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'status' => 'required|in:pending,processing,shipped,delivered,cancelled,refunded',
            'tracking_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $order = Order::findOrFail($id);
            $oldStatus = $order->status;

            $updateData = [
                'status' => $request->status,
                'notes' => $request->notes,
            ];

            // Handle status-specific updates
            if ($request->status === 'shipped' && $oldStatus !== 'shipped') {
                $updateData['shipped_at'] = now();
                $updateData['tracking_number'] = $request->tracking_number;
            } elseif ($request->status === 'delivered' && $oldStatus !== 'delivered') {
                $updateData['delivered_at'] = now();
                if ($oldStatus !== 'shipped') {
                    $updateData['shipped_at'] = $updateData['shipped_at'] ?? now();
                }
            } elseif ($request->status === 'cancelled') {
                // Restore stock for physical items
                foreach ($order->items as $item) {
                    if ($item->product_type === 'physical' && $item->product) {
                        $item->product->increaseStock($item->quantity);
                    }
                }
            }

            $order->update($updateData);

            return response()->json([
                'success' => true,
                'message' => 'Order status updated successfully',
                'order' => $order->load(['user', 'items.product']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update order status: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function updatePaymentStatus(Request $request, $id)
    {
        if (!Auth::user()->can('manage orders') && !Auth::user()->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'payment_status' => 'required|in:pending,paid,failed,refunded',
        ]);

        try {
            $order = Order::findOrFail($id);
            $order->update(['payment_status' => $request->payment_status]);

            return response()->json([
                'success' => true,
                'message' => 'Payment status updated successfully',
                'order' => $order->load(['user', 'items.product']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment status: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function addNote(Request $request, $id)
    {
        if (!Auth::user()->can('manage orders') && !Auth::user()->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'notes' => 'required|string|max:1000',
        ]);

        try {
            $order = Order::findOrFail($id);
            $existingNotes = $order->notes ? $order->notes . "\n\n" : '';
            $newNote = "[" . now()->format('Y-m-d H:i:s') . "] " . Auth::user()->name . ": " . $request->notes;
            
            $order->update(['notes' => $existingNotes . $newNote]);

            return response()->json([
                'success' => true,
                'message' => 'Note added successfully',
                'order' => $order->load(['user', 'items.product']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add note: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function getOrderStats(Request $request)
    {
        if (!Auth::user()->can('manage orders') && !Auth::user()->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $dateRange = $request->input('date_range', 30); // Default to last 30 days
        $startDate = now()->subDays($dateRange);

        $stats = [
            'total_orders' => Order::where('created_at', '>=', $startDate)->count(),
            'total_revenue' => Order::where('created_at', '>=', $startDate)
                                   ->where('payment_status', 'paid')
                                   ->sum('total_amount'),
            'average_order_value' => Order::where('created_at', '>=', $startDate)
                                          ->where('payment_status', 'paid')
                                          ->avg('total_amount'),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'processing_orders' => Order::where('status', 'processing')->count(),
            'shipped_orders' => Order::where('status', 'shipped')->count(),
            'delivered_orders' => Order::where('status', 'delivered')->count(),
            'cancelled_orders' => Order::where('status', 'cancelled')->count(),
        ];

        // Daily order trends
        $orderTrends = Order::selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(total_amount) as revenue')
            ->where('created_at', '>=', $startDate)
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Top selling products
        $topProducts = OrderItem::selectRaw('product_id, product_name, SUM(quantity) as total_sold, SUM(total_price) as total_revenue')
            ->whereHas('order', function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate)
                      ->where('payment_status', 'paid');
            })
            ->groupBy('product_id', 'product_name')
            ->orderBy('total_sold', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'stats' => $stats,
            'order_trends' => $orderTrends,
            'top_products' => $topProducts,
        ]);
    }

    public function exportOrders(Request $request)
    {
        if (!Auth::user()->can('manage orders') && !Auth::user()->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // This would typically generate a CSV or Excel file
        // For now, return JSON data that can be processed on frontend
        $query = Order::with(['user', 'items.product']);

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $orders = $query->get();

        return response()->json([
            'orders' => $orders,
            'exported_at' => now()->toISOString(),
        ]);
    }
}
