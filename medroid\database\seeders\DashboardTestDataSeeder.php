<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Appointment;
use App\Models\ChatConversation;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;

class DashboardTestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test users with recent login activity
        $this->createTestUsers();
        
        // Create test appointments
        $this->createTestAppointments();
        
        // Create test chat conversations
        $this->createTestChatConversations();
    }

    private function createTestUsers()
    {
        // Create admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'email_verified_at' => now(),
                'last_login_at' => now(),
                'last_activity_at' => now(),
            ]
        );

        // Create test patients with recent activity
        for ($i = 1; $i <= 10; $i++) {
            $user = User::firstOrCreate(
                ['email' => "patient{$i}@test.com"],
                [
                    'name' => "Test Patient {$i}",
                    'password' => Hash::make('password'),
                    'role' => 'patient',
                    'email_verified_at' => now(),
                    'last_login_at' => Carbon::now()->subDays(rand(0, 30)),
                    'last_activity_at' => Carbon::now()->subDays(rand(0, 30)),
                    'created_at' => Carbon::now()->subDays(rand(0, 60)),
                ]
            );

            // Create patient record
            Patient::firstOrCreate(
                ['user_id' => $user->id],
                [
                    'date_of_birth' => Carbon::now()->subYears(rand(18, 80)),
                    'gender' => ['male', 'female'][rand(0, 1)],
                ]
            );
        }

        // Create test providers
        for ($i = 1; $i <= 5; $i++) {
            $user = User::firstOrCreate(
                ['email' => "provider{$i}@test.com"],
                [
                    'name' => "Dr. Test Provider {$i}",
                    'password' => Hash::make('password'),
                    'role' => 'provider',
                    'email_verified_at' => now(),
                    'last_login_at' => Carbon::now()->subDays(rand(0, 15)),
                    'last_activity_at' => Carbon::now()->subDays(rand(0, 15)),
                    'created_at' => Carbon::now()->subDays(rand(30, 90)),
                ]
            );

            // Create provider record
            Provider::firstOrCreate(
                ['user_id' => $user->id],
                [
                    'specialization' => ['General Practice', 'Cardiology', 'Dermatology', 'Pediatrics'][rand(0, 3)],
                    'license_number' => 'LIC' . str_pad($i, 6, '0', STR_PAD_LEFT),
                    'verification_status' => 'verified',
                    'verified_at' => now(),
                    'bio' => "Experienced healthcare provider with years of practice.",
                ]
            );
        }
    }

    private function createTestAppointments()
    {
        $patients = Patient::all();
        $providers = Provider::all();

        if ($patients->isEmpty() || $providers->isEmpty()) {
            return;
        }

        // Create appointments with various statuses and dates
        for ($i = 0; $i < 20; $i++) {
            $patient = $patients->random();
            $provider = $providers->random();
            
            $scheduledAt = Carbon::now()->addDays(rand(-30, 30));
            $status = ['scheduled', 'completed', 'cancelled', 'in_progress'][rand(0, 3)];

            Appointment::create([
                'patient_id' => $patient->id,
                'provider_id' => $provider->id,
                'scheduled_at' => $scheduledAt,
                'date' => $scheduledAt->format('Y-m-d'),
                'time_slot' => [
                    'start' => $scheduledAt->format('H:i'),
                    'end' => $scheduledAt->addHour()->format('H:i'),
                ],
                'status' => $status,
                'reason' => 'Test appointment for dashboard metrics',
                'amount' => rand(50, 200),
                'payment_status' => $status === 'completed' ? 'paid' : 'pending',
                'is_telemedicine' => rand(0, 1) === 1,
            ]);
        }
    }

    private function createTestChatConversations()
    {
        $patients = Patient::all();

        if ($patients->isEmpty()) {
            return;
        }

        // Create chat conversations for AI consults
        for ($i = 0; $i < 15; $i++) {
            $patient = $patients->random();
            
            ChatConversation::create([
                'patient_id' => $patient->id,
                'title' => "Health Consultation " . ($i + 1),
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => 'I have been experiencing headaches lately. What could be the cause?',
                        'timestamp' => Carbon::now()->subDays(rand(0, 30))->toISOString(),
                    ],
                    [
                        'role' => 'assistant',
                        'content' => 'Headaches can have various causes including stress, dehydration, lack of sleep, or underlying medical conditions. I recommend staying hydrated, getting adequate rest, and if symptoms persist, consulting with a healthcare provider.',
                        'timestamp' => Carbon::now()->subDays(rand(0, 30))->toISOString(),
                    ],
                ],
                'health_concerns' => ['headache', 'stress'],
                'recommendations' => ['Stay hydrated', 'Get adequate rest', 'Monitor symptoms'],
                'escalated' => rand(0, 1) === 1,
                'created_at' => Carbon::now()->subDays(rand(0, 30)),
            ]);
        }
    }
}
