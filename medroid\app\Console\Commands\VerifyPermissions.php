<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class VerifyPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:verify {--fix : Automatically fix issues found}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify the permissions system is working correctly and optionally fix issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Verifying permissions system...');
        
        $issues = [];
        $fix = $this->option('fix');

        // Check 1: Verify tables exist
        $this->info("\n1. Checking permission tables...");
        $tables = ['roles', 'permissions', 'role_has_permissions', 'model_has_roles', 'model_has_permissions'];
        foreach ($tables as $table) {
            if (!\Schema::hasTable($table)) {
                $issues[] = "Missing table: {$table}";
                $this->error("❌ Missing table: {$table}");
            } else {
                $this->info("✅ Table exists: {$table}");
            }
        }

        // Check 2: Verify roles exist
        $this->info("\n2. Checking roles...");
        $requiredRoles = ['admin', 'manager', 'provider', 'patient'];
        foreach ($requiredRoles as $roleName) {
            $role = Role::where('name', $roleName)->first();
            if (!$role) {
                $issues[] = "Missing role: {$roleName}";
                $this->error("❌ Missing role: {$roleName}");
                
                if ($fix) {
                    Role::create(['name' => $roleName, 'guard_name' => 'web']);
                    $this->info("🔧 Created role: {$roleName}");
                }
            } else {
                $permissionCount = $role->permissions()->count();
                $this->info("✅ Role exists: {$roleName} ({$permissionCount} permissions)");
            }
        }

        // Check 3: Verify permissions exist
        $this->info("\n3. Checking permissions...");
        $permissionCount = Permission::count();
        if ($permissionCount === 0) {
            $issues[] = "No permissions found in database";
            $this->error("❌ No permissions found in database");
            
            if ($fix) {
                $this->info("🔧 Running permissions seeder...");
                $this->call('db:seed', ['--class' => 'RolesAndPermissionsSeeder']);
            }
        } else {
            $this->info("✅ Found {$permissionCount} permissions");
        }

        // Check 4: Verify users have roles
        $this->info("\n4. Checking user role assignments...");
        $totalUsers = User::count();
        $usersWithRoles = User::has('roles')->count();
        $usersWithoutRoles = $totalUsers - $usersWithRoles;

        $this->info("Total users: {$totalUsers}");
        $this->info("Users with Spatie roles: {$usersWithRoles}");
        
        if ($usersWithoutRoles > 0) {
            $issues[] = "{$usersWithoutRoles} users without Spatie roles";
            $this->error("❌ {$usersWithoutRoles} users without Spatie roles");
            
            if ($fix) {
                $this->info("🔧 Fixing user roles...");
                $this->call('fix:user-roles');
            }
        } else {
            $this->info("✅ All users have Spatie roles assigned");
        }

        // Check 5: Verify admin user specifically
        $this->info("\n5. Checking admin user...");
        $adminUser = User::where('role', 'admin')->first();
        if (!$adminUser) {
            $issues[] = "No admin user found";
            $this->error("❌ No admin user found");
        } else {
            $hasAdminRole = $adminUser->hasRole('admin');
            $adminPermissions = $adminUser->getAllPermissions()->count();
            
            $this->info("Admin user: {$adminUser->email}");
            $this->info("Has admin role: " . ($hasAdminRole ? 'Yes' : 'No'));
            $this->info("Permission count: {$adminPermissions}");
            
            if (!$hasAdminRole) {
                $issues[] = "Admin user doesn't have admin role";
                $this->error("❌ Admin user doesn't have admin role");
                
                if ($fix) {
                    $adminUser->assignRole('admin');
                    $this->info("🔧 Assigned admin role to {$adminUser->email}");
                }
            }
            
            if ($adminPermissions === 0) {
                $issues[] = "Admin user has no permissions";
                $this->error("❌ Admin user has no permissions");
            }
        }

        // Summary
        $this->info("\n" . str_repeat('=', 50));
        if (empty($issues)) {
            $this->info("✅ All permissions checks passed!");
            $this->info("🎉 Permissions system is working correctly.");
        } else {
            $this->error("❌ Found " . count($issues) . " issues:");
            foreach ($issues as $issue) {
                $this->error("   - {$issue}");
            }
            
            if (!$fix) {
                $this->info("\n💡 Run with --fix to automatically resolve issues:");
                $this->info("   php artisan permissions:verify --fix");
            }
        }

        return empty($issues) ? 0 : 1;
    }
}
