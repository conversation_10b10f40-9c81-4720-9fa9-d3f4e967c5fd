import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/models/provider.dart';
import 'package:medroid_app/models/product.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/ecommerce_service.dart';
import 'package:medroid_app/widgets/chat_scaffold.dart';
import 'package:medroid_app/widgets/provider_card.dart';
import 'package:medroid_app/utils/gradient_colors.dart';
import 'package:medroid_app/utils/app_colors.dart';

class ProviderMarketplaceScreen extends StatefulWidget {
  final Function(BuildContext, {String? initialMessage, File? imageFile})?
      onCreateNewChat;

  const ProviderMarketplaceScreen({Key? key, this.onCreateNewChat})
      : super(key: key);

  @override
  ProviderMarketplaceScreenState createState() =>
      ProviderMarketplaceScreenState();
}

class ProviderMarketplaceScreenState extends State<ProviderMarketplaceScreen>
    with TickerProviderStateMixin {
  // Tab controller for switching between Services and Products
  late TabController _tabController;

  // Provider-related state
  final List<HealthcareProvider> _providers = [];
  final List<String> _specializations = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  String? _selectedSpecialization;
  double _minRating = 0.0;
  String _sortBy = 'rating';

  // Product-related state
  final List<Product> _products = [];
  final List<ProductCategory> _categories = [];
  bool _productsLoading = false;
  String _selectedCategory = 'all';
  String _productSortBy = 'name';

  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _hasMoreProviders = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _loadMoreProviders();
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Always reload data when the widget is mounted
    _loadInitialData();
    _loadSpecializations();
    _loadProducts();
    _loadCategories();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      // Use getAvailableProviders instead of getProviders to allow unauthenticated access
      final data = await apiService.getAvailableProviders();

      final providers =
          data.map((item) => HealthcareProvider.fromJson(item)).toList();

      setState(() {
        _providers.clear();
        _providers.addAll(providers);
        _isLoading = false;
        _hasMoreProviders = providers.length >= 10; // Assuming page size is 10
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _loadMoreProviders() async {
    if (_isLoading || !_hasMoreProviders) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Use getAvailableProviders instead of getProviders to allow unauthenticated access
      final data = await apiService.getAvailableProviders();

      final newProviders =
          data.map((item) => HealthcareProvider.fromJson(item)).toList();

      setState(() {
        _providers.addAll(newProviders);
        _isLoading = false;
        _hasMoreProviders =
            newProviders.length >= 10; // Assuming page size is 10
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _loadSpecializations() async {
    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final specializations = await apiService.getProviderSpecializations();

      setState(() {
        _specializations.clear();
        _specializations.addAll(specializations);
      });
    } catch (e) {
      debugPrint('Error loading specializations: $e');

      // If we get a session expired error, just clear the specializations list
      // This prevents the error from being displayed to the user
      if (e.toString().contains('Your session has expired')) {
        setState(() {
          _specializations.clear();
        });
      }
    }
  }

  Future<void> _refreshProviders() async {
    await _loadInitialData();
  }

  // Product loading methods
  Future<void> _loadProducts() async {
    setState(() {
      _productsLoading = true;
    });

    try {
      final products = await EcommerceService.getProducts(
        category: _selectedCategory != 'all' ? _selectedCategory : null,
        search:
            _searchController.text.isNotEmpty ? _searchController.text : null,
        sort: _productSortBy,
      );

      setState(() {
        _products.clear();
        _products.addAll(products);
        _productsLoading = false;
      });
    } catch (e) {
      setState(() {
        _productsLoading = false;
      });
      debugPrint('Error loading products: $e');
    }
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await EcommerceService.getCategories();
      setState(() {
        _categories.clear();
        _categories.addAll(categories);
      });
    } catch (e) {
      debugPrint('Error loading categories: $e');
    }
  }

  Widget _buildProductCard(Product product) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          // Navigate to product detail
          Navigator.pushNamed(context, '/product-detail', arguments: product);
        },
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(12)),
                  color: Colors.grey[200],
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius:
                          const BorderRadius.vertical(top: Radius.circular(12)),
                      child: product.primaryImage != null
                          ? Image.network(
                              product.primaryImage!,
                              width: double.infinity,
                              height: double.infinity,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  color: Colors.grey[200],
                                  child: const Icon(Icons.image_not_supported,
                                      size: 50),
                                );
                              },
                            )
                          : Container(
                              color: Colors.grey[200],
                              child: const Icon(Icons.shopping_bag, size: 50),
                            ),
                    ),
                    if (product.isOnSale)
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            '${product.discountPercentage.toInt()}% OFF',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    if (product.isDigital)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppColors.tealSurge,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Text(
                            'Digital',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            // Product Info
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    if (product.shortDescription != null)
                      Text(
                        product.shortDescription!,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    const Spacer(),
                    Row(
                      children: [
                        Text(
                          product.formattedPrice,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: AppColors.tealSurge,
                          ),
                        ),
                        if (product.isOnSale) ...[
                          const SizedBox(width: 8),
                          Text(
                            product.formattedOriginalPrice,
                            style: const TextStyle(
                              decoration: TextDecoration.lineThrough,
                              color: Colors.grey,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ],
                    ),
                    if (product.isPhysical && product.stockQuantity <= 5)
                      Text(
                        'Only ${product.stockQuantity} left',
                        style: const TextStyle(
                          color: Colors.orange,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpecializationChip({
    required String label,
    required bool isSelected,
    required Function(bool) onSelected,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isSelected ? AppColors.tealSurge : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? AppColors.tealSurge : Colors.grey.withAlpha(30),
          width: 1,
        ),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: AppColors.tealSurge.withAlpha(20),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onSelected(!isSelected),
          borderRadius: BorderRadius.circular(30),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isSelected)
                  const Padding(
                    padding: EdgeInsets.only(right: 6),
                    child: Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                Text(
                  label,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black87,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
                left: 16,
                right: 16,
                top: 16,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Filter Providers',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Specialization',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  SizedBox(
                    height: 50,
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: FilterChip(
                            label: const Text('All'),
                            selected: _selectedSpecialization == null,
                            onSelected: (selected) {
                              setState(() {
                                _selectedSpecialization =
                                    selected ? null : _selectedSpecialization;
                              });
                            },
                          ),
                        ),
                        ..._specializations.map((specialization) {
                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: FilterChip(
                              label: Text(specialization),
                              selected:
                                  _selectedSpecialization == specialization,
                              onSelected: (selected) {
                                setState(() {
                                  _selectedSpecialization =
                                      selected ? specialization : null;
                                });
                              },
                            ),
                          );
                        }).toList(),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Minimum Rating',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Slider(
                          value: _minRating,
                          min: 0,
                          max: 5,
                          divisions: 10,
                          label: _minRating.toString(),
                          onChanged: (value) {
                            setState(() {
                              _minRating = value;
                            });
                          },
                        ),
                      ),
                      Container(
                        width: 40,
                        alignment: Alignment.center,
                        child: Text(
                          _minRating.toStringAsFixed(1),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Sort By',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Row(
                    children: [
                      Radio<String>(
                        value: 'rating',
                        groupValue: _sortBy,
                        onChanged: (value) {
                          setState(() {
                            _sortBy = value!;
                          });
                        },
                      ),
                      const Text('Rating'),
                      Radio<String>(
                        value: 'proximity',
                        groupValue: _sortBy,
                        onChanged: (value) {
                          setState(() {
                            _sortBy = value!;
                          });
                        },
                      ),
                      const Text('Proximity'),
                    ],
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: const Text('Cancel'),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: () {
                          _selectedSpecialization = _selectedSpecialization;
                          _minRating = _minRating;
                          _sortBy = _sortBy;
                          Navigator.pop(context);
                          _refreshProviders();
                        },
                        child: const Text('Apply Filters'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on desktop/tablet view
    final isDesktop = MediaQuery.of(context).size.width >= 768;

    return ChatScaffold(
      backgroundColor:
          AppColors.backgroundLight, // Match Discover screen background
      appBar: AppBar(
        backgroundColor:
            AppColors.backgroundLight, // Match Discover screen background
        elevation: 0,
        automaticallyImplyLeading: false,
        titleSpacing: 0, // Remove default spacing
        centerTitle: false, // Ensure title is left-aligned
        title: Padding(
          padding: EdgeInsets.only(
              left: isDesktop
                  ? 32
                  : 24), // Match the padding of the content below
          child: Text(
            'Shop',
            style: TextStyle(
              color: AppColors.tealSurge,
              fontWeight: FontWeight.bold,
              fontSize: isDesktop ? 24 : 22,
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.shopping_cart,
              color: AppColors.tealSurge,
              size: isDesktop ? 24 : 22,
            ),
            onPressed: () {
              Navigator.pushNamed(context, '/cart');
            },
          ),
          IconButton(
            icon: Icon(
              Icons.filter_list_rounded,
              color: AppColors.tealSurge,
              size: isDesktop ? 24 : 22,
            ),
            onPressed: _showFilterBottomSheet,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.tealSurge,
          labelColor: AppColors.tealSurge,
          unselectedLabelColor: Colors.grey[600],
          tabs: const [
            Tab(text: 'Services'),
            Tab(text: 'Products'),
          ],
        ),
      ),
      showChatInput: true,
      hintText: 'Ask about healthcare or products...',
      onCreateNewChat: widget.onCreateNewChat,
      body: TabBarView(
        controller: _tabController,
        children: [
          // Services Tab
          _buildServicesTab(isDesktop),
          // Products Tab
          _buildProductsTab(isDesktop),
        ],
      ),
    );
  }

  Widget _buildServicesTab(bool isDesktop) {
    return Column(
      children: [
        // Header with search bar - Minimalistic and elegant design
        Container(
          padding: EdgeInsets.fromLTRB(isDesktop ? 32 : 24, isDesktop ? 24 : 16,
              isDesktop ? 32 : 24, isDesktop ? 24 : 16),
          decoration: const BoxDecoration(
            color: AppColors.backgroundLight,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(bottom: isDesktop ? 24 : 16),
                child: Text(
                  'Connect with trusted medical professionals near you',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: isDesktop ? 16 : 14,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.grey.withAlpha(40),
                    width: 1,
                  ),
                ),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search by name or specialty...',
                    hintStyle: TextStyle(
                      color: Colors.grey[400],
                      fontSize: isDesktop ? 16 : 14,
                    ),
                    prefixIcon: Icon(
                      Icons.search_rounded,
                      color: GradientColors.primaryGradient[0],
                      size: isDesktop ? 22 : 20,
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                        vertical: isDesktop ? 16 : 14,
                        horizontal: isDesktop ? 20 : 16),
                  ),
                  onChanged: (value) {
                    // In a real app, implement search functionality
                  },
                ),
              ),
            ],
          ),
        ),

        // Specialization chips
        if (_specializations.isNotEmpty)
          Container(
            height: isDesktop ? 50 : 44,
            margin: EdgeInsets.only(top: isDesktop ? 12 : 8),
            padding: EdgeInsets.symmetric(horizontal: isDesktop ? 32 : 24),
            color: AppColors.backgroundLight,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildSpecializationChip(
                  label: 'All',
                  isSelected: _selectedSpecialization == null,
                  onSelected: (selected) {
                    setState(() {
                      _selectedSpecialization = null;
                      _refreshProviders();
                    });
                  },
                ),
                SizedBox(width: isDesktop ? 14 : 10),
                ..._specializations.map((specialization) {
                  return Padding(
                    padding: EdgeInsets.only(right: isDesktop ? 14 : 10),
                    child: _buildSpecializationChip(
                      label: specialization,
                      isSelected: _selectedSpecialization == specialization,
                      onSelected: (selected) {
                        setState(() {
                          _selectedSpecialization =
                              selected ? specialization : null;
                          _refreshProviders();
                        });
                      },
                    ),
                  );
                }).toList(),
              ],
            ),
          ),

        // Provider list section
        Expanded(
          child: _hasError
              ? Container(
                  color: AppColors.backgroundLight,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline_rounded,
                          size: 48,
                          color: GradientColors.primaryGradient[0],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error Loading Providers',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? Colors.white
                                    : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _errorMessage,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 24),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: const Color(0xFFEC4899),
                              width: 1,
                            ),
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: _refreshProviders,
                              borderRadius: BorderRadius.circular(12),
                              child: const Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 24, vertical: 12),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.refresh_rounded,
                                      color: Color(0xFFEC4899),
                                      size: 18,
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      'Try Again',
                                      style: TextStyle(
                                        color: Color(0xFFEC4899),
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : Container(
                  color: AppColors.backgroundLight,
                  child: RefreshIndicator(
                    onRefresh: _refreshProviders,
                    color: GradientColors.primaryGradient[0],
                    child: _providers.isEmpty && _isLoading
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    GradientColors.primaryGradient[0],
                                  ),
                                ),
                                SizedBox(height: isDesktop ? 24 : 16),
                                Text(
                                  'Loading providers...',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: isDesktop ? 16 : 14,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : _providers.isEmpty
                            ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.search_off_rounded,
                                      size: isDesktop ? 64 : 48,
                                      color: Colors.grey[400],
                                    ),
                                    SizedBox(height: isDesktop ? 24 : 16),
                                    Text(
                                      'No providers found',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: isDesktop ? 22 : 18,
                                        color: Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? Colors.white
                                            : Colors.black87,
                                      ),
                                    ),
                                    SizedBox(height: isDesktop ? 12 : 8),
                                    Text(
                                      'Try adjusting your filters or search terms',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontSize: isDesktop ? 16 : 14,
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : Container(
                                color: AppColors.backgroundLight,
                                child: isDesktop
                                    // Grid view for desktop
                                    ? GridView.builder(
                                        controller: _scrollController,
                                        padding: const EdgeInsets.only(
                                            top: 16,
                                            bottom: 100,
                                            left: 24,
                                            right: 24),
                                        physics: const BouncingScrollPhysics(),
                                        gridDelegate:
                                            const SliverGridDelegateWithFixedCrossAxisCount(
                                          crossAxisCount: 2,
                                          childAspectRatio: 2.2,
                                          crossAxisSpacing: 16,
                                          mainAxisSpacing: 16,
                                        ),
                                        itemCount: _providers.length +
                                            (_isLoading ? 1 : 0),
                                        itemBuilder: (context, index) {
                                          if (index == _providers.length) {
                                            return Center(
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(16.0),
                                                child:
                                                    CircularProgressIndicator(
                                                  valueColor:
                                                      AlwaysStoppedAnimation<
                                                          Color>(
                                                    GradientColors
                                                        .primaryGradient[0],
                                                  ),
                                                ),
                                              ),
                                            );
                                          }

                                          final provider = _providers[index];
                                          return ProviderCard(
                                              provider: provider);
                                        },
                                      )
                                    // List view for mobile
                                    : ListView.builder(
                                        controller: _scrollController,
                                        padding: const EdgeInsets.only(
                                            top: 8, bottom: 100),
                                        physics: const BouncingScrollPhysics(),
                                        itemCount: _providers.length +
                                            (_isLoading ? 1 : 0),
                                        itemBuilder: (context, index) {
                                          if (index == _providers.length) {
                                            return Center(
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(16.0),
                                                child:
                                                    CircularProgressIndicator(
                                                  valueColor:
                                                      AlwaysStoppedAnimation<
                                                          Color>(
                                                    GradientColors
                                                        .primaryGradient[0],
                                                  ),
                                                ),
                                              ),
                                            );
                                          }

                                          final provider = _providers[index];
                                          return ProviderCard(
                                              provider: provider);
                                        },
                                      ),
                              ),
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildProductsTab(bool isDesktop) {
    return Column(
      children: [
        // Header with search bar for products
        Container(
          padding: EdgeInsets.fromLTRB(isDesktop ? 32 : 24, isDesktop ? 24 : 16,
              isDesktop ? 32 : 24, isDesktop ? 24 : 16),
          decoration: const BoxDecoration(
            color: AppColors.backgroundLight,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(bottom: isDesktop ? 24 : 16),
                child: Text(
                  'Browse health products and supplements',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: isDesktop ? 16 : 14,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.grey.withAlpha(40),
                    width: 1,
                  ),
                ),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search products...',
                    hintStyle: TextStyle(
                      color: Colors.grey[400],
                      fontSize: isDesktop ? 16 : 14,
                    ),
                    prefixIcon: Icon(
                      Icons.search_rounded,
                      color: GradientColors.primaryGradient[0],
                      size: isDesktop ? 22 : 20,
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                        vertical: isDesktop ? 16 : 14,
                        horizontal: isDesktop ? 20 : 16),
                  ),
                  onChanged: (value) {
                    _loadProducts();
                  },
                ),
              ),
            ],
          ),
        ),

        // Category chips
        if (_categories.isNotEmpty)
          Container(
            height: isDesktop ? 50 : 44,
            margin: EdgeInsets.only(top: isDesktop ? 12 : 8),
            padding: EdgeInsets.symmetric(horizontal: isDesktop ? 32 : 24),
            color: AppColors.backgroundLight,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildCategoryChip('all', 'All Products', '🏪'),
                SizedBox(width: isDesktop ? 14 : 10),
                ..._categories.map((category) {
                  return Padding(
                    padding: EdgeInsets.only(right: isDesktop ? 14 : 10),
                    child: _buildCategoryChip(
                      category.id.toString(),
                      category.name,
                      category.icon,
                    ),
                  );
                }).toList(),
              ],
            ),
          ),

        // Products grid
        Expanded(
          child: Container(
            color: AppColors.backgroundLight,
            child: RefreshIndicator(
              onRefresh: _loadProducts,
              color: GradientColors.primaryGradient[0],
              child: _products.isEmpty && _productsLoading
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              GradientColors.primaryGradient[0],
                            ),
                          ),
                          SizedBox(height: isDesktop ? 24 : 16),
                          Text(
                            'Loading products...',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: isDesktop ? 16 : 14,
                            ),
                          ),
                        ],
                      ),
                    )
                  : _products.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.shopping_bag_outlined,
                                size: isDesktop ? 64 : 48,
                                color: Colors.grey[400],
                              ),
                              SizedBox(height: isDesktop ? 24 : 16),
                              Text(
                                'No products found',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: isDesktop ? 22 : 18,
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? Colors.white
                                      : Colors.black87,
                                ),
                              ),
                              SizedBox(height: isDesktop ? 12 : 8),
                              Text(
                                'Try adjusting your search or category filters',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: isDesktop ? 16 : 14,
                                ),
                              ),
                            ],
                          ),
                        )
                      : GridView.builder(
                          padding: EdgeInsets.all(isDesktop ? 32 : 16),
                          physics: const BouncingScrollPhysics(),
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: isDesktop ? 3 : 2,
                            childAspectRatio: 0.75,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                          ),
                          itemCount: _products.length,
                          itemBuilder: (context, index) {
                            return _buildProductCard(_products[index]);
                          },
                        ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryChip(
      String categoryId, String categoryName, String? icon) {
    final isSelected = _selectedCategory == categoryId;

    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Text(icon, style: const TextStyle(fontSize: 16)),
            const SizedBox(width: 4),
          ],
          Text(categoryName),
        ],
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedCategory = categoryId;
        });
        _loadProducts();
      },
      selectedColor: AppColors.tealSurge.withOpacity(0.2),
      checkmarkColor: AppColors.tealSurge,
    );
  }
}
