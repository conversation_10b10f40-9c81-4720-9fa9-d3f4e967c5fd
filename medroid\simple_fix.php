<?php

/**
 * Simple fix script - Auto-verify all pending providers (no verified_at dependency)
 * Usage: php simple_fix.php
 */

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Provider;

echo "🚀 SIMPLE FIX: Auto-verifying all pending providers...\n\n";

// Get pending providers
$pendingProviders = Provider::where('verification_status', 'pending')->with('user')->get();

if ($pendingProviders->count() == 0) {
    echo "✅ No pending providers found. All providers are already verified!\n";
    
    // Show current status
    $totalProviders = Provider::count();
    $verifiedProviders = Provider::where('verification_status', 'verified')->count();
    echo "\nCurrent status:\n";
    echo "├─ Total providers: {$totalProviders}\n";
    echo "└─ Verified providers: {$verifiedProviders}\n\n";
    
    if ($verifiedProviders > 0) {
        echo "🎉 Appointment slots should be working!\n";
    } else {
        echo "⚠️  No verified providers found. This may be the issue.\n";
    }
    
    exit(0);
}

echo "Found {$pendingProviders->count()} pending providers:\n";
foreach ($pendingProviders as $provider) {
    $userName = $provider->user ? $provider->user->name : 'Provider ' . $provider->id;
    $userEmail = $provider->user ? $provider->user->email : 'No email';
    echo "├─ {$userName} ({$userEmail})\n";
}

echo "\nVerifying providers...\n\n";

$verifiedCount = 0;
$errorCount = 0;

foreach ($pendingProviders as $provider) {
    try {
        // Simple update - only change verification_status
        $provider->update([
            'verification_status' => 'verified',
            'rejection_reason' => null
        ]);
        
        $userName = $provider->user ? $provider->user->name : 'Provider ' . $provider->id;
        echo "✅ Verified: {$userName}\n";
        $verifiedCount++;
        
    } catch (Exception $e) {
        $userName = $provider->user ? $provider->user->name : 'Provider ' . $provider->id;
        echo "❌ Error verifying {$userName}: " . $e->getMessage() . "\n";
        $errorCount++;
    }
}

echo "\n=== RESULTS ===\n";
echo "✅ Successfully verified: {$verifiedCount} providers\n";

if ($errorCount > 0) {
    echo "❌ Errors: {$errorCount} providers\n";
}

// Show final status
$totalProviders = Provider::count();
$finalVerifiedProviders = Provider::where('verification_status', 'verified')->count();
$finalPendingProviders = Provider::where('verification_status', 'pending')->count();

echo "\nFinal status:\n";
echo "├─ Total providers: {$totalProviders}\n";
echo "├─ Verified providers: {$finalVerifiedProviders}\n";
echo "└─ Pending providers: {$finalPendingProviders}\n\n";

if ($finalVerifiedProviders > 0) {
    echo "🎉 SUCCESS: Appointment slots should now be working!\n";
    echo "💡 Test the chat appointment booking feature to confirm.\n";
} else {
    echo "⚠️  WARNING: Still no verified providers. There may be other issues.\n";
}

echo "\n=== SCRIPT COMPLETE ===\n";
