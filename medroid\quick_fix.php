<?php

/**
 * Quick fix script - Auto-verify all pending providers
 * Usage: php quick_fix.php
 */

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Provider;
use Carbon\Carbon;
use Illuminate\Support\Facades\Schema;

echo "🚀 QUICK FIX: Auto-verifying all pending providers...\n\n";

// Step 1: Check if verified_at column exists, if not run migration
echo "🔍 Checking database schema...\n";
if (!Schema::hasColumn('providers', 'verified_at')) {
    echo "⚠️  verified_at column missing. Running migration...\n";

    try {
        \Artisan::call('migrate', ['--force' => true]);
        echo "✅ Migration completed successfully!\n\n";
    } catch (Exception $e) {
        echo "❌ Migration failed: " . $e->getMessage() . "\n";
        echo "💡 Please run manually: php artisan migrate\n\n";
    }
} else {
    echo "✅ Database schema is up to date.\n\n";
}

$pendingProviders = Provider::where('verification_status', 'pending')->get();

if ($pendingProviders->count() == 0) {
    echo "✅ No pending providers found. All providers are already verified!\n";
    exit(0);
}

echo "Found {$pendingProviders->count()} pending providers. Verifying...\n\n";

$verifiedCount = 0;
foreach ($pendingProviders as $provider) {
    try {
        // Check if verified_at column exists before trying to set it
        $updateData = [
            'verification_status' => 'verified',
            'rejection_reason' => null
        ];

        if (Schema::hasColumn('providers', 'verified_at')) {
            $updateData['verified_at'] = Carbon::now();
        }

        $provider->update($updateData);

        $userName = $provider->user ? $provider->user->name : 'Provider ' . $provider->id;
        echo "✅ {$userName}\n";
        $verifiedCount++;

    } catch (Exception $e) {
        echo "❌ Error with provider {$provider->id}: " . $e->getMessage() . "\n";
    }
}

echo "\n🎉 SUCCESS: Verified {$verifiedCount} providers!\n";
echo "💡 Appointment slots should now be available in chat.\n";
