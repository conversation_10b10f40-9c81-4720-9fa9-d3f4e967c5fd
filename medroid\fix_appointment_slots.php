<?php

/**
 * Comprehensive script to diagnose and fix appointment slots issues
 * This script will:
 * 1. Check provider verification status
 * 2. Auto-verify providers if needed
 * 3. Test appointment slots functionality
 * 4. Provide detailed diagnostics
 * 
 * Usage: php fix_appointment_slots.php [--fix] [--force]
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel application
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Provider;
use App\Models\User;
use App\Models\Service;
use Carbon\Carbon;

// Parse command line arguments
$fix = in_array('--fix', $argv);
$force = in_array('--force', $argv);

echo "=== MEDROID APPOINTMENT SLOTS DIAGNOSTIC & FIX TOOL ===\n\n";

if ($fix) {
    echo "🔧 FIX MODE: Will attempt to fix issues found\n";
    if ($force) {
        echo "⚡ FORCE MODE: Will apply fixes without confirmation\n";
    }
} else {
    echo "🔍 DIAGNOSTIC MODE: Will only analyze issues (use --fix to apply fixes)\n";
}

echo "\n";

// Step 1: Basic provider statistics
echo "=== STEP 1: PROVIDER ANALYSIS ===\n";
$totalProviders = Provider::count();
$verifiedProviders = Provider::where('verification_status', 'verified')->count();
$pendingProviders = Provider::where('verification_status', 'pending')->count();
$rejectedProviders = Provider::where('verification_status', 'rejected')->count();

echo "Total Providers: {$totalProviders}\n";
echo "├─ Verified: {$verifiedProviders}\n";
echo "├─ Pending: {$pendingProviders}\n";
echo "└─ Rejected: {$rejectedProviders}\n\n";

// Step 2: Identify the main issue
echo "=== STEP 2: ISSUE IDENTIFICATION ===\n";
$issues = [];

if ($totalProviders == 0) {
    $issues[] = "CRITICAL: No providers found in database";
    echo "❌ CRITICAL: No providers found in database\n";
} else {
    echo "✅ Providers exist in database\n";
}

if ($verifiedProviders == 0) {
    $issues[] = "CRITICAL: No verified providers (appointment slots require verified providers)";
    echo "❌ CRITICAL: No verified providers found\n";
    echo "   → This is likely why appointment slots are not showing\n";
} else {
    echo "✅ Verified providers exist\n";
}

// Check providers with availability
$providersWithAvailability = 0;
$providers = Provider::where('verification_status', 'verified')->get();
foreach ($providers as $provider) {
    $weeklyAvailability = $provider->getAvailabilityData();
    $hasAvailability = false;
    foreach ($weeklyAvailability as $day) {
        if (!empty($day['slots'])) {
            $hasAvailability = true;
            break;
        }
    }
    if ($hasAvailability) {
        $providersWithAvailability++;
    }
}

if ($verifiedProviders > 0 && $providersWithAvailability == 0) {
    $issues[] = "WARNING: Verified providers have no availability set";
    echo "⚠️  WARNING: Verified providers have no availability set\n";
} elseif ($providersWithAvailability > 0) {
    echo "✅ {$providersWithAvailability} verified providers have availability\n";
}

echo "\n";

// Step 3: Test appointment slots API
echo "=== STEP 3: APPOINTMENT SLOTS TEST ===\n";
echo "Testing appointment slots for next 3 days...\n\n";

$totalSlotsFound = 0;
for ($i = 0; $i < 3; $i++) {
    $date = Carbon::today()->addDays($i)->format('Y-m-d');
    $dayName = Carbon::today()->addDays($i)->format('l');
    
    $verifiedProvidersForSlots = Provider::where('verification_status', 'verified')->get();
    $slotsForDay = 0;
    
    foreach ($verifiedProvidersForSlots as $provider) {
        $slots = $provider->getAvailableTimeSlots($date);
        $slotsForDay += count($slots);
    }
    
    $totalSlotsFound += $slotsForDay;
    echo "{$dayName} ({$date}): {$slotsForDay} slots\n";
}

echo "\nTotal slots found: {$totalSlotsFound}\n";

if ($totalSlotsFound == 0) {
    $issues[] = "CRITICAL: No appointment slots available";
    echo "❌ CRITICAL: No appointment slots available\n";
} else {
    echo "✅ Appointment slots are available\n";
}

echo "\n";

// Step 4: Show detailed provider info if issues exist
if (!empty($issues)) {
    echo "=== STEP 4: DETAILED PROVIDER INFORMATION ===\n";
    $allProviders = Provider::with('user')->get();
    
    foreach ($allProviders as $provider) {
        $userName = $provider->user ? $provider->user->name : 'No User';
        $userEmail = $provider->user ? $provider->user->email : 'No Email';
        
        echo "Provider ID: {$provider->id}\n";
        echo "├─ Name: {$userName}\n";
        echo "├─ Email: {$userEmail}\n";
        echo "├─ Status: {$provider->verification_status}\n";
        echo "├─ Verified At: " . ($provider->verified_at ? $provider->verified_at->format('Y-m-d H:i:s') : 'Not set') . "\n";
        echo "├─ Specialization: " . ($provider->specialization ?: 'Not set') . "\n";
        echo "├─ License: " . ($provider->license_number ?: 'Not set') . "\n";
        
        // Check availability
        $weeklyAvailability = $provider->getAvailabilityData();
        $hasAvailability = false;
        $totalSlots = 0;
        foreach ($weeklyAvailability as $day) {
            if (!empty($day['slots'])) {
                $hasAvailability = true;
                $totalSlots += count($day['slots']);
            }
        }
        echo "├─ Has Availability: " . ($hasAvailability ? "Yes ({$totalSlots} slots)" : 'No') . "\n";
        echo "└─ Created: " . $provider->created_at->format('Y-m-d H:i:s') . "\n\n";
    }
}

// Step 5: Apply fixes if requested
if ($fix && !empty($issues)) {
    echo "=== STEP 5: APPLYING FIXES ===\n";
    
    // Fix 1: Verify pending providers
    if ($pendingProviders > 0) {
        echo "🔧 Fix 1: Auto-verifying pending providers...\n";
        
        if (!$force) {
            echo "This will verify {$pendingProviders} pending providers. Continue? (y/N): ";
            $handle = fopen("php://stdin", "r");
            $line = fgets($handle);
            fclose($handle);
            
            if (trim(strtolower($line)) !== 'y') {
                echo "Skipping provider verification.\n\n";
                goto skipVerification;
            }
        }
        
        $pendingProvidersList = Provider::where('verification_status', 'pending')->get();
        $verifiedCount = 0;
        
        foreach ($pendingProvidersList as $provider) {
            try {
                $provider->update([
                    'verification_status' => 'verified',
                    'verified_at' => Carbon::now(),
                    'rejection_reason' => null
                ]);
                
                $userName = $provider->user ? $provider->user->name : 'Provider ' . $provider->id;
                echo "  ✅ Verified: {$userName}\n";
                $verifiedCount++;
                
            } catch (Exception $e) {
                $userName = $provider->user ? $provider->user->name : 'Provider ' . $provider->id;
                echo "  ❌ Error: {$userName} - " . $e->getMessage() . "\n";
            }
        }
        
        echo "Verified {$verifiedCount} providers.\n\n";
        
        skipVerification:
    }
    
    // Re-test after fixes
    echo "🧪 Re-testing appointment slots after fixes...\n";
    $newTotalSlots = 0;
    $newVerifiedProviders = Provider::where('verification_status', 'verified')->count();
    
    for ($i = 0; $i < 3; $i++) {
        $date = Carbon::today()->addDays($i)->format('Y-m-d');
        $verifiedProvidersForSlots = Provider::where('verification_status', 'verified')->get();
        
        foreach ($verifiedProvidersForSlots as $provider) {
            $slots = $provider->getAvailableTimeSlots($date);
            $newTotalSlots += count($slots);
        }
    }
    
    echo "After fixes:\n";
    echo "├─ Verified providers: {$newVerifiedProviders}\n";
    echo "└─ Total slots (3 days): {$newTotalSlots}\n\n";
    
    if ($newTotalSlots > 0) {
        echo "🎉 SUCCESS: Appointment slots should now be working!\n";
        echo "💡 Test the chat appointment booking feature to confirm.\n";
    } else {
        echo "⚠️  Slots still not available. Additional issues may exist:\n";
        echo "   - Providers may need to set their availability\n";
        echo "   - Providers may need active services\n";
        echo "   - Check provider weekly_availability data\n";
    }
}

echo "\n=== SUMMARY ===\n";
if (empty($issues)) {
    echo "✅ No critical issues found. Appointment slots should be working.\n";
} else {
    echo "Issues found:\n";
    foreach ($issues as $issue) {
        echo "❌ {$issue}\n";
    }
    
    if (!$fix) {
        echo "\n💡 Run with --fix flag to automatically resolve issues:\n";
        echo "   php fix_appointment_slots.php --fix\n";
    }
}

echo "\n=== SCRIPT COMPLETE ===\n";
