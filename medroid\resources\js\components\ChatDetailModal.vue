<template>
    <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <!-- Header -->
                <div class="bg-white dark:bg-gray-800 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                Chat Details
                            </h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400" v-if="chat">
                                {{ chat.title || 'Untitled Chat' }} - {{ formatDate(chat.created_at) }}
                            </p>
                        </div>
                        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>

                <!-- Content -->
                <div class="bg-white dark:bg-gray-800 px-6 py-4 max-h-96 overflow-y-auto" v-if="chat">
                    <!-- Chat Info -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Participants</h4>
                            <div class="space-y-2">
                                <!-- Chat Type -->
                                <div class="flex items-center mb-3">
                                    <span v-if="chat.type === 'ai_chat'" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 mr-2">
                                        <i class="fas fa-robot mr-1"></i>
                                        AI Chat
                                    </span>
                                    <span v-else class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mr-2">
                                        <i class="fas fa-users mr-1"></i>
                                        User Chat
                                    </span>
                                </div>

                                <!-- Patient -->
                                <div v-if="chat.patient" class="flex items-center">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mr-2">
                                        Patient
                                    </span>
                                    <span class="text-sm text-gray-900 dark:text-gray-100">
                                        {{ chat.patient.user?.name || 'Unknown' }}
                                    </span>
                                    <span class="text-xs text-gray-500 dark:text-gray-400 ml-2">
                                        ({{ chat.patient.user?.email || 'No email' }})
                                    </span>
                                </div>

                                <!-- Provider or AI -->
                                <div class="flex items-center">
                                    <span v-if="chat.type === 'ai_chat'" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 mr-2">
                                        AI Assistant
                                    </span>
                                    <span v-else-if="chat.provider" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 mr-2">
                                        Provider
                                    </span>

                                    <span v-if="chat.type === 'ai_chat'" class="text-sm text-gray-900 dark:text-gray-100">
                                        AI Clinical Assistant
                                    </span>
                                    <span v-else-if="chat.provider" class="text-sm text-gray-900 dark:text-gray-100">
                                        {{ chat.provider.user?.name || 'Unknown' }}
                                    </span>

                                    <span v-if="chat.provider" class="text-xs text-gray-500 dark:text-gray-400 ml-2">
                                        ({{ chat.provider.user?.email || 'No email' }})
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Status & Flags</h4>
                            <div class="space-y-2">
                                <!-- Status -->
                                <div class="flex items-center">
                                    <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">Status:</span>
                                    <span v-if="chat.type === 'ai_chat'" :class="chat.escalated ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                        {{ chat.escalated ? 'Escalated' : 'Active' }}
                                    </span>
                                    <span v-else :class="getStatusBadgeClass(chat.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                        {{ chat.status }}
                                    </span>
                                </div>

                                <!-- Flagged/Escalated -->
                                <div class="flex items-center" v-if="chat.is_flagged || (chat.type === 'ai_chat' && chat.escalated)">
                                    <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">{{ chat.type === 'ai_chat' ? 'Escalated:' : 'Flagged:' }}</span>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                        {{ chat.flag_reason || chat.escalation_reason || 'No reason provided' }}
                                    </span>
                                </div>

                                <!-- Archived -->
                                <div class="flex items-center" v-if="chat.is_archived">
                                    <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">Archived:</span>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                        Yes
                                    </span>
                                </div>

                                <!-- AI Chat specific flags -->
                                <div v-if="chat.type === 'ai_chat'" class="space-y-1">
                                    <div class="flex items-center" v-if="chat.is_anonymous">
                                        <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">Anonymous:</span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                            Yes
                                        </span>
                                    </div>
                                    <div class="flex items-center" v-if="chat.is_public">
                                        <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">Public:</span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                            Yes
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Messages -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">Messages ({{ messages.length }})</h4>
                        <div class="space-y-4 max-h-64 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <div v-for="(message, index) in messages" :key="message.id || index" class="border-b border-gray-100 dark:border-gray-700 pb-3 last:border-b-0">
                                <div class="flex items-start justify-between mb-2">
                                    <div class="flex items-center">
                                        <span v-if="chat.type === 'ai_chat'" :class="getSenderBadgeClassAI(message.role)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mr-2">
                                            {{ message.role === 'user' ? 'Patient' : 'AI Assistant' }}
                                        </span>
                                        <span v-else :class="getSenderBadgeClass(message.sender_type)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mr-2">
                                            {{ message.sender_type }}
                                        </span>
                                        <span class="text-xs text-gray-500 dark:text-gray-400">
                                            {{ formatDate(message.created_at || message.timestamp) }}
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span v-if="message.is_system_message" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                            System
                                        </span>
                                        <span v-if="message.is_read" class="text-xs text-green-500">
                                            <i class="fas fa-check-double"></i>
                                        </span>
                                        <span v-if="message.isTruncated" class="text-xs text-yellow-500" title="Message was truncated">
                                            <i class="fas fa-cut"></i>
                                        </span>
                                    </div>
                                </div>
                                <p class="text-sm text-gray-900 dark:text-gray-100">{{ message.content }}</p>

                                <!-- AI Chat specific metadata -->
                                <div v-if="chat.type === 'ai_chat' && message.metadata" class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                                    <div v-if="message.metadata.confidence" class="flex items-center">
                                        <span class="mr-2">Confidence:</span>
                                        <div class="w-16 bg-gray-200 rounded-full h-1.5 dark:bg-gray-700">
                                            <div class="bg-blue-600 h-1.5 rounded-full" :style="`width: ${message.metadata.confidence * 100}%`"></div>
                                        </div>
                                        <span class="ml-2">{{ Math.round(message.metadata.confidence * 100) }}%</span>
                                    </div>
                                </div>
                            </div>
                            <div v-if="messages.length === 0" class="text-center text-gray-500 dark:text-gray-400 py-8">
                                No messages in this chat
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-gray-50 dark:bg-gray-700 px-6 py-4 flex items-center justify-between">
                    <div class="flex space-x-3">
                        <!-- Actions for User Chats -->
                        <template v-if="chat?.type === 'user_chat'">
                            <button v-if="!chat?.is_flagged" @click="flagChat" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                <i class="fas fa-flag mr-2"></i>
                                Flag Chat
                            </button>
                            <button v-else @click="unflagChat" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                <i class="fas fa-flag mr-2"></i>
                                Remove Flag
                            </button>

                            <button v-if="!chat?.is_archived" @click="archiveChat" class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class="fas fa-archive mr-2"></i>
                                Archive
                            </button>
                        </template>

                        <!-- Actions for AI Chats -->
                        <template v-else-if="chat?.type === 'ai_chat'">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                AI chats are managed automatically.
                                <span v-if="chat.escalated" class="text-red-600 dark:text-red-400">This conversation was escalated for human review.</span>
                            </div>
                        </template>
                    </div>

                    <button @click="$emit('close')" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
    show: Boolean,
    chatId: [String, Number],
});

const emit = defineEmits(['close', 'updated']);

const chat = ref(null);
const messages = ref([]);
const loading = ref(false);

// Watch for chat ID changes
watch(() => props.chatId, (newChatId) => {
    if (newChatId && props.show) {
        fetchChatDetails();
    }
});

// Watch for show changes
watch(() => props.show, (newShow) => {
    if (newShow && props.chatId) {
        fetchChatDetails();
    }
});

const fetchChatDetails = async () => {
    if (!props.chatId) return;

    loading.value = true;
    try {
        // First get chat details to determine type
        const chatResponse = await window.axios.get(`/chats-detail/${props.chatId}`);
        chat.value = chatResponse.data;

        // For AI chats, messages are embedded in the chat object
        if (chat.value.type === 'ai_chat') {
            messages.value = chat.value.messages || [];
        } else {
            // For user chats, fetch messages separately
            const messagesResponse = await window.axios.get(`/chats-messages/${props.chatId}`);
            messages.value = messagesResponse.data.messages || [];
        }
    } catch (error) {
        console.error('Error fetching chat details:', error);
    } finally {
        loading.value = false;
    }
};

const flagChat = async () => {
    const reason = prompt('Please provide a reason for flagging this chat:');
    if (!reason) return;
    
    try {
        await window.axios.post(`/chats-flag/${props.chatId}`, { reason });
        await fetchChatDetails();
        emit('updated');
    } catch (error) {
        console.error('Error flagging chat:', error);
        alert('Failed to flag chat');
    }
};

const unflagChat = async () => {
    try {
        await window.axios.post(`/chats-unflag/${props.chatId}`);
        await fetchChatDetails();
        emit('updated');
    } catch (error) {
        console.error('Error unflagging chat:', error);
        alert('Failed to remove flag');
    }
};

const archiveChat = async () => {
    if (!confirm('Are you sure you want to archive this chat?')) return;
    
    try {
        await window.axios.post(`/chats-archive/${props.chatId}`);
        await fetchChatDetails();
        emit('updated');
    } catch (error) {
        console.error('Error archiving chat:', error);
        alert('Failed to archive chat');
    }
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
};

const getStatusBadgeClass = (status) => {
    const classes = {
        'active': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        'inactive': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
        'ended': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    };
    return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
};

const getSenderBadgeClass = (senderType) => {
    const classes = {
        'patient': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
        'provider': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        'admin': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
        'system': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
    };
    return classes[senderType] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
};

const getSenderBadgeClassAI = (role) => {
    const classes = {
        'user': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
        'assistant': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
        'system': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
    };
    return classes[role] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
};
</script>
