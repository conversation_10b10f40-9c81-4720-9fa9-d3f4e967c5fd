{"hash": "034e0f93", "configHash": "0328908c", "lockfileHash": "619b70f4", "browserHash": "3cac3ad3", "optimized": {"@inertiajs/core": {"src": "../../@inertiajs/core/dist/index.esm.js", "file": "@inertiajs_core.js", "fileHash": "e0ba09b7", "needsInterop": false}, "@inertiajs/vue3": {"src": "../../@inertiajs/vue3/dist/index.esm.js", "file": "@inertiajs_vue3.js", "fileHash": "b28b9b4f", "needsInterop": false}, "@vueuse/core": {"src": "../../@vueuse/core/index.mjs", "file": "@vueuse_core.js", "fileHash": "656fb2c3", "needsInterop": false}, "agora-rtc-sdk-ng": {"src": "../../agora-rtc-sdk-ng/AgoraRTC_N-production.js", "file": "agora-rtc-sdk-ng.js", "fileHash": "dc97f8f0", "needsInterop": true}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "4574a2ba", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "e55f40e3", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "d7ce1937", "needsInterop": false}, "laravel-vite-plugin/inertia-helpers": {"src": "../../laravel-vite-plugin/inertia-helpers/index.js", "file": "laravel-vite-plugin_inertia-helpers.js", "fileHash": "f46c3e79", "needsInterop": false}, "lodash": {"src": "../../lodash/lodash.js", "file": "lodash.js", "fileHash": "bfe44da4", "needsInterop": true}, "lucide-vue-next": {"src": "../../lucide-vue-next/dist/esm/lucide-vue-next.js", "file": "lucide-vue-next.js", "fileHash": "0a837cbc", "needsInterop": false}, "qrcode": {"src": "../../qrcode/lib/browser.js", "file": "qrcode.js", "fileHash": "a7152543", "needsInterop": true}, "reka-ui": {"src": "../../reka-ui/dist/index.js", "file": "reka-ui.js", "fileHash": "537cc120", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "be3c5b33", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "93728759", "needsInterop": false}}, "chunks": {"chunk-PXYDA7QB": {"file": "chunk-PXYDA7QB.js"}, "chunk-YFHHV7KE": {"file": "chunk-YFHHV7KE.js"}, "chunk-SLCLIWM7": {"file": "chunk-SLCLIWM7.js"}, "chunk-U3LI7FBV": {"file": "chunk-U3LI7FBV.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-YHHEEY6D": {"file": "chunk-YHHEEY6D.js"}}}