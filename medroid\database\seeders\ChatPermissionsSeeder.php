<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\User;

class ChatPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeder.
     *
     * @return void
     */
    public function run()
    {
        // Create chat permissions if they don't exist
        $chatPermissions = [
            'view chats',
            'create chats',
            'edit chats',
            'delete chats',
        ];

        foreach ($chatPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign chat permissions to admin role
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->givePermissionTo($chatPermissions);

        // Assign view and edit chat permissions to manager role
        $managerRole = Role::firstOrCreate(['name' => 'manager']);
        $managerRole->givePermissionTo(['view chats', 'edit chats']);

        // Assign view and create chat permissions to provider role
        $providerRole = Role::firstOrCreate(['name' => 'provider']);
        $providerRole->givePermissionTo(['view chats', 'create chats']);

        // Assign view and create chat permissions to patient role
        $patientRole = Role::firstOrCreate(['name' => 'patient']);
        $patientRole->givePermissionTo(['view chats', 'create chats']);

        // Assign admin role to all admin users
        $adminUsers = User::where('role', 'admin')->get();
        foreach ($adminUsers as $user) {
            $user->assignRole('admin');
        }

        // Assign manager role to all manager users
        $managerUsers = User::where('role', 'manager')->get();
        foreach ($managerUsers as $user) {
            $user->assignRole('manager');
        }

        // Assign provider role to all provider users
        $providerUsers = User::where('role', 'provider')->get();
        foreach ($providerUsers as $user) {
            $user->assignRole('provider');
        }

        // Assign patient role to all patient users
        $patientUsers = User::where('role', 'patient')->get();
        foreach ($patientUsers as $user) {
            $user->assignRole('patient');
        }

        $this->command->info('Chat permissions seeded successfully!');
    }
}
