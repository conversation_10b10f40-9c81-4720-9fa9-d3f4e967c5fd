<?php

namespace App\Http\Controllers;

use App\Models\DeviceToken;
use App\Models\UserSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DeviceAnalyticsController extends Controller
{
    /**
     * Get device analytics summary.
     */
    public function getDeviceAnalytics()
    {
        $deviceStats = DeviceToken::select('device_type', DB::raw('count(*) as count'))
            ->groupBy('device_type')
            ->get()
            ->pluck('count', 'device_type');

        $browserStats = DeviceToken::select('browser', DB::raw('count(*) as count'))
            ->whereNotNull('browser')
            ->groupBy('browser')
            ->get()
            ->pluck('count', 'browser');

        $platformStats = DeviceToken::select('platform', DB::raw('count(*) as count'))
            ->whereNotNull('platform')
            ->groupBy('platform')
            ->get()
            ->pluck('count', 'platform');

        $activeSessionsCount = UserSession::active()->recent()->count();
        $totalDevicesCount = DeviceToken::count();

        return response()->json([
            'device_types' => $deviceStats,
            'browsers' => $browserStats,
            'platforms' => $platformStats,
            'active_sessions' => $activeSessionsCount,
            'total_devices' => $totalDevicesCount,
        ]);
    }

    /**
     * Get recent user sessions.
     */
    public function getRecentSessions(Request $request)
    {
        $limit = $request->get('limit', 50);
        
        $sessions = UserSession::with('user:id,name,email')
            ->orderBy('last_activity', 'desc')
            ->limit($limit)
            ->get();

        return response()->json($sessions);
    }

    /**
     * Get device tokens with user information.
     */
    public function getDeviceTokens(Request $request)
    {
        $query = DeviceToken::with('user:id,name,email');

        if ($request->has('device_type')) {
            $query->where('device_type', $request->device_type);
        }

        if ($request->has('browser')) {
            $query->where('browser', $request->browser);
        }

        if ($request->has('platform')) {
            $query->where('platform', $request->platform);
        }

        $tokens = $query->orderBy('last_used_at', 'desc')
            ->paginate($request->get('per_page', 20));

        return response()->json($tokens);
    }
}
