<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Get all notifications for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $limit = $request->input('limit', 20);
        $offset = $request->input('offset', 0);

        $notifications = $this->notificationService->getUserNotifications(
            $request->user(),
            $limit,
            $offset
        );

        return response()->json([
            'notifications' => $notifications,
            'unread_count' => $this->notificationService->getUnreadCount($request->user()),
        ]);
    }

    /**
     * Get unread notification count for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function unreadCount(Request $request)
    {
        $count = $this->notificationService->getUnreadCount($request->user());

        return response()->json([
            'unread_count' => $count,
        ]);
    }

    /**
     * Mark a notification as read.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead(Request $request, $id)
    {
        $success = $this->notificationService->markAsRead($id, $request->user());

        if (!$success) {
            return response()->json([
                'message' => 'Notification not found',
            ], 404);
        }

        return response()->json([
            'message' => 'Notification marked as read',
            'unread_count' => $this->notificationService->getUnreadCount($request->user()),
        ]);
    }

    /**
     * Mark all notifications as read for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAllAsRead(Request $request)
    {
        $this->notificationService->markAllAsRead($request->user());

        return response()->json([
            'message' => 'All notifications marked as read',
            'unread_count' => 0,
        ]);
    }

    /**
     * Store a device token for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeDeviceToken(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required|string',
            'device_type' => 'required|in:android,ios,web',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $userAgentService = app(\App\Services\UserAgentService::class);
        $deviceInfo = $userAgentService->parseUserAgent($request);

        $deviceToken = $this->notificationService->storeDeviceToken(
            $request->user(),
            $request->token,
            $request->device_type,
            $deviceInfo
        );

        return response()->json([
            'message' => 'Device token stored successfully',
            'device_token' => $deviceToken,
        ]);
    }

    /**
     * Update a device token for the authenticated user.
     * This is useful when Firebase refreshes the token.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateDeviceToken(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'old_token' => 'required|string',
            'new_token' => 'required|string',
            'device_type' => 'required|in:android,ios,web',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Update the token using the service
        $updatedToken = $this->notificationService->updateDeviceToken(
            $request->old_token,
            $request->new_token,
            $request->device_type
        );

        if (!$updatedToken) {
            return response()->json([
                'message' => 'Old token not found',
            ], 404);
        }

        return response()->json([
            'message' => 'Device token updated successfully',
            'device_token' => $updatedToken,
        ]);
    }

    /**
     * Send a test notification to a user.
     * This endpoint is for testing purposes only.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendTestNotification(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'title' => 'required|string|max:255',
            'body' => 'required|string',
            'type' => 'required|string|max:50',
            'data' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = User::find($request->user_id);
        $success = $this->notificationService->sendPushNotification(
            $user,
            $request->title,
            $request->body,
            $request->type,
            $request->data ?? []
        );

        if ($success) {
            return response()->json([
                'message' => 'Test notification sent successfully',
            ]);
        } else {
            return response()->json([
                'message' => 'Failed to send test notification. Check logs for details.',
            ], 500);
        }
    }
}
